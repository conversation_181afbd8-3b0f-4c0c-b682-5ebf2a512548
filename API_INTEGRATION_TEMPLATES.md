# Light Year Admin V5 - API对接标准模板

## 通用API响应格式

### 标准响应结构
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "pagination": {
    "page": 1,
    "size": 10,
    "total": 100,
    "pages": 10
  }
}
```

### 错误响应结构
```json
{
  "code": 400,
  "message": "参数错误",
  "errors": {
    "field_name": ["错误信息1", "错误信息2"]
  }
}
```

## 标准CRUD操作模板

### 1. 列表页面 (GET /api/resources)
```javascript
// 加载数据列表
function loadResourceList(page = 1, size = 10) {
  const params = {
    page: page,
    size: size,
    keyword: $('#search-keyword').val(),
    status: $('#search-status').val(),
    category_id: $('#search-category').val(),
    start_date: $('#start-date').val(),
    end_date: $('#end-date').val()
  };
  
  // 移除空值参数
  Object.keys(params).forEach(key => {
    if (params[key] === '' || params[key] === null) {
      delete params[key];
    }
  });
  
  fetchData('/api/resources', params).then(response => {
    if (response.code === 200) {
      renderResourceTable(response.data);
      renderPagination(response.pagination);
    }
  });
}

// 渲染数据表格
function renderResourceTable(resources) {
  const tbody = $('#resource-table tbody');
  tbody.empty();
  
  if (!resources || resources.length === 0) {
    tbody.append(`
      <tr>
        <td colspan="100%" class="text-center text-muted py-4">
          <i class="mdi mdi-inbox-outline fs-1"></i>
          <div>暂无数据</div>
        </td>
      </tr>
    `);
    return;
  }
  
  resources.forEach(resource => {
    const row = `
      <tr>
        <td>
          <input type="checkbox" class="form-check-input" value="${resource.id}">
        </td>
        <td>${resource.id}</td>
        <td>${escapeHtml(resource.name)}</td>
        <td>
          <span class="badge ${resource.status ? 'bg-success' : 'bg-secondary'}">
            ${resource.status ? '启用' : '禁用'}
          </span>
        </td>
        <td>${formatDateTime(resource.created_at)}</td>
        <td>
          <div class="btn-group btn-group-sm">
            <button type="button" class="btn btn-outline-primary" onclick="editResource(${resource.id})">
              <i class="mdi mdi-pencil"></i> 编辑
            </button>
            <button type="button" class="btn btn-outline-danger" onclick="deleteResource(${resource.id})">
              <i class="mdi mdi-delete"></i> 删除
            </button>
          </div>
        </td>
      </tr>
    `;
    tbody.append(row);
  });
}

// 搜索功能
function searchResources() {
  loadResourceList(1, 10);
}

// 重置搜索
function resetSearch() {
  $('#search-form')[0].reset();
  $('.select2').val(null).trigger('change');
  loadResourceList(1, 10);
}
```

### 2. 详情页面 (GET /api/resources/{id})
```javascript
// 获取资源详情
function getResourceDetail(id) {
  fetchData(`/api/resources/${id}`).then(response => {
    if (response.code === 200) {
      renderResourceDetail(response.data);
    }
  });
}

// 渲染详情信息
function renderResourceDetail(resource) {
  $('#resource-id').text(resource.id);
  $('#resource-name').text(resource.name);
  $('#resource-status').html(
    resource.status ? 
    '<span class="badge bg-success">启用</span>' : 
    '<span class="badge bg-secondary">禁用</span>'
  );
  $('#resource-created-at').text(formatDateTime(resource.created_at));
  $('#resource-description').html(resource.description || '暂无描述');
  
  // 如果有关联数据
  if (resource.category) {
    $('#resource-category').text(resource.category.name);
  }
  
  // 如果有图片
  if (resource.images && resource.images.length > 0) {
    renderResourceImages(resource.images);
  }
}
```

### 3. 创建页面 (POST /api/resources)
```javascript
// 初始化创建表单
function initCreateForm() {
  // 初始化表单验证
  $('#resource-form').validate({
    rules: {
      name: { required: true, minlength: 2, maxlength: 100 },
      category_id: { required: true },
      price: { number: true, min: 0 },
      email: { email: true }
    },
    messages: {
      name: {
        required: "请输入资源名称",
        minlength: "名称至少2个字符",
        maxlength: "名称最多100个字符"
      },
      category_id: { required: "请选择分类" },
      price: { number: "请输入有效的价格", min: "价格不能为负数" },
      email: { email: "请输入有效的邮箱地址" }
    },
    submitHandler: function(form) {
      createResource();
    }
  });
  
  // 初始化下拉框
  initCategorySelect();
  
  // 初始化文件上传
  initFileUploader();
}

// 创建资源
function createResource() {
  const formData = new FormData($('#resource-form')[0]);
  
  // 添加额外数据
  formData.append('status', $('#status-switch').is(':checked') ? 1 : 0);
  
  // 添加富文本内容
  formData.append('description', $('#description-editor').summernote('code'));
  
  $.ajax({
    url: '/api/resources',
    method: 'POST',
    data: formData,
    processData: false,
    contentType: false,
    beforeSend: showLoading,
    success: function(response) {
      hideLoading();
      if (response.code === 200) {
        showSuccess('创建成功');
        setTimeout(() => {
          window.location.href = '/admin/resources';
        }, 1500);
      } else {
        showError(response.message);
        // 显示字段错误
        if (response.errors) {
          showFieldErrors(response.errors);
        }
      }
    },
    error: function(xhr) {
      hideLoading();
      handleAjaxError(xhr);
    }
  });
}
```

### 4. 编辑页面 (PUT /api/resources/{id})
```javascript
// 初始化编辑表单
function initEditForm(id) {
  // 加载资源数据
  fetchData(`/api/resources/${id}`).then(response => {
    if (response.code === 200) {
      fillEditForm(response.data);
    }
  });
  
  // 初始化表单验证（同创建表单）
  initFormValidation();
}

// 填充编辑表单
function fillEditForm(resource) {
  $('#resource-id').val(resource.id);
  $('#name').val(resource.name);
  $('#category-select').val(resource.category_id).trigger('change');
  $('#price').val(resource.price);
  $('#status-switch').prop('checked', resource.status);
  
  // 富文本编辑器
  $('#description-editor').summernote('code', resource.description || '');
  
  // 图片预览
  if (resource.images) {
    renderImagePreviews(resource.images);
  }
}

// 更新资源
function updateResource() {
  const id = $('#resource-id').val();
  const formData = new FormData($('#resource-form')[0]);
  
  // 添加PUT方法标识
  formData.append('_method', 'PUT');
  
  $.ajax({
    url: `/api/resources/${id}`,
    method: 'POST', // 使用POST模拟PUT
    data: formData,
    processData: false,
    contentType: false,
    beforeSend: showLoading,
    success: function(response) {
      hideLoading();
      if (response.code === 200) {
        showSuccess('更新成功');
        // 可选择是否跳转
        // window.location.href = '/admin/resources';
      } else {
        showError(response.message);
        if (response.errors) {
          showFieldErrors(response.errors);
        }
      }
    },
    error: function(xhr) {
      hideLoading();
      handleAjaxError(xhr);
    }
  });
}
```

### 5. 删除操作 (DELETE /api/resources/{id})
```javascript
// 删除单个资源
function deleteResource(id) {
  confirmAction('确定要删除这个资源吗？删除后无法恢复。', function() {
    $.ajax({
      url: `/api/resources/${id}`,
      method: 'DELETE',
      beforeSend: showLoading,
      success: function(response) {
        hideLoading();
        if (response.code === 200) {
          showSuccess('删除成功');
          loadResourceList(); // 重新加载列表
        } else {
          showError(response.message);
        }
      },
      error: function(xhr) {
        hideLoading();
        handleAjaxError(xhr);
      }
    });
  });
}

// 批量删除
function batchDeleteResources() {
  const selectedIds = [];
  $('#resource-table tbody input[type="checkbox"]:checked').each(function() {
    selectedIds.push($(this).val());
  });
  
  if (selectedIds.length === 0) {
    showError('请选择要删除的项目');
    return;
  }
  
  confirmAction(`确定要删除选中的 ${selectedIds.length} 个项目吗？`, function() {
    $.ajax({
      url: '/api/resources/batch-delete',
      method: 'POST',
      data: JSON.stringify({ ids: selectedIds }),
      contentType: 'application/json',
      beforeSend: showLoading,
      success: function(response) {
        hideLoading();
        if (response.code === 200) {
          showSuccess(`成功删除 ${selectedIds.length} 个项目`);
          loadResourceList();
          // 取消全选
          $('#select-all').prop('checked', false);
        } else {
          showError(response.message);
        }
      },
      error: function(xhr) {
        hideLoading();
        handleAjaxError(xhr);
      }
    });
  });
}
```

## 状态切换操作模板

```javascript
// 切换资源状态
function toggleResourceStatus(id, currentStatus) {
  const newStatus = currentStatus ? 0 : 1;
  const statusText = newStatus ? '启用' : '禁用';
  
  confirmAction(`确定要${statusText}这个资源吗？`, function() {
    $.ajax({
      url: `/api/resources/${id}/status`,
      method: 'PUT',
      data: JSON.stringify({ status: newStatus }),
      contentType: 'application/json',
      beforeSend: showLoading,
      success: function(response) {
        hideLoading();
        if (response.code === 200) {
          showSuccess(`${statusText}成功`);
          loadResourceList(); // 重新加载列表
        } else {
          showError(response.message);
        }
      },
      error: function(xhr) {
        hideLoading();
        handleAjaxError(xhr);
      }
    });
  });
}

// 批量状态切换
function batchToggleStatus(status) {
  const selectedIds = getSelectedIds();
  if (selectedIds.length === 0) {
    showError('请选择要操作的项目');
    return;
  }
  
  const statusText = status ? '启用' : '禁用';
  confirmAction(`确定要${statusText}选中的 ${selectedIds.length} 个项目吗？`, function() {
    $.ajax({
      url: '/api/resources/batch-status',
      method: 'PUT',
      data: JSON.stringify({ ids: selectedIds, status: status }),
      contentType: 'application/json',
      beforeSend: showLoading,
      success: function(response) {
        hideLoading();
        if (response.code === 200) {
          showSuccess(`批量${statusText}成功`);
          loadResourceList();
        } else {
          showError(response.message);
        }
      },
      error: function(xhr) {
        hideLoading();
        handleAjaxError(xhr);
      }
    });
  });
}
```

## 文件上传API对接

```javascript
// 单文件上传
function uploadSingleFile(file, callback) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', 'image'); // 文件类型标识
  
  $.ajax({
    url: '/api/upload/single',
    method: 'POST',
    data: formData,
    processData: false,
    contentType: false,
    xhr: function() {
      const xhr = new window.XMLHttpRequest();
      // 上传进度
      xhr.upload.addEventListener('progress', function(evt) {
        if (evt.lengthComputable) {
          const percentComplete = evt.loaded / evt.total * 100;
          updateUploadProgress(percentComplete);
        }
      }, false);
      return xhr;
    },
    success: function(response) {
      if (response.code === 200) {
        callback(null, response.data);
      } else {
        callback(response.message);
      }
    },
    error: function(xhr) {
      callback('上传失败');
    }
  });
}

// 多文件上传
function uploadMultipleFiles(files, callback) {
  const formData = new FormData();
  for (let i = 0; i < files.length; i++) {
    formData.append('files[]', files[i]);
  }
  
  $.ajax({
    url: '/api/upload/multiple',
    method: 'POST',
    data: formData,
    processData: false,
    contentType: false,
    success: function(response) {
      if (response.code === 200) {
        callback(null, response.data);
      } else {
        callback(response.message);
      }
    },
    error: function(xhr) {
      callback('上传失败');
    }
  });
}
```

## 导入导出API对接

```javascript
// 数据导出
function exportData(format = 'excel') {
  const params = {
    format: format,
    keyword: $('#search-keyword').val(),
    status: $('#search-status').val()
  };
  
  // 创建下载链接
  const queryString = $.param(params);
  const downloadUrl = `/api/resources/export?${queryString}`;
  
  // 创建隐藏的下载链接
  const link = document.createElement('a');
  link.href = downloadUrl;
  link.download = `resources_${new Date().getTime()}.${format}`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// 数据导入
function importData(file) {
  const formData = new FormData();
  formData.append('file', file);
  
  $.ajax({
    url: '/api/resources/import',
    method: 'POST',
    data: formData,
    processData: false,
    contentType: false,
    beforeSend: showLoading,
    success: function(response) {
      hideLoading();
      if (response.code === 200) {
        showSuccess(`导入成功，共导入 ${response.data.count} 条记录`);
        loadResourceList();
      } else {
        showError(response.message);
        // 显示导入错误详情
        if (response.data && response.data.errors) {
          showImportErrors(response.data.errors);
        }
      }
    },
    error: function(xhr) {
      hideLoading();
      handleAjaxError(xhr);
    }
  });
}
```

这份API对接模板提供了完整的CRUD操作、文件上传、导入导出等常见功能的标准实现，可以作为AI工具生成代码的参考模板。
