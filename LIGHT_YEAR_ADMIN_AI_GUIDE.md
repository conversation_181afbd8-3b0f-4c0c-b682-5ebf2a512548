# Light Year Admin V5 - AI开发指南

## 项目概述
Light Year Admin V5是一个基于Bootstrap 5.1.3的后台管理系统HTML模板，包含41个集成插件和丰富的布局示例。本文档专为AI工具（Cursor、Gemini CLI等）设计，帮助AI高效生成后台管理页面。

## 核心架构

### 技术栈
- **前端框架**: Bootstrap 5.1.3
- **图标库**: Material Design Icons (mdi)
- **JavaScript**: jQuery + 各种专业插件
- **样式**: CSS3 + 自定义主题
- **后端示例**: PHP (可替换为任何后端技术)

### 文件结构
```
├── index.html              # 主框架页面（带导航）
├── lyear_main.html         # 仪表板首页
├── lyear_layout_*.html     # 布局示例页面
├── lyear_js_*.html         # 插件功能页面
├── css/                    # 样式文件
├── js/                     # JavaScript插件库
├── images/                 # 图片资源
└── data/                   # 数据文件和后端示例
```

## 页面开发模式

### 1. 独立页面模式 (推荐用于API对接)
创建独立的功能页面，不依赖主框架导航：

```html
<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>页面标题 - Light Year Admin</title>
<!-- 核心CSS -->
<link rel="stylesheet" href="css/materialdesignicons.min.css">
<link rel="stylesheet" href="css/bootstrap.min.css">
<link rel="stylesheet" href="css/style.min.css">
<!-- 插件CSS -->
<!-- 根据需要引入特定插件的CSS -->
</head>
<body>
<div class="container-fluid">
  <!-- 页面内容 -->
</div>
<!-- 核心JS -->
<script src="js/jquery.min.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="js/main.min.js"></script>
<!-- 插件JS -->
<!-- 根据需要引入特定插件的JS -->
</body>
</html>
```

### 2. 框架集成模式
在主框架内创建标签页内容：

```html
<!-- 参考 lyear_main.html 的结构 -->
<div class="container-fluid">
  <div class="row">
    <!-- 内容区域 -->
  </div>
</div>
```

## 常用组件模板

### 数据统计卡片
```html
<div class="col-md-6 col-xl-3">
  <div class="card bg-primary text-white">
    <div class="card-body">
      <div class="d-flex justify-content-between">
        <span class="avatar-md rounded-circle bg-white bg-opacity-25 avatar-box">
          <i class="mdi mdi-currency-cny fs-4"></i>
        </span>
        <span class="fs-4" id="stat-value">0</span>
      </div>
      <div class="text-end">统计标题</div>
    </div>
  </div>
</div>
```

### 数据表格
```html
<div class="card">
  <header class="card-header">
    <div class="card-title">表格标题</div>
  </header>
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-striped">
        <thead>
          <tr>
            <th>列1</th>
            <th>列2</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody id="data-table-body">
          <!-- 动态内容 -->
        </tbody>
      </table>
    </div>
  </div>
</div>
```

### 表单容器
```html
<div class="card">
  <header class="card-header">
    <div class="card-title">表单标题</div>
  </header>
  <div class="card-body">
    <form id="data-form" method="post">
      <div class="row">
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">字段标签</label>
            <input type="text" class="form-control" name="field_name" required>
          </div>
        </div>
      </div>
      <div class="form-actions">
        <button type="submit" class="btn btn-primary">提交</button>
        <button type="reset" class="btn btn-secondary">重置</button>
      </div>
    </form>
  </div>
</div>
```

## 插件集成指南

### 表单验证 (jQuery Validate)
```html
<!-- CSS -->
<!-- 无需额外CSS -->

<!-- JS -->
<script src="js/jquery-validate/jquery.validate.min.js"></script>
<script src="js/jquery-validate/messages_zh.min.js"></script>

<script>
$('#data-form').validate({
  rules: {
    field_name: {
      required: true,
      minlength: 2
    }
  },
  messages: {
    field_name: {
      required: "请输入字段名称",
      minlength: "至少输入2个字符"
    }
  }
});
</script>
```

### 数据表格 (Bootstrap Table)
```html
<!-- CSS -->
<link rel="stylesheet" href="js/bootstrap-table/bootstrap-table.min.css">

<!-- JS -->
<script src="js/bootstrap-table/bootstrap-table.min.js"></script>
<script src="js/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>

<script>
$('#data-table').bootstrapTable({
  url: '/api/data',
  method: 'get',
  pagination: true,
  search: true,
  columns: [{
    field: 'id',
    title: 'ID'
  }, {
    field: 'name',
    title: '名称'
  }]
});
</script>
```

### 下拉选择 (Select2)
```html
<!-- CSS -->
<link rel="stylesheet" href="js/select2/select2.min.css">

<!-- JS -->
<script src="js/select2/select2.min.js"></script>
<script src="js/select2/i18n/zh-CN.js"></script>

<script>
$('.select2').select2({
  language: 'zh-CN',
  ajax: {
    url: '/api/options',
    dataType: 'json'
  }
});
</script>
```

### 日期时间选择器
```html
<!-- CSS -->
<link rel="stylesheet" href="js/bootstrap-daterangepicker/daterangepicker.min.css">

<!-- JS -->
<script src="js/momentjs/moment.min.js"></script>
<script src="js/bootstrap-daterangepicker/daterangepicker.min.js"></script>

<script>
$('.daterange').daterangepicker({
  locale: {
    format: 'YYYY-MM-DD',
    separator: ' 至 ',
    applyLabel: '确定',
    cancelLabel: '取消'
  }
});
</script>
```

### 文件上传 (WebUploader)
```html
<!-- CSS -->
<link rel="stylesheet" href="js/webuploader/webuploader.css">

<!-- JS -->
<script src="js/webuploader/webuploader.min.js"></script>

<script>
var uploader = WebUploader.create({
  swf: 'js/webuploader/Uploader.swf',
  server: '/api/upload',
  pick: '#file-picker',
  accept: {
    title: 'Images',
    extensions: 'gif,jpg,jpeg,bmp,png',
    mimeTypes: 'image/*'
  }
});
</script>
```

## API对接模式

### 标准AJAX请求模板
```javascript
// GET请求
function fetchData(url, params = {}) {
  return $.ajax({
    url: url,
    method: 'GET',
    data: params,
    dataType: 'json',
    beforeSend: function() {
      // 显示加载动画
      showLoading();
    },
    success: function(response) {
      hideLoading();
      if (response.code === 200) {
        return response.data;
      } else {
        showError(response.message);
      }
    },
    error: function(xhr, status, error) {
      hideLoading();
      showError('请求失败：' + error);
    }
  });
}

// POST请求
function submitData(url, data) {
  return $.ajax({
    url: url,
    method: 'POST',
    data: JSON.stringify(data),
    contentType: 'application/json',
    dataType: 'json',
    beforeSend: function() {
      showLoading();
    },
    success: function(response) {
      hideLoading();
      if (response.code === 200) {
        showSuccess('操作成功');
        return response.data;
      } else {
        showError(response.message);
      }
    },
    error: function(xhr, status, error) {
      hideLoading();
      showError('提交失败：' + error);
    }
  });
}
```

### 通用工具函数
```javascript
// 显示加载动画
function showLoading() {
  // 使用项目内置的loading插件
  $('.lyear-loading').show();
}

function hideLoading() {
  $('.lyear-loading').hide();
}

// 消息提示
function showSuccess(message) {
  // 使用Bootstrap Notify
  $.notify({
    message: message
  }, {
    type: 'success'
  });
}

function showError(message) {
  $.notify({
    message: message
  }, {
    type: 'danger'
  });
}

// 确认对话框
function confirmAction(message, callback) {
  // 使用jQuery Confirm
  $.confirm({
    title: '确认操作',
    content: message,
    buttons: {
      confirm: {
        text: '确定',
        btnClass: 'btn-primary',
        action: callback
      },
      cancel: {
        text: '取消',
        btnClass: 'btn-secondary'
      }
    }
  });
}
```

## 页面类型开发指南

### 1. 仪表板页面 (Dashboard)
**参考**: `lyear_main.html`
**用途**: 数据概览、统计图表、快速操作

```html
<!-- 统计卡片行 -->
<div class="row">
  <div class="col-md-6 col-xl-3">
    <div class="card bg-primary text-white">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <span class="avatar-md rounded-circle bg-white bg-opacity-25 avatar-box">
            <i class="mdi mdi-account fs-4"></i>
          </span>
          <span class="fs-4" id="user-count">0</span>
        </div>
        <div class="text-end">用户总数</div>
      </div>
    </div>
  </div>
  <!-- 更多统计卡片... -->
</div>

<!-- 图表区域 -->
<div class="row mt-4">
  <div class="col-lg-8">
    <div class="card">
      <header class="card-header">
        <div class="card-title">数据趋势</div>
      </header>
      <div class="card-body">
        <canvas id="trend-chart"></canvas>
      </div>
    </div>
  </div>
  <div class="col-lg-4">
    <div class="card">
      <header class="card-header">
        <div class="card-title">最新动态</div>
      </header>
      <div class="card-body">
        <div id="recent-activities"></div>
      </div>
    </div>
  </div>
</div>
```

### 2. 数据列表页面 (List/Table)
**参考**: `lyear_layout_list_curd.html`
**用途**: 数据展示、搜索、分页、批量操作

```html
<div class="card">
  <header class="card-header">
    <div class="card-title">数据管理</div>
    <div class="card-actions">
      <button type="button" class="btn btn-primary" onclick="showAddModal()">
        <i class="mdi mdi-plus"></i> 新增
      </button>
    </div>
  </header>
  <div class="card-body">
    <!-- 搜索栏 -->
    <div class="row mb-3">
      <div class="col-md-3">
        <input type="text" class="form-control" id="search-keyword" placeholder="搜索关键词">
      </div>
      <div class="col-md-2">
        <select class="form-select" id="search-status">
          <option value="">全部状态</option>
          <option value="1">启用</option>
          <option value="0">禁用</option>
        </select>
      </div>
      <div class="col-md-2">
        <button type="button" class="btn btn-outline-primary" onclick="searchData()">
          <i class="mdi mdi-magnify"></i> 搜索
        </button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-responsive">
      <table class="table table-striped" id="data-table">
        <thead>
          <tr>
            <th><input type="checkbox" id="select-all"></th>
            <th>ID</th>
            <th>名称</th>
            <th>状态</th>
            <th>创建时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <!-- 动态内容 -->
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <nav aria-label="分页导航">
      <ul class="pagination justify-content-center" id="pagination">
        <!-- 动态生成 -->
      </ul>
    </nav>
  </div>
</div>
```

### 3. 表单页面 (Form)
**参考**: `lyear_layout_form.html`
**用途**: 数据录入、编辑

```html
<div class="card">
  <header class="card-header">
    <div class="card-title">数据编辑</div>
  </header>
  <div class="card-body">
    <form id="data-form" method="post">
      <input type="hidden" name="id" id="data-id">

      <div class="row">
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">名称 <span class="text-danger">*</span></label>
            <input type="text" class="form-control" name="name" required>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">分类</label>
            <select class="form-select select2" name="category_id">
              <option value="">请选择分类</option>
            </select>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-12">
          <div class="mb-3">
            <label class="form-label">描述</label>
            <textarea class="form-control" name="description" rows="4"></textarea>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">状态</label>
            <div class="form-check form-switch">
              <input class="form-check-input" type="checkbox" name="status" value="1">
              <label class="form-check-label">启用</label>
            </div>
          </div>
        </div>
      </div>

      <div class="form-actions">
        <button type="submit" class="btn btn-primary">
          <i class="mdi mdi-check"></i> 保存
        </button>
        <button type="button" class="btn btn-secondary" onclick="history.back()">
          <i class="mdi mdi-arrow-left"></i> 返回
        </button>
      </div>
    </form>
  </div>
</div>
```

## 常用业务场景

### 1. 用户管理页面
```javascript
// 用户列表API对接
function loadUserList(page = 1, size = 10) {
  const params = {
    page: page,
    size: size,
    keyword: $('#search-keyword').val(),
    status: $('#search-status').val()
  };

  fetchData('/api/users', params).then(response => {
    renderUserTable(response.data);
    renderPagination(response.pagination);
  });
}

// 渲染用户表格
function renderUserTable(users) {
  const tbody = $('#data-table tbody');
  tbody.empty();

  users.forEach(user => {
    const row = `
      <tr>
        <td><input type="checkbox" value="${user.id}"></td>
        <td>${user.id}</td>
        <td>${user.name}</td>
        <td>
          <span class="badge ${user.status ? 'bg-success' : 'bg-secondary'}">
            ${user.status ? '启用' : '禁用'}
          </span>
        </td>
        <td>${user.created_at}</td>
        <td>
          <button class="btn btn-sm btn-outline-primary" onclick="editUser(${user.id})">
            <i class="mdi mdi-pencil"></i> 编辑
          </button>
          <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(${user.id})">
            <i class="mdi mdi-delete"></i> 删除
          </button>
        </td>
      </tr>
    `;
    tbody.append(row);
  });
}

// 删除用户
function deleteUser(id) {
  confirmAction('确定要删除这个用户吗？', function() {
    submitData(`/api/users/${id}`, {}, 'DELETE').then(() => {
      loadUserList();
    });
  });
}
```

### 2. 商品管理页面
```javascript
// 商品表单处理
function initProductForm() {
  // 初始化富文本编辑器
  $('#product-description').summernote({
    height: 200,
    lang: 'zh-CN'
  });

  // 初始化图片上传
  const uploader = WebUploader.create({
    swf: 'js/webuploader/Uploader.swf',
    server: '/api/upload/image',
    pick: '#image-picker',
    accept: {
      title: 'Images',
      extensions: 'gif,jpg,jpeg,bmp,png',
      mimeTypes: 'image/*'
    }
  });

  uploader.on('uploadSuccess', function(file, response) {
    if (response.code === 200) {
      addImagePreview(response.data.url);
    }
  });

  // 表单验证
  $('#product-form').validate({
    rules: {
      name: { required: true, minlength: 2 },
      price: { required: true, number: true, min: 0 },
      category_id: { required: true }
    }
  });
}

// 添加图片预览
function addImagePreview(url) {
  const preview = `
    <div class="image-preview">
      <img src="${url}" class="img-thumbnail" style="width: 100px; height: 100px;">
      <input type="hidden" name="images[]" value="${url}">
      <button type="button" class="btn btn-sm btn-danger" onclick="removeImage(this)">
        <i class="mdi mdi-close"></i>
      </button>
    </div>
  `;
  $('#image-previews').append(preview);
}
```

### 3. 订单管理页面
```javascript
// 订单状态更新
function updateOrderStatus(orderId, status) {
  const statusMap = {
    'pending': '待处理',
    'processing': '处理中',
    'shipped': '已发货',
    'delivered': '已送达',
    'cancelled': '已取消'
  };

  confirmAction(`确定要将订单状态更改为"${statusMap[status]}"吗？`, function() {
    submitData(`/api/orders/${orderId}/status`, { status: status }).then(() => {
      loadOrderList();
      showSuccess('订单状态更新成功');
    });
  });
}

// 订单详情模态框
function showOrderDetail(orderId) {
  fetchData(`/api/orders/${orderId}`).then(order => {
    const modal = `
      <div class="modal fade" id="order-detail-modal">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">订单详情 - ${order.order_no}</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <!-- 订单信息 -->
              <div class="row">
                <div class="col-md-6">
                  <p><strong>订单号:</strong> ${order.order_no}</p>
                  <p><strong>客户:</strong> ${order.customer_name}</p>
                  <p><strong>状态:</strong> ${order.status}</p>
                </div>
                <div class="col-md-6">
                  <p><strong>总金额:</strong> ¥${order.total_amount}</p>
                  <p><strong>创建时间:</strong> ${order.created_at}</p>
                </div>
              </div>

              <!-- 商品列表 -->
              <h6>商品清单</h6>
              <div class="table-responsive">
                <table class="table table-sm">
                  <thead>
                    <tr>
                      <th>商品名称</th>
                      <th>数量</th>
                      <th>单价</th>
                      <th>小计</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${order.items.map(item => `
                      <tr>
                        <td>${item.product_name}</td>
                        <td>${item.quantity}</td>
                        <td>¥${item.price}</td>
                        <td>¥${item.subtotal}</td>
                      </tr>
                    `).join('')}
                  </tbody>
                </table>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
          </div>
        </div>
      </div>
    `;

    $('body').append(modal);
    $('#order-detail-modal').modal('show');

    // 模态框关闭后移除
    $('#order-detail-modal').on('hidden.bs.modal', function() {
      $(this).remove();
    });
  });
}
```

## 插件详细使用指南

### 1. 表单验证插件 (jQuery Validate)
**文件路径**: `js/jquery-validate/`
**使用场景**: 所有表单页面

```html
<!-- 引入文件 -->
<script src="js/jquery-validate/jquery.validate.min.js"></script>
<script src="js/jquery-validate/messages_zh.min.js"></script>

<!-- 表单HTML -->
<form id="user-form">
  <div class="mb-3">
    <label class="form-label">用户名 <span class="text-danger">*</span></label>
    <input type="text" class="form-control" name="username" required>
  </div>
  <div class="mb-3">
    <label class="form-label">邮箱 <span class="text-danger">*</span></label>
    <input type="email" class="form-control" name="email" required>
  </div>
  <div class="mb-3">
    <label class="form-label">密码 <span class="text-danger">*</span></label>
    <input type="password" class="form-control" name="password" required>
  </div>
  <button type="submit" class="btn btn-primary">提交</button>
</form>

<script>
$('#user-form').validate({
  rules: {
    username: {
      required: true,
      minlength: 3,
      maxlength: 20
    },
    email: {
      required: true,
      email: true
    },
    password: {
      required: true,
      minlength: 6
    }
  },
  messages: {
    username: {
      required: "请输入用户名",
      minlength: "用户名至少3个字符",
      maxlength: "用户名最多20个字符"
    },
    email: {
      required: "请输入邮箱",
      email: "请输入有效的邮箱地址"
    },
    password: {
      required: "请输入密码",
      minlength: "密码至少6个字符"
    }
  },
  submitHandler: function(form) {
    // 表单验证通过后的处理
    const formData = $(form).serialize();
    submitData('/api/users', formData);
  }
});
</script>
```

### 2. 数据表格插件 (Bootstrap Table)
**文件路径**: `js/bootstrap-table/`
**使用场景**: 数据列表页面

```html
<!-- 引入文件 -->
<link rel="stylesheet" href="js/bootstrap-table/bootstrap-table.min.css">
<script src="js/bootstrap-table/bootstrap-table.min.js"></script>
<script src="js/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>

<!-- 表格HTML -->
<table id="user-table"
       data-toggle="table"
       data-url="/api/users"
       data-pagination="true"
       data-search="true"
       data-show-refresh="true"
       data-show-columns="true">
  <thead>
    <tr>
      <th data-field="id" data-sortable="true">ID</th>
      <th data-field="username">用户名</th>
      <th data-field="email">邮箱</th>
      <th data-field="status" data-formatter="statusFormatter">状态</th>
      <th data-field="created_at" data-sortable="true">创建时间</th>
      <th data-field="operate" data-formatter="operateFormatter">操作</th>
    </tr>
  </thead>
</table>

<script>
// 状态格式化
function statusFormatter(value, row, index) {
  return value ?
    '<span class="badge bg-success">启用</span>' :
    '<span class="badge bg-secondary">禁用</span>';
}

// 操作按钮格式化
function operateFormatter(value, row, index) {
  return [
    '<button class="btn btn-sm btn-outline-primary" onclick="editUser(' + row.id + ')">',
    '<i class="mdi mdi-pencil"></i> 编辑',
    '</button>',
    '<button class="btn btn-sm btn-outline-danger ms-1" onclick="deleteUser(' + row.id + ')">',
    '<i class="mdi mdi-delete"></i> 删除',
    '</button>'
  ].join('');
}

// 表格事件
$('#user-table').on('load-success.bs.table', function (data) {
  console.log('表格加载成功', data);
});

$('#user-table').on('refresh.bs.table', function () {
  console.log('表格刷新');
});
</script>
```

### 3. 富文本编辑器 (Summernote)
**文件路径**: `js/summernote/`
**使用场景**: 内容编辑页面

```html
<!-- 引入文件 -->
<link rel="stylesheet" href="js/summernote/summernote-bs4.min.css">
<script src="js/summernote/summernote-bs4.min.js"></script>
<script src="js/summernote/lang/summernote-zh-CN.min.js"></script>

<!-- 编辑器HTML -->
<div class="mb-3">
  <label class="form-label">内容</label>
  <textarea id="content-editor" name="content"></textarea>
</div>

<script>
$('#content-editor').summernote({
  height: 300,
  lang: 'zh-CN',
  toolbar: [
    ['style', ['style']],
    ['font', ['bold', 'underline', 'clear']],
    ['fontname', ['fontname']],
    ['color', ['color']],
    ['para', ['ul', 'ol', 'paragraph']],
    ['table', ['table']],
    ['insert', ['link', 'picture', 'video']],
    ['view', ['fullscreen', 'codeview', 'help']]
  ],
  callbacks: {
    onImageUpload: function(files) {
      // 图片上传处理
      uploadImage(files[0]);
    }
  }
});

// 图片上传函数
function uploadImage(file) {
  const formData = new FormData();
  formData.append('image', file);

  $.ajax({
    url: '/api/upload/image',
    method: 'POST',
    data: formData,
    processData: false,
    contentType: false,
    success: function(response) {
      if (response.code === 200) {
        $('#content-editor').summernote('insertImage', response.data.url);
      }
    }
  });
}
```

### 4. 文件上传插件 (WebUploader)
**文件路径**: `js/webuploader/`
**使用场景**: 文件上传页面

```html
<!-- 引入文件 -->
<link rel="stylesheet" href="js/webuploader/webuploader.css">
<script src="js/webuploader/webuploader.min.js"></script>

<!-- 上传区域HTML -->
<div class="mb-3">
  <label class="form-label">上传文件</label>
  <div id="uploader" class="wu-example">
    <div class="queueList">
      <div id="dndArea" class="placeholder">
        <div id="filePicker">选择文件</div>
        <p>或将文件拖到这里，单次最多可选300个文件</p>
      </div>
    </div>
    <div class="statusBar" style="display:none;">
      <div class="progress">
        <span class="text">0%</span>
        <span class="percentage"></span>
      </div>
      <div class="info"></div>
      <div class="btns">
        <div id="filePicker2">继续选择</div>
        <div class="uploadBtn">开始上传</div>
      </div>
    </div>
  </div>
</div>

<script>
var uploader = WebUploader.create({
  swf: 'js/webuploader/Uploader.swf',
  server: '/api/upload',
  pick: {
    id: '#filePicker',
    innerHTML: '选择文件'
  },
  dnd: '#dndArea',
  paste: '#uploader',
  accept: {
    title: 'Images',
    extensions: 'gif,jpg,jpeg,bmp,png',
    mimeTypes: 'image/*'
  },
  auto: false,
  chunked: true,
  chunkSize: 2 * 1024 * 1024,
  threads: 3
});

// 文件添加进队列
uploader.on('fileQueued', function(file) {
  var $li = $(
    '<div id="' + file.id + '" class="file-item thumbnail">' +
    '<img>' +
    '<div class="info">' + file.name + '</div>' +
    '</div>'
  );

  $('.queueList').append($li);

  // 创建缩略图
  uploader.makeThumb(file, function(error, src) {
    if (error) {
      $li.find('img').replaceWith('<span>不能预览</span>');
      return;
    }
    $li.find('img').attr('src', src);
  }, 100, 100);
});

// 上传进度
uploader.on('uploadProgress', function(file, percentage) {
  var $li = $('#' + file.id);
  var $percent = $li.find('.progress span');

  $percent.css('width', percentage * 100 + '%');
});

// 上传成功
uploader.on('uploadSuccess', function(file, response) {
  if (response.code === 200) {
    $('#' + file.id).addClass('upload-success');
    // 保存文件信息
    addFileToForm(response.data);
  }
});

// 上传失败
uploader.on('uploadError', function(file) {
  $('#' + file.id).addClass('upload-error');
});

// 开始上传
$('.uploadBtn').on('click', function() {
  uploader.upload();
});
</script>
```

## 响应式设计和移动端适配

### Bootstrap 5 栅格系统使用
```html
<!-- 响应式卡片布局 -->
<div class="row">
  <!-- 超小屏幕1列，小屏幕2列，中屏幕3列，大屏幕4列 -->
  <div class="col-12 col-sm-6 col-md-4 col-lg-3">
    <div class="card">...</div>
  </div>
</div>

<!-- 响应式表单 -->
<div class="row">
  <div class="col-12 col-md-6">
    <div class="mb-3">
      <label class="form-label">字段1</label>
      <input type="text" class="form-control">
    </div>
  </div>
  <div class="col-12 col-md-6">
    <div class="mb-3">
      <label class="form-label">字段2</label>
      <input type="text" class="form-control">
    </div>
  </div>
</div>

<!-- 响应式按钮组 -->
<div class="d-flex flex-column flex-md-row gap-2">
  <button class="btn btn-primary">主要操作</button>
  <button class="btn btn-secondary">次要操作</button>
</div>
```

### 移动端优化CSS
```css
/* 移动端适配样式 */
@media (max-width: 768px) {
  /* 卡片间距调整 */
  .card {
    margin-bottom: 1rem;
  }

  /* 按钮组垂直排列 */
  .btn-group {
    flex-direction: column;
  }

  /* 表格字体大小调整 */
  .table-responsive {
    font-size: 0.875rem;
  }

  /* 搜索栏调整 */
  .search-form .col-md-3,
  .search-form .col-md-2 {
    margin-bottom: 0.5rem;
  }

  /* 模态框调整 */
  .modal-dialog {
    margin: 0.5rem;
  }
}

@media (max-width: 576px) {
  /* 超小屏幕优化 */
  .card-header {
    padding: 0.75rem;
  }

  .card-body {
    padding: 0.75rem;
  }

  .btn-sm {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}
```

## 性能优化和最佳实践

### 1. 按需加载资源
```html
<!-- 基础页面只加载核心文件 -->
<link rel="stylesheet" href="css/bootstrap.min.css">
<link rel="stylesheet" href="css/materialdesignicons.min.css">
<link rel="stylesheet" href="css/style.min.css">

<script src="js/jquery.min.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="js/main.min.js"></script>

<!-- 根据页面功能按需加载插件 -->
<!-- 表单页面 -->
<script src="js/jquery-validate/jquery.validate.min.js"></script>
<script src="js/select2/select2.min.js"></script>

<!-- 表格页面 -->
<script src="js/bootstrap-table/bootstrap-table.min.js"></script>

<!-- 富文本编辑页面 -->
<script src="js/summernote/summernote-bs4.min.js"></script>
```

### 2. 数据缓存策略
```javascript
// 简单的内存缓存实现
class DataCache {
  constructor(defaultTTL = 300000) { // 默认5分钟
    this.cache = new Map();
    this.defaultTTL = defaultTTL;
  }

  set(key, data, ttl = this.defaultTTL) {
    this.cache.set(key, {
      data: data,
      expires: Date.now() + ttl
    });
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() > item.expires) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  clear() {
    this.cache.clear();
  }
}

const cache = new DataCache();

// 带缓存的数据获取
function fetchDataWithCache(url, params = {}, ttl = 300000) {
  const cacheKey = url + JSON.stringify(params);
  const cached = cache.get(cacheKey);

  if (cached) {
    return Promise.resolve(cached);
  }

  return fetchData(url, params).then(data => {
    cache.set(cacheKey, data, ttl);
    return data;
  });
}
```

### 3. 错误处理和用户体验
```javascript
// 全局错误处理
window.addEventListener('error', function(e) {
  console.error('JavaScript Error:', e.error);
  showError('页面发生错误，请刷新重试');
});

// 网络状态监听
window.addEventListener('online', function() {
  showSuccess('网络连接已恢复');
});

window.addEventListener('offline', function() {
  showError('网络连接已断开，请检查网络设置');
});

// AJAX统一错误处理
$(document).ajaxError(function(event, xhr, settings, error) {
  console.error('AJAX Error:', {
    url: settings.url,
    status: xhr.status,
    error: error
  });

  switch(xhr.status) {
    case 401:
      showError('登录已过期，请重新登录');
      setTimeout(() => {
        window.location.href = '/login';
      }, 2000);
      break;
    case 403:
      showError('没有权限执行此操作');
      break;
    case 404:
      showError('请求的资源不存在');
      break;
    case 500:
      showError('服务器内部错误，请稍后重试');
      break;
    default:
      showError('请求失败，请检查网络连接');
  }
});
```

## AI工具使用指南

### 1. 页面生成提示词模板
```
请基于Light Year Admin V5模板生成一个[功能名称]管理页面，要求：

**页面类型**: [列表页面/表单页面/仪表板页面]
**主要功能**:
- [功能1：如数据展示、搜索、分页]
- [功能2：如新增、编辑、删除]
- [功能3：如状态切换、批量操作]

**技术要求**:
- 使用Bootstrap 5响应式布局
- 集成[插件名称]插件（如jQuery Validate、Bootstrap Table等）
- 包含完整的API对接代码（GET/POST/PUT/DELETE）
- 添加表单验证和错误处理
- 支持移动端适配

**API接口**:
- 列表接口: GET /api/[资源名称]
- 详情接口: GET /api/[资源名称]/{id}
- 创建接口: POST /api/[资源名称]
- 更新接口: PUT /api/[资源名称]/{id}
- 删除接口: DELETE /api/[资源名称]/{id}

**数据字段**:
[列出具体的数据字段和类型]

**参考页面**: 参考模板中的[具体页面名称]页面结构
```

### 2. 组件生成提示词
```
请基于Light Year Admin V5模板生成一个[组件名称]组件，要求：

**组件功能**: [具体描述组件功能]
**使用场景**: [描述在什么页面或情况下使用]
**技术栈**: Bootstrap 5 + jQuery + [相关插件]
**样式要求**: 保持与模板整体风格一致
**交互要求**: [描述用户交互行为]

请提供完整的HTML、CSS、JavaScript代码。
```

### 3. 代码规范要求
- **HTML结构**: 使用语义化标签，遵循Bootstrap 5类名规范
- **CSS样式**: 优先使用Bootstrap类，自定义样式使用模板中的变量
- **JavaScript**: 使用jQuery语法，遵循模板中的函数命名规范
- **图标**: 统一使用Material Design Icons (mdi-*)
- **颜色**: 使用Bootstrap 5主题色彩 (primary, secondary, success, danger等)
- **响应式**: 确保在各种屏幕尺寸下正常显示

### 4. 常用代码片段
```javascript
// 页面初始化模板
$(document).ready(function() {
  // 初始化插件
  initPlugins();

  // 加载数据
  loadData();

  // 绑定事件
  bindEvents();
});

// 标准API调用模板
function apiCall(method, url, data = null) {
  return $.ajax({
    url: url,
    method: method,
    data: data ? JSON.stringify(data) : null,
    contentType: 'application/json',
    dataType: 'json',
    beforeSend: showLoading,
    complete: hideLoading
  });
}

// 标准表格渲染模板
function renderTable(data) {
  const tbody = $('#data-table tbody');
  tbody.empty();

  if (!data || data.length === 0) {
    tbody.append('<tr><td colspan="100%" class="text-center">暂无数据</td></tr>');
    return;
  }

  data.forEach(item => {
    const row = createTableRow(item);
    tbody.append(row);
  });
}
```

这份详细的知识文档为AI工具提供了完整的Light Year Admin V5模板使用指南，包含了从基础架构到具体实现的所有必要信息，可以帮助AI工具高效生成符合模板规范的后台管理页面。
