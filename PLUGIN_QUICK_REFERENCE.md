# Light Year Admin V5 - 插件快速参考手册

## 插件列表总览

### 表单相关插件
| 插件名称 | 文件路径 | 主要功能 | 使用场景 |
|---------|----------|----------|----------|
| jQuery <PERSON> | `js/jquery-validate/` | 表单验证 | 所有表单页面 |
| Select2 | `js/select2/` | 下拉框增强 | 选择器、搜索框 |
| Bootstrap Select | `js/bootstrap-select/` | 下拉框美化 | 简单选择器 |
| TouchSpin | `js/bootstrap-touchspin/` | 数字输入增强 | 数量、价格输入 |
| Bootstrap Switch | 内置 | 开关切换 | 状态切换 |
| TagsInput | `js/jquery-tagsinput/` | 标签输入 | 标签管理 |

### 日期时间插件
| 插件名称 | 文件路径 | 主要功能 | 使用场景 |
|---------|----------|----------|----------|
| DateRangePicker | `js/bootstrap-daterangepicker/` | 日期范围选择 | 时间筛选 |
| Material DateTimePicker | `js/bootstrap-material-datetimepicker/` | 日期时间选择 | 表单日期输入 |
| Bootstrap DatePicker | `js/bootstrap-datepicker/` | 日期选择 | 简单日期选择 |

### 富文本编辑器
| 插件名称 | 文件路径 | 特点 | 推荐场景 |
|---------|----------|------|----------|
| Summernote | `js/summernote/` | 轻量级、Bootstrap风格 | 一般内容编辑 |
| CKEditor 5 | `js/ckeditor5/` | 功能强大、现代化 | 专业内容编辑 |
| CKEditor 4 | `js/ckeditor4/` | 经典版本、稳定 | 兼容性要求高 |
| TinyMCE | `js/tinymce/` | 功能全面 | 复杂内容编辑 |
| UEditor | `js/ueditor/` | 百度出品、中文友好 | 中文环境 |
| WangEditor | `js/wangEditor/` | 国产、轻量 | 简单富文本需求 |
| Editor.md | `js/editor.md/` | Markdown编辑器 | 技术文档编辑 |

### 数据展示插件
| 插件名称 | 文件路径 | 主要功能 | 使用场景 |
|---------|----------|----------|----------|
| Bootstrap Table | `js/bootstrap-table/` | 数据表格 | 数据列表页面 |
| Chart.js | `js/chart.min.js` | 图表绘制 | 数据可视化 |
| FullCalendar | `js/fullcalendar/` | 日历组件 | 日程管理 |

### 文件上传插件
| 插件名称 | 文件路径 | 特点 | 适用场景 |
|---------|----------|------|----------|
| WebUploader | `js/webuploader/` | 功能全面、支持大文件 | 文件管理系统 |
| Dropzone | `js/dropzone/` | 拖拽上传、界面美观 | 图片上传 |
| Cropper | `js/cropper/` | 图片裁剪 | 头像上传 |

### 树形结构插件
| 插件名称 | 文件路径 | 特点 | 适用场景 |
|---------|----------|------|----------|
| ZTree | `js/zTree_v3/` | 功能强大、性能好 | 组织架构、菜单管理 |
| Bootstrap TreeView | `js/bootstrap-treeview/` | Bootstrap风格 | 简单树形展示 |
| JSTree | `js/jstree/` | 功能全面、主题丰富 | 文件管理、分类管理 |

### UI增强插件
| 插件名称 | 文件路径 | 主要功能 | 使用场景 |
|---------|----------|----------|----------|
| Layer | `js/layer/` | 弹层组件 | 弹窗、提示 |
| jQuery Confirm | `js/jquery-confirm/` | 确认对话框 | 删除确认 |
| Bootstrap Notify | `js/bootstrap-notify.min.js` | 消息通知 | 操作反馈 |
| Magnific Popup | `js/magnific-popup/` | 图片预览 | 图片展示 |
| Perfect Scrollbar | `js/perfect-scrollbar.min.js` | 滚动条美化 | 内容滚动 |

## 快速集成代码

### 1. 表单验证 (jQuery Validate)
```html
<script src="js/jquery-validate/jquery.validate.min.js"></script>
<script src="js/jquery-validate/messages_zh.min.js"></script>
<script>
$('#form').validate({
  rules: { field: { required: true } },
  messages: { field: { required: "必填项" } }
});
</script>
```

### 2. 下拉框增强 (Select2)
```html
<link rel="stylesheet" href="js/select2/select2.min.css">
<script src="js/select2/select2.min.js"></script>
<script>
$('.select2').select2({
  placeholder: '请选择',
  allowClear: true
});
</script>
```

### 3. 日期范围选择 (DateRangePicker)
```html
<link rel="stylesheet" href="js/bootstrap-daterangepicker/daterangepicker.min.css">
<script src="js/momentjs/moment.min.js"></script>
<script src="js/bootstrap-daterangepicker/daterangepicker.min.js"></script>
<script>
$('.daterange').daterangepicker({
  locale: { format: 'YYYY-MM-DD' }
});
</script>
```

### 4. 富文本编辑器 (Summernote)
```html
<link rel="stylesheet" href="js/summernote/summernote-bs4.min.css">
<script src="js/summernote/summernote-bs4.min.js"></script>
<script>
$('.summernote').summernote({
  height: 200,
  lang: 'zh-CN'
});
</script>
```

### 5. 数据表格 (Bootstrap Table)
```html
<link rel="stylesheet" href="js/bootstrap-table/bootstrap-table.min.css">
<script src="js/bootstrap-table/bootstrap-table.min.js"></script>
<script>
$('#table').bootstrapTable({
  url: '/api/data',
  pagination: true,
  search: true
});
</script>
```

### 6. 文件上传 (WebUploader)
```html
<link rel="stylesheet" href="js/webuploader/webuploader.css">
<script src="js/webuploader/webuploader.min.js"></script>
<script>
WebUploader.create({
  server: '/upload',
  pick: '#picker'
});
</script>
```

### 7. 树形结构 (ZTree)
```html
<link rel="stylesheet" href="js/zTree_v3/css/zTreeStyle/zTreeStyle.css">
<script src="js/zTree_v3/js/jquery.ztree.all.min.js"></script>
<script>
$.fn.zTree.init($("#tree"), setting, zNodes);
</script>
```

### 8. 弹层提示 (Layer)
```html
<script src="js/layer/layer.min.js"></script>
<script>
layer.msg('提示信息');
layer.confirm('确认吗？', function(index){
  // 确认操作
  layer.close(index);
});
</script>
```

### 9. 图片裁剪 (Cropper)
```html
<link rel="stylesheet" href="js/cropper/cropper.min.css">
<script src="js/cropper/cropper.min.js"></script>
<script>
$('#image').cropper({
  aspectRatio: 1,
  viewMode: 1
});
</script>
```

### 10. 消息通知 (Bootstrap Notify)
```html
<script src="js/bootstrap-notify.min.js"></script>
<script>
$.notify({
  message: '操作成功'
}, {
  type: 'success'
});
</script>
```

## 常用配置选项

### Select2 常用配置
```javascript
$('.select2').select2({
  placeholder: '请选择',
  allowClear: true,
  width: '100%',
  language: 'zh-CN',
  ajax: {
    url: '/api/options',
    dataType: 'json',
    delay: 250,
    processResults: function (data) {
      return {
        results: data.items
      };
    }
  }
});
```

### Bootstrap Table 常用配置
```javascript
$('#table').bootstrapTable({
  url: '/api/data',
  method: 'get',
  pagination: true,
  sidePagination: 'server',
  pageSize: 10,
  pageList: [10, 25, 50, 100],
  search: true,
  showRefresh: true,
  showColumns: true,
  sortName: 'id',
  sortOrder: 'desc',
  queryParams: function(params) {
    return {
      page: params.offset / params.limit + 1,
      size: params.limit,
      sort: params.sort,
      order: params.order,
      search: params.search
    };
  }
});
```

### Summernote 常用配置
```javascript
$('.summernote').summernote({
  height: 300,
  lang: 'zh-CN',
  placeholder: '请输入内容...',
  toolbar: [
    ['style', ['style']],
    ['font', ['bold', 'underline', 'clear']],
    ['color', ['color']],
    ['para', ['ul', 'ol', 'paragraph']],
    ['table', ['table']],
    ['insert', ['link', 'picture', 'video']],
    ['view', ['fullscreen', 'codeview']]
  ],
  callbacks: {
    onImageUpload: function(files) {
      uploadImage(files[0]);
    }
  }
});
```

### WebUploader 常用配置
```javascript
var uploader = WebUploader.create({
  swf: 'js/webuploader/Uploader.swf',
  server: '/api/upload',
  pick: '#filePicker',
  accept: {
    title: 'Images',
    extensions: 'gif,jpg,jpeg,bmp,png',
    mimeTypes: 'image/*'
  },
  auto: false,
  chunked: true,
  chunkSize: 2 * 1024 * 1024,
  threads: 3,
  fileNumLimit: 10,
  fileSizeLimit: 50 * 1024 * 1024,
  fileSingleSizeLimit: 10 * 1024 * 1024
});
```

## 插件兼容性说明

### Bootstrap 5 兼容性
- ✅ 完全兼容：Bootstrap Table, Select2, DateRangePicker
- ⚠️ 需要调整：Summernote (使用 summernote-bs4.min.js)
- ⚠️ 样式调整：部分老插件可能需要自定义CSS适配

### jQuery 版本要求
- 推荐使用：jQuery 3.6.0+
- 最低要求：jQuery 1.12.0+
- 注意：部分插件对jQuery版本有特殊要求

### 浏览器支持
- 现代浏览器：Chrome, Firefox, Safari, Edge
- IE支持：IE11+ (部分插件可能不支持IE)
- 移动端：iOS Safari, Android Chrome

## 性能优化建议

1. **按需加载**：只在需要的页面加载对应插件
2. **CDN加速**：生产环境建议使用CDN
3. **文件合并**：将常用插件合并为一个文件
4. **懒加载**：大型插件可以考虑懒加载
5. **缓存策略**：设置合适的缓存头

这份快速参考手册提供了所有插件的基本信息和快速集成代码，方便AI工具快速查找和使用。
