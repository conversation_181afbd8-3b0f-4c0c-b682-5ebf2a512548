/** ----------------------------------
 * 光年(Light Year Admin v5)后台管理系统模板
 * 基于Bootstrap v5.1.3的一款纯静态页面模板
 * http://www.bixiaguangnian.com
 * yinqi<<EMAIL>>
 -------------------------------------- */
:root {
	/*************************
	 * 在这里对主要的配色做出定义
	 ************************/
    
	/* 圆角样式 */
	--border-radius-mini: 12px;
	--border-radius-small: 16px;
	--border-radius-medium: 24px;
	--border-radius-large: 32px;

	--bs-purple: #926dde;
    --bs-purple-hover: #a282e3;
	--bs-pink: #f96197;
    --bs-pink-hover: #fa75a4;
	--bs-yellow: #fcc525;
    --bs-yellow-hover: #fdd04d;
	--bs-teal: #33cabb;
    --bs-teal-hover: #52d3c7;
	--bs-cyan: #57c7d4;
    --bs-cyan-hover: #77d2dc;
	--bs-brown: #8d6658;
    --bs-brown-hover: #9d7162;
    --bs-indigo: #6610f2;
    --bs-indigo-hover: #7516F4;
	--bs-primary: #007bff;
    --bs-primary-hover: #0096FF;
	--bs-success: #15c377; /* green */
    --bs-success-hover: #16d17f;
	--bs-info: #48b0f7; /* blue */
    --bs-info-hover: #65bdf8;
	--bs-warning: #faa64b; /* orange */
    --bs-warning-hover: #fbb264;
	--bs-danger: #f44236; /* red */
    --bs-danger-hover: #fa8181;
    --bs-dark: #212529;
    --bs-dark-hover: #393F45;
    --bs-secondary: #6c757d;
    --bs-secondary-hover: #7a838a;
    --bs-light: #fcfdfe;
    --bs-light-hover: #f9fafb;
    --bs-pre: #f8f9fa;
    --bs-pre-font: #2f6f9f;
    --bs-pure-black: #000000;
    
	--bs-primary-rgb: 0, 123, 255;
	--bs-success-rgb: 21, 195, 119;
	--bs-info-rgb: 72, 176, 247;
	--bs-warning-rgb: 250, 166, 75;
	--bs-danger-rgb: 244, 66, 54;
	--bs-teal-rgb: 51, 202, 187;
	--bs-indigo-rgb: 102, 16, 242;
	--bs-purple-rgb: 146, 109, 222;
	--bs-pink-rgb: 249, 97, 151;
	--bs-yellow-rgb: 252, 197, 37;
	--bs-cyan-rgb: 87, 199, 212;
	--bs-brown-rgb: 141, 102, 88;
	--bs-dark-rgb: 33, 37, 41;
	--bs-secondary-rgb: 108, 117, 125;
	--bs-light-rgb: 252, 253, 254;
	--bs-body-color-rgb: 73, 80, 87;
    --bs-gray-rgb: 108, 117, 125;
	--bs-font-sans-serif: system-ui, -apple-system, "Microsoft YaHei", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
	--bs-body-font-size: 14px;
	--bs-body-line-height: 1.72;
	--bs-body-color: #495057;
	--bs-body-bg: #f4f5fa;
	--bs-border-color: #ededee;
	--bs-border-rgb: 237, 237, 238;
}

html,
body {
	height: 100%;
}

a {
	color: var(--bs-primary);
	text-decoration: none;
	-webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
	transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

a:hover {
	color: var(--bs-primary-hover);
}

a:hover,
a:focus,
a:active {
	text-decoration: none;
	outline: none
}

a,
button,
a:focus,
a:active,
button:focus,
button:active,
.input-group-text:focus,
.input-group-text:active {
	outline: none !important;
}

hr {
    background-color: rgba(var(--bs-body-color-rgb), .1);
    opacity: 1;
}

pre {
	background-color: var(--bs-pre);
	border: none;
	padding: 15px;
	border-radius: 3px;
	font-size: inherit;
	color: var(--bs-pre-font);
}

img {
    max-width: 100%;
}

/** ----------------------------------
 * 演示新增样式，为了演示效果用，实际部署中用不到可以删除
 -------------------------------------- */
.example-box .btn {
	margin-bottom: 10px;
	margin-right: 6px;
}

.border-example,
.border-example-row {
	padding: 1rem;
	margin: 1rem 0 0;
	border: .2rem solid var(--bs-pre);
}

.border-example-row .row>.col,
.border-example-row .row>[class^=col-] {
	padding-top: .75rem;
	padding-bottom: .75rem;
	background-color: rgba(var(--bs-dark-rgb), 0.03);
	border: 1px solid rgba(var(--bs-dark-rgb), 0.1);
}

.border-example-row .row+.row {
	margin-top: 1rem;
}

.border-example+pre {
	border-left: none;
}

.border-example-row .row+.row {
	margin-top: 1rem;
}

.border-example::after {
	display: block;
	clear: both;
	content: "";
}

.border-example> :last-child {
	margin-bottom: 0;
}

.border-example-row-flex-cols .row {
	min-height: 10rem;
	background-color: rgba(255, 0, 0, 0.1);
}

.border-example>.form-control+.form-control {
	margin-top: 0.5rem;
}

.border-highlight {
	background-color: rgba(var(--bs-purple-rgb), .15);
	border: 1px solid rgba(var(--bs-purple-rgb), .15);
}

.border-example-toasts {
	min-height: 240px;
}

.border-example-position-utils {
	position: relative;
	padding: 3em;
}

.border-example-position-utils .position-relative {
	height: 200px;
	background-color: var(--bs-light-hover);
}

.border-example-position-utils .position-absolute {
	width: 2em;
	height: 2em;
	background-color: var(--bs-dark);
	border-radius: 0.25rem;
}

.border-example-ratios .ratio {
	display: inline-block;
	width: 10rem;
	color: var(--bs-secondary);
	background-color: var(--bs-pre);
	border: 1px solid var(--bs-gray-300);
}

.border-example-ratios .ratio>div {
	display: flex;
	align-items: center;
	justify-content: center;
}

.scrollspy-example {
	position: relative;
	height: 200px;
	margin-top: .5rem;
	overflow: auto;
}

.scrollspy-example-2 {
	position: relative;
	height: 350px;
	overflow: auto;
}

.border-example>.nav+.nav,
.border-example>.alert+.alert,
.border-example>.navbar+.navbar,
.border-example>.progress+.progress {
	margin-top: 1rem;
}

.border-example-offcanvas .offcanvas {
	position: static;
	display: block;
	height: 200px;
	visibility: visible;
	transform: translate(0);
}

.border-example .fixed-bottom,
.border-example .sticky-bottom {
	position: static;
	margin: 1rem -1rem -1rem;
}

.border-example .fixed-top,
.border-example .sticky-top {
	position: static;
	margin: -1rem -1rem 1rem;
}

.border-example-modal .modal {
	position: static;
	display: block;
}

.border-example>.dropdown-menu {
	position: static;
	display: block;
}
.border-example-border-utils [class^="border"] {
    display: inline-block;
    width: 5rem;
    height: 5rem;
    margin: 0.25rem;
    background-color: var(--bs-gray-100);
}
.border-example-border-utils-0 [class^="border"] {
    border: 1px solid var(--bs-gray-300);
}

/** ----------------------------------
 * 发现的一个问题
 * flex的布局下，按钮里面的多个英文或中文不会在一行显示
 * 这里强制其不换行
 -------------------------------------- */
.d-flex .btn,
.d-flex .nav-link {
	white-space: nowrap;
}

/** ----------------------------------
 * 通用辅助类
 -------------------------------------- */
/* padding */
.p-2-5 {
	padding: 0.75rem !important;
}

.px-2-5 {
	padding-right: 0.75rem !important;
	padding-left: 0.75rem !important;
}

.py-2-5 {
	padding-top: 0.75rem !important;
	padding-bottom: 0.75rem !important;
}

.pt-2-5 {
	padding-top: 0.75rem !important;
}

.pe-2-5 {
	padding-right: 0.75rem !important;
}

.pb-2-5 {
	padding-bottom: 0.75rem !important;
}

.ps-2-5 {
	padding-left: 0.75rem !important;
}

.m-2-5 {
	margin: 0.75rem !important;
}

.mx-2-5 {
	margin-right: 0.75rem !important;
	margin-left: 0.75rem !important;
}

.my-2-5 {
	margin-top: 0.75rem !important;
	margin-bottom: 0.75rem !important;
}

.mt-2-5 {
	margin-top: 0.75rem !important;
}

.mb-2-5 {
	margin-bottom: 0.75rem !important;
}

.me-2-5 {
	margin-right: 0.75rem !important;
}

.ms-2-5 {
	margin-left: 0.75rem !important;
}

/* 垂直水平居中 */
.center-vh {
	display: -webkit-box;
	display: flex;
	-webkit-box-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	align-items: center;
	height: 100%;
}

/* 布局 */
.flex-box {
    display: -webkit-box;
    display: flex;
    -webkit-box-pack: justify;
    justify-content: space-between;
}

/* 分割线 */
.lyear-divider {
	display: -webkit-box;
	display: flex;
	-webkit-box-align: center;
	align-items: center;
	-webkit-box-flex: 0;
	flex: 0 1;
	color: var(--bs-body-color);
	font-size: 14px;
	letter-spacing: .5px;
	margin: 2rem auto;
	width: 100%;
}

.lyear-divider::before,
.lyear-divider::after {
	content: '';
	-webkit-box-flex: 1;
	flex-grow: 1;
	border-top: 1px solid var(--bs-border-color);
}

.lyear-divider::before {
	margin-right: 15px;
}

.lyear-divider::after {
	margin-left: 15px;
}

/* 头像 */
.avatar-box {
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
}

.avatar-xs {
	height: 1rem;
	width: 1rem;
}

.avatar-sm {
	height: 2rem;
	width: 2rem;
}

.avatar-md {
	height: 3rem;
	width: 3rem;
}

.avatar-lg {
	height: 4rem;
	width: 4rem;
}

.avatar-xl {
	height: 5rem;
	width: 5rem;
}

/* 其他 */
.rotate-180 {
	-webkit-transform: rotate(180deg);
	transform: rotate(180deg);
}
.btn-close:focus {
    box-shadow: none;
}
.overflow-x-hidden {
    overflow-x: hidden !important;
}
.overflow-y-hidden {
    overflow-y: hidden !important;
}

/** ----------------------------------
 * 重置样式
 -------------------------------------- */
/* 布局 */
.container-fluid {
	padding-left: 1rem !important;
	padding-right: 1rem !important;
	padding-top: 1rem !important;
	padding-bottom: 0 !important;
}
.navbar .container-fluid {
    padding: 0.5rem !important;
}

/* 标题 */

/* 按钮 */
.btn {
	font-size: 14px;
	padding: 0.4695rem 0.75rem;
	-webkit-border-radius: 0.125rem;
	border-radius: 0.125rem;
}

.btn-mini {
	padding: 6px 14px;
}

.btn-check:focus+.btn,
.btn:focus {
	outline: 0;
	box-shadow: none;
}

.btn-w-xs {
	width: 80px;
}

.btn-w-sm {
	width: 100px;
}

.btn-w-md {
	width: 120px;
}

.btn-w-lg {
	width: 145px;
}

.btn-w-xl {
	width: 180px;
}

.btn-default {
	background-color: var(--bs-light);
	border-color: var(--bs-border-color);
	color: var(--bs-secondary) !important;
}

.btn-default:hover {
	background-color: var(--bs-light-hover);
	border-color: var(--bs-border-color);
	color: var(--bs-body-color);
}

.btn-default:focus,
.btn-default.focus {
	background-color: var(--bs-light-hover) !important;
	border-color: var(--bs-border-color) !important;
	color: var(--bs-body-color);
}

.btn-default:not([disabled]):not(.disabled).active,
.btn-default:not([disabled]):not(.disabled):active,
.show>.btn-default.dropdown-toggle {
	background-color: var(--bs-light-hover) !important;
	border-color: var(--bs-border-color) !important;
	color: var(--bs-body-color);
	-webkit-box-shadow: none;
	box-shadow: none
}

.btn-default:not(:disabled):not(.disabled):active:focus,
.btn-default:not(:disabled):not(.disabled).active:focus,
.show>.btn-default.dropdown-toggle:focus {
	box-shadow: none;
}

.btn-default.disabled,
.btn-default:disabled {
	background-color: var(--bs-light);
	border-color: var(--bs-border-color);
	opacity: 0.5
}

.btn-primary {
	background-color: var(--bs-primary);
	border-color: var(--bs-primary);
}

.btn-primary:hover {
	background-color: var(--bs-primary-hover);
	border-color: var(--bs-primary-hover);
}

.btn-check:focus+.btn-primary,
.btn-primary:focus {
	background-color: var(--bs-primary-hover);
	border-color: var(--bs-primary-hover);
	box-shadow: none;
}

.btn-check:checked+.btn-primary,
.btn-check:active+.btn-primary,
.btn-primary:active,
.btn-primary.active,
.show>.btn-primary.dropdown-toggle {
	background-color: var(--bs-primary-hover);
	border-color: var(--bs-primary-hover);
}

.btn-check:checked+.btn-primary:focus,
.btn-check:active+.btn-primary:focus,
.btn-primary:active:focus,
.btn-primary.active:focus,
.show>.btn-primary.dropdown-toggle:focus {
	box-shadow: none;
}

.btn-primary:disabled,
.btn-primary.disabled {
	background-color: var(--bs-primary);
	border-color: var(--bs-primary);
}

.btn-secondary {
	background-color: var(--bs-secondary);
	border-color: var(--bs-secondary);
}

.btn-secondary:hover {
	background-color: var(--bs-secondary-hover);
	border-color: var(--bs-secondary-hover);
}

.btn-check:focus+.btn-secondary,
.btn-secondary:focus {
	background-color: var(--bs-secondary-hover);
	border-color: var(--bs-secondary-hover);
	box-shadow: none;
}

.btn-check:checked+.btn-secondary,
.btn-check:active+.btn-secondary,
.btn-secondary:active,
.btn-secondary.active,
.show>.btn-secondary.dropdown-toggle {
	background-color: var(--bs-secondary-hover);
	border-color: var(--bs-secondary-hover);
}

.btn-check:checked+.btn-secondary:focus,
.btn-check:active+.btn-secondary:focus,
.btn-secondary:active:focus,
.btn-secondary.active:focus,
.show>.btn-secondary.dropdown-toggle:focus {
	box-shadow: none;
}

.btn-success {
	background-color: var(--bs-success);
	border-color: var(--bs-success);
}

.btn-success:hover {
	background-color: var(--bs-success-hover);
	border-color: var(--bs-success-hover);
}

.btn-check:focus+.btn-success,
.btn-success:focus {
	background-color: var(--bs-success-hover);
	border-color: var(--bs-success-hover);
	box-shadow: none;
}

.btn-check:checked+.btn-success,
.btn-check:active+.btn-success,
.btn-success:active,
.btn-success.active,
.show>.btn-success.dropdown-toggle {
	background-color: var(--bs-success-hover);
	border-color: var(--bs-success-hover);
}

.btn-check:checked+.btn-success:focus,
.btn-check:active+.btn-success:focus,
.btn-success:active:focus,
.btn-success.active:focus,
.show>.btn-success.dropdown-toggle:focus {
	box-shadow: none;
}

.btn-success:disabled,
.btn-success.disabled {
	background-color: var(--bs-success);
	border-color: var(--bs-success);
}

.btn-info {
	color: var(--bs-white);
	background-color: var(--bs-info);
	border-color: var(--bs-info);
}

.btn-info:hover {
	color: var(--bs-white);
	background-color: var(--bs-info-hover);
	border-color: var(--bs-info-hover);
}

.btn-check:focus+.btn-info,
.btn-info:focus {
	color: var(--bs-white);
	background-color: var(--bs-info-hover);
	border-color: var(--bs-info-hover);
	box-shadow: none;
}

.btn-check:checked+.btn-info,
.btn-check:active+.btn-info,
.btn-info:active,
.btn-info.active,
.show>.btn-info.dropdown-toggle {
	color: var(--bs-white);
	background-color: var(--bs-info-hover);
	border-color: var(--bs-info-hover);
}

.btn-check:checked+.btn-info:focus,
.btn-check:active+.btn-info:focus,
.btn-info:active:focus,
.btn-info.active:focus,
.show>.btn-info.dropdown-toggle:focus {
	box-shadow: none;
}

.btn-info:disabled,
.btn-info.disabled {
	color: var(--bs-white);
	background-color: var(--bs-info);
	border-color: var(--bs-info);
}

.btn-warning {
	color: var(--bs-white);
	background-color: var(--bs-warning);
	border-color: var(--bs-warning);
}

.btn-warning:hover {
	color: var(--bs-white);
	background-color: var(--bs-warning-hover);
	border-color: var(--bs-warning-hover);
}

.btn-check:focus+.btn-warning,
.btn-warning:focus {
	color: var(--bs-white);
	background-color: var(--bs-warning-hover);
	border-color: var(--bs-warning-hover);
	box-shadow: none;
}

.btn-check:checked+.btn-warning,
.btn-check:active+.btn-warning,
.btn-warning:active,
.btn-warning.active,
.show>.btn-warning.dropdown-toggle {
	color: var(--bs-white);
	background-color: var(--bs-warning-hover);
	border-color: var(--bs-warning-hover);
}

.btn-check:checked+.btn-warning:focus,
.btn-check:active+.btn-warning:focus,
.btn-warning:active:focus,
.btn-warning.active:focus,
.show>.btn-warning.dropdown-toggle:focus {
	box-shadow: none;
}

.btn-warning:disabled,
.btn-warning.disabled {
	color: var(--bs-white);
	background-color: var(--bs-warning);
	border-color: var(--bs-warning);
}

.btn-danger {
	background-color: var(--bs-danger);
	border-color: var(--bs-danger);
}

.btn-danger:hover {
	background-color: var(--bs-danger-hover);
	border-color: var(--bs-danger-hover);
}

.btn-check:focus+.btn-danger,
.btn-danger:focus {
	background-color: var(--bs-danger-hover);
	border-color: var(--bs-danger-hover);
	box-shadow: none;
}

.btn-check:checked+.btn-danger,
.btn-check:active+.btn-danger,
.btn-danger:active,
.btn-danger.active,
.show>.btn-danger.dropdown-toggle {
	background-color: var(--bs-danger-hover);
	border-color: var(--bs-danger-hover);
}

.btn-check:checked+.btn-danger:focus,
.btn-check:active+.btn-danger:focus,
.btn-danger:active:focus,
.btn-danger.active:focus,
.show>.btn-danger.dropdown-toggle:focus {
	box-shadow: none;
}

.btn-danger:disabled,
.btn-danger.disabled {
	background-color: var(--bs-danger);
	border-color: var(--bs-danger);
}

.btn-check:focus+.btn-light,
.btn-light:focus {
	box-shadow: none;
}

.btn-check:checked+.btn-light:focus,
.btn-check:active+.btn-light:focus,
.btn-light:active:focus,
.btn-light.active:focus,
.show>.btn-light.dropdown-toggle:focus {
	box-shadow: none;
}

.btn-dark {
    color: var(--bs-white);
    background-color: var(--bs-dark);
    border-color: var(--bs-dark);
}

.btn-dark:hover {
	background-color: var(--bs-dark-hover);
	border-color: var(--bs-dark-hover);
}

.btn-check:focus+.btn-dark,
.btn-dark:focus {
	background-color: var(--bs-dark-hover);
	border-color: var(--bs-dark-hover);
	box-shadow: none;
}

.btn-check:checked+.btn-dark,
.btn-check:active+.btn-dark,
.btn-dark:active,
.btn-dark.active,
.show>.btn-dark.dropdown-toggle {
	background-color: var(--bs-dark-hover);
	border-color: var(--bs-dark-hover);
}

.btn-check:checked+.btn-dark:focus,
.btn-check:active+.btn-dark:focus,
.btn-dark:active:focus,
.btn-dark.active:focus,
.show>.btn-dark.dropdown-toggle:focus {
	box-shadow: none;
}

.btn-purple {
	color: var(--bs-white);
	background-color: var(--bs-purple);
	border-color: var(--bs-purple);
}

.btn-purple:hover {
	color: var(--bs-white);
	background-color: var(--bs-purple-hover);
	border-color: var(--bs-purple-hover);
}

.btn-check:focus+.btn-purple,
.btn-purple:focus {
	color: var(--bs-white);
	background-color: var(--bs-purple-hover);
	border-color: var(--bs-purple-hover);
	box-shadow: none;
}

.btn-check:checked+.btn-purple,
.btn-check:active+.btn-purple,
.btn-purple:active,
.btn-purple.active,
.show>.btn-purple.dropdown-toggle {
	color: var(--bs-white);
	background-color: var(--bs-purple-hover);
	border-color: var(--bs-purple-hover);
}

.btn-check:checked+.btn-purple:focus,
.btn-check:active+.btn-purple:focus,
.btn-purple:active:focus,
.btn-purple.active:focus,
.show>.btn-purple.dropdown-toggle:focus {
	box-shadow: none;
}

.btn-purple:disabled,
.btn-purple.disabled {
	color: var(--bs-white);
	background-color: var(--bs-purple);
	border-color: var(--bs-purple);
}

.btn-pink {
	color: var(--bs-white);
	background-color: var(--bs-pink);
	border-color: var(--bs-pink);
}

.btn-pink:hover {
	color: var(--bs-white);
	background-color: var(--bs-pink-hover);
	border-color: var(--bs-pink-hover);
}

.btn-check:focus+.btn-pink,
.btn-pink:focus {
	color: var(--bs-white);
	background-color: var(--bs-pink-hover);
	border-color: var(--bs-pink-hover);
	box-shadow: none;
}

.btn-check:checked+.btn-pink,
.btn-check:active+.btn-pink,
.btn-pink:active,
.btn-pink.active,
.show>.btn-pink.dropdown-toggle {
	color: var(--bs-white);
	background-color: var(--bs-pink-hover);
	border-color: var(--bs-pink-hover);
}

.btn-check:checked+.btn-pink:focus,
.btn-check:active+.btn-pink:focus,
.btn-pink:active:focus,
.btn-pink.active:focus,
.show>.btn-pink.dropdown-toggle:focus {
	box-shadow: none;
}

.btn-pink:disabled,
.btn-pink.disabled {
	color: var(--bs-white);
	background-color: var(--bs-pink);
	border-color: var(--bs-pink);
}

.btn-cyan {
	color: var(--bs-white);
	background-color: var(--bs-cyan);
	border-color: var(--bs-cyan);
}

.btn-cyan:hover {
	color: var(--bs-white);
	background-color: var(--bs-cyan-hover);
	border-color: var(--bs-cyan-hover);
}

.btn-check:focus+.btn-cyan,
.btn-cyan:focus {
	color: var(--bs-white);
	background-color: var(--bs-cyan-hover);
	border-color: var(--bs-cyan-hover);
	box-shadow: none;
}

.btn-check:checked+.btn-cyan,
.btn-check:active+.btn-cyan,
.btn-cyan:active,
.btn-cyan.active,
.show>.btn-cyan.dropdown-toggle {
	color: var(--bs-white);
	background-color: var(--bs-cyan-hover);
	border-color: var(--bs-cyan-hover);
}

.btn-check:checked+.btn-cyan:focus,
.btn-check:active+.btn-cyan:focus,
.btn-cyan:active:focus,
.btn-cyan.active:focus,
.show>.btn-cyan.dropdown-toggle:focus {
	box-shadow: none;
}

.btn-cyan:disabled,
.btn-cyan.disabled {
	color: var(--bs-white);
	background-color: var(--bs-cyan);
	border-color: var(--bs-cyan);
}

.btn-yellow {
	color: var(--bs-white);
	background-color: var(--bs-yellow);
	border-color: var(--bs-yellow);
}

.btn-yellow:hover {
	color: var(--bs-white);
	background-color: var(--bs-yellow-hover);
	border-color: var(--bs-yellow-hover);
}

.btn-check:focus+.btn-yellow,
.btn-yellow:focus {
	color: var(--bs-white);
	background-color: var(--bs-yellow-hover);
	border-color: var(--bs-yellow-hover);
	box-shadow: none;
}

.btn-check:checked+.btn-yellow,
.btn-check:active+.btn-yellow,
.btn-yellow:active,
.btn-yellow.active,
.show>.btn-yellow.dropdown-toggle {
	color: var(--bs-white);
	background-color: var(--bs-yellow-hover);
	border-color: var(--bs-yellow-hover);
}

.btn-check:checked+.btn-yellow:focus,
.btn-check:active+.btn-yellow:focus,
.btn-yellow:active:focus,
.btn-yellow.active:focus,
.show>.btn-yellow.dropdown-toggle:focus {
	box-shadow: none;
}

.btn-yellow:disabled,
.btn-yellow.disabled {
	color: var(--bs-white);
	background-color: var(--bs-yellow);
	border-color: var(--bs-yellow);
}

.btn-indigo {
	color: var(--bs-white);
	background-color: var(--bs-indigo);
	border-color: var(--bs-indigo);
}

.btn-indigo:hover {
	color: var(--bs-white);
	background-color: var(--bs-indigo-hover);
	border-color: var(--bs-indigo-hover);
}

.btn-check:focus+.btn-indigo,
.btn-indigo:focus {
	color: var(--bs-white);
	background-color: var(--bs-indigo-hover);
	border-color: var(--bs-indigo-hover);
	box-shadow: none;
}

.btn-check:checked+.btn-indigo,
.btn-check:active+.btn-indigo,
.btn-indigo:active,
.btn-indigo.active,
.show>.btn-indigo.dropdown-toggle {
	color: var(--bs-white);
	background-color: var(--bs-indigo-hover);
	border-color: var(--bs-indigo-hover);
}

.btn-check:checked+.btn-indigo:focus,
.btn-check:active+.btn-indigo:focus,
.btn-indigo:active:focus,
.btn-indigo.active:focus,
.show>.btn-indigo.dropdown-toggle:focus {
	box-shadow: none;
}

.btn-indigo:disabled,
.btn-indigo.disabled {
	color: var(--bs-white);
	background-color: var(--bs-indigo);
	border-color: var(--bs-indigo);
}

.btn-brown {
	color: var(--bs-white);
	background-color: var(--bs-brown);
	border-color: var(--bs-brown);
}

.btn-brown:hover {
	color: var(--bs-white);
	background-color: var(--bs-brown-hover);
	border-color: var(--bs-brown-hover);
}

.btn-check:focus+.btn-brown,
.btn-brown:focus {
	color: var(--bs-white);
	background-color: var(--bs-brown-hover);
	border-color: var(--bs-brown-hover);
	box-shadow: none;
}

.btn-check:checked+.btn-brown,
.btn-check:active+.btn-brown,
.btn-brown:active,
.btn-brown.active,
.show>.btn-brown.dropdown-toggle {
	color: var(--bs-white);
	background-color: var(--bs-brown-hover);
	border-color: var(--bs-brown-hover);
}

.btn-check:checked+.btn-brown:focus,
.btn-check:active+.btn-brown:focus,
.btn-brown:active:focus,
.btn-brown.active:focus,
.show>.btn-brown.dropdown-toggle:focus {
	box-shadow: none;
}

.btn-brown:disabled,
.btn-brown.disabled {
	color: var(--bs-white);
	background-color: var(--bs-brown);
	border-color: var(--bs-brown);
}

.btn-teal {
	color: var(--bs-white);
	background-color: var(--bs-teal);
	border-color: var(--bs-teal);
}

.btn-teal:hover {
	color: var(--bs-white);
	background-color: var(--bs-teal-hover);
	border-color: var(--bs-teal-hover);
}

.btn-check:focus+.btn-teal,
.btn-teal:focus {
	color: var(--bs-white);
	background-color: var(--bs-teal-hover);
	border-color: var(--bs-teal-hover);
	box-shadow: none;
}

.btn-check:checked+.btn-teal,
.btn-check:active+.btn-teal,
.btn-teal:active,
.btn-teal.active,
.show>.btn-teal.dropdown-toggle {
	color: var(--bs-white);
	background-color: var(--bs-teal-hover);
	border-color: var(--bs-teal-hover);
}

.btn-check:checked+.btn-teal:focus,
.btn-check:active+.btn-teal:focus,
.btn-teal:active:focus,
.btn-teal.active:focus,
.show>.btn-teal.dropdown-toggle:focus {
	box-shadow: none;
}

.btn-teal:disabled,
.btn-teal.disabled {
	color: var(--bs-white);
	background-color: var(--bs-teal);
	border-color: var(--bs-teal);
}

.btn-outline-primary {
	color: var(--bs-primary);
	border-color: var(--bs-primary);
}

.btn-outline-primary:hover {
	background-color: var(--bs-primary);
	border-color: var(--bs-primary);
}

.btn-check:focus+.btn-outline-primary,
.btn-outline-primary:focus {
	box-shadow: none;
}

.btn-check:checked+.btn-outline-primary,
.btn-check:active+.btn-outline-primary,
.btn-outline-primary:active,
.btn-outline-primary.active,
.btn-outline-primary.dropdown-toggle.show {
	background-color: var(--bs-primary);
	border-color: var(--bs-primary);
}

.btn-check:checked+.btn-outline-primary:focus,
.btn-check:active+.btn-outline-primary:focus,
.btn-outline-primary:active:focus,
.btn-outline-primary.active:focus,
.btn-outline-primary.dropdown-toggle.show:focus {
	box-shadow: none;
}

.btn-outline-primary:disabled,
.btn-outline-primary.disabled {
	color: var(--bs-primary);
}

.btn-check:focus+.btn-outline-secondary,
.btn-outline-secondary:focus {
	box-shadow: none;
}

.btn-check:checked+.btn-outline-secondary:focus,
.btn-check:active+.btn-outline-secondary:focus,
.btn-outline-secondary:active:focus,
.btn-outline-secondary.active:focus,
.btn-outline-secondary.dropdown-toggle.show:focus {
	box-shadow: none;
}

.btn-outline-success {
	color: var(--bs-success);
	border-color: var(--bs-success);
}

.btn-outline-success:hover {
	background-color: var(--bs-success);
	border-color: var(--bs-success);
}

.btn-check:focus+.btn-outline-success,
.btn-outline-success:focus {
	box-shadow: none;
}

.btn-check:checked+.btn-outline-success,
.btn-check:active+.btn-outline-success,
.btn-outline-success:active,
.btn-outline-success.active,
.btn-outline-success.dropdown-toggle.show {
	background-color: var(--bs-success);
	border-color: var(--bs-success);
}

.btn-check:checked+.btn-outline-success:focus,
.btn-check:active+.btn-outline-success:focus,
.btn-outline-success:active:focus,
.btn-outline-success.active:focus,
.btn-outline-success.dropdown-toggle.show:focus {
	box-shadow: none;
}

.btn-outline-success:disabled,
.btn-outline-success.disabled {
	color: var(--bs-success);
}

.btn-outline-info {
	color: var(--bs-info);
	border-color: var(--bs-info);
}

.btn-outline-info:hover {
	color: var(--bs-white);
	background-color: var(--bs-info);
	border-color: var(--bs-info);
}

.btn-check:focus+.btn-outline-info,
.btn-outline-info:focus {
	box-shadow: none;
}

.btn-check:checked+.btn-outline-info,
.btn-check:active+.btn-outline-info,
.btn-outline-info:active,
.btn-outline-info.active,
.btn-outline-info.dropdown-toggle.show {
	color: var(--bs-white);
	background-color: var(--bs-info);
	border-color: var(--bs-info);
}

.btn-check:checked+.btn-outline-info:focus,
.btn-check:active+.btn-outline-info:focus,
.btn-outline-info:active:focus,
.btn-outline-info.active:focus,
.btn-outline-info.dropdown-toggle.show:focus {
	box-shadow: none;
}

.btn-outline-info:disabled,
.btn-outline-info.disabled {
	color: var(--bs-info);
	background-color: transparent;
}

.btn-outline-warning {
	color: var(--bs-warning);
	border-color: var(--bs-warning);
}

.btn-outline-warning:hover {
	color: var(--bs-white);
	background-color: var(--bs-warning);
	border-color: var(--bs-warning);
}

.btn-check:focus+.btn-outline-warning,
.btn-outline-warning:focus {
	box-shadow: none;
}

.btn-check:checked+.btn-outline-warning,
.btn-check:active+.btn-outline-warning,
.btn-outline-warning:active,
.btn-outline-warning.active,
.btn-outline-warning.dropdown-toggle.show {
	color: var(--bs-white);
	background-color: var(--bs-warning);
	border-color: var(--bs-warning);
}

.btn-check:checked+.btn-outline-warning:focus,
.btn-check:active+.btn-outline-warning:focus,
.btn-outline-warning:active:focus,
.btn-outline-warning.active:focus,
.btn-outline-warning.dropdown-toggle.show:focus {
	box-shadow: 0 0 0 0.25rem rgba(var(--bs-warning-rgb), 0.5);
}

.btn-outline-warning:disabled,
.btn-outline-warning.disabled {
	color: var(--bs-warning);
}

.btn-outline-danger {
	color: var(--bs-danger);
	border-color: var(--bs-danger);
}

.btn-outline-danger:hover {
	background-color: var(--bs-danger);
	border-color: var(--bs-danger);
}

.btn-check:focus+.btn-outline-danger,
.btn-outline-danger:focus {
	box-shadow: none;
}

.btn-check:checked+.btn-outline-danger,
.btn-check:active+.btn-outline-danger,
.btn-outline-danger:active,
.btn-outline-danger.active,
.btn-outline-danger.dropdown-toggle.show {
	background-color: var(--bs-danger);
	border-color: var(--bs-danger);
}

.btn-check:checked+.btn-outline-danger:focus,
.btn-check:active+.btn-outline-danger:focus,
.btn-outline-danger:active:focus,
.btn-outline-danger.active:focus,
.btn-outline-danger.dropdown-toggle.show:focus {
	box-shadow: none;
}

.btn-outline-danger:disabled,
.btn-outline-danger.disabled {
	color: var(--bs-danger);
}

.btn-check:focus+.btn-outline-light,
.btn-outline-light:focus {
	box-shadow: none;
}

.btn-check:checked+.btn-outline-light:focus,
.btn-check:active+.btn-outline-light:focus,
.btn-outline-light:active:focus,
.btn-outline-light.active:focus,
.btn-outline-light.dropdown-toggle.show:focus {
	box-shadow: none;
}

.btn-check:focus+.btn-outline-dark,
.btn-outline-dark:focus {
	box-shadow: none;
}

.btn-check:checked+.btn-outline-dark:focus,
.btn-check:active+.btn-outline-dark:focus,
.btn-outline-dark:active:focus,
.btn-outline-dark.active:focus,
.btn-outline-dark.dropdown-toggle.show:focus {
	box-shadow: none;
}

.btn-outline-purple {
	color: var(--bs-purple);
	border-color: var(--bs-purple);
}

.btn-outline-purple:hover {
	background-color: var(--bs-purple);
	border-color: var(--bs-purple);
    color: var(--bs-white);
}

.btn-check:focus+.btn-outline-purple,
.btn-outline-purple:focus {
	box-shadow: none;
}

.btn-check:checked+.btn-outline-purple,
.btn-check:active+.btn-outline-purple,
.btn-outline-purple:active,
.btn-outline-purple.active,
.btn-outline-purple.dropdown-toggle.show {
	background-color: var(--bs-purple);
	border-color: var(--bs-purple);
}

.btn-check:checked+.btn-outline-purple:focus,
.btn-check:active+.btn-outline-purple:focus,
.btn-outline-purple:active:focus,
.btn-outline-purple.active:focus,
.btn-outline-purple.dropdown-toggle.show:focus {
	box-shadow: none;
}

.btn-outline-purple:disabled,
.btn-outline-purple.disabled {
	color: var(--bs-purple);
}

.btn-outline-pink {
	color: var(--bs-pink);
	border-color: var(--bs-pink);
}

.btn-outline-pink:hover {
	background-color: var(--bs-pink);
	border-color: var(--bs-pink);
    color: var(--bs-white);
}

.btn-check:focus+.btn-outline-pink,
.btn-outline-pink:focus {
	box-shadow: none;
}

.btn-check:checked+.btn-outline-pink,
.btn-check:active+.btn-outline-pink,
.btn-outline-pink:active,
.btn-outline-pink.active,
.btn-outline-pink.dropdown-toggle.show {
	background-color: var(--bs-pink);
	border-color: var(--bs-pink);
}

.btn-check:checked+.btn-outline-pink:focus,
.btn-check:active+.btn-outline-pink:focus,
.btn-outline-pink:active:focus,
.btn-outline-pink.active:focus,
.btn-outline-pink.dropdown-toggle.show:focus {
	box-shadow: none;
}

.btn-outline-pink:disabled,
.btn-outline-pink.disabled {
	color: var(--bs-pink);
}

.btn-outline-cyan {
	color: var(--bs-cyan);
	border-color: var(--bs-cyan);
}

.btn-outline-cyan:hover {
	background-color: var(--bs-cyan);
	border-color: var(--bs-cyan);
    color: var(--bs-white);
}

.btn-check:focus+.btn-outline-cyan,
.btn-outline-cyan:focus {
	box-shadow: none;
}

.btn-check:checked+.btn-outline-cyan,
.btn-check:active+.btn-outline-cyan,
.btn-outline-cyan:active,
.btn-outline-cyan.active,
.btn-outline-cyan.dropdown-toggle.show {
	background-color: var(--bs-cyan);
	border-color: var(--bs-cyan);
}

.btn-check:checked+.btn-outline-cyan:focus,
.btn-check:active+.btn-outline-cyan:focus,
.btn-outline-cyan:active:focus,
.btn-outline-cyan.active:focus,
.btn-outline-cyan.dropdown-toggle.show:focus {
	box-shadow: none;
}

.btn-outline-cyan:disabled,
.btn-outline-cyan.disabled {
	color: var(--bs-cyan);
}

.btn-outline-yellow {
	color: var(--bs-yellow);
	border-color: var(--bs-yellow);
}

.btn-outline-yellow:hover {
	background-color: var(--bs-yellow);
	border-color: var(--bs-yellow);
    color: var(--bs-white);
}

.btn-check:focus+.btn-outline-yellow,
.btn-outline-yellow:focus {
	box-shadow: none;
}

.btn-check:checked+.btn-outline-yellow,
.btn-check:active+.btn-outline-yellow,
.btn-outline-yellow:active,
.btn-outline-yellow.active,
.btn-outline-yellow.dropdown-toggle.show {
	background-color: var(--bs-yellow);
	border-color: var(--bs-yellow);
}

.btn-check:checked+.btn-outline-yellow:focus,
.btn-check:active+.btn-outline-yellow:focus,
.btn-outline-yellow:active:focus,
.btn-outline-yellow.active:focus,
.btn-outline-yellow.dropdown-toggle.show:focus {
	box-shadow: none;
}

.btn-outline-yellow:disabled,
.btn-outline-yellow.disabled {
	color: var(--bs-yellow);
}

.btn-outline-brown {
	color: var(--bs-brown);
	border-color: var(--bs-brown);
}

.btn-outline-brown:hover {
	background-color: var(--bs-brown);
	border-color: var(--bs-brown);
    color: var(--bs-white);
}

.btn-check:focus+.btn-outline-brown,
.btn-outline-brown:focus {
	box-shadow: none;
}

.btn-check:checked+.btn-outline-brown,
.btn-check:active+.btn-outline-brown,
.btn-outline-brown:active,
.btn-outline-brown.active,
.btn-outline-brown.dropdown-toggle.show {
	background-color: var(--bs-brown);
	border-color: var(--bs-brown);
}

.btn-check:checked+.btn-outline-brown:focus,
.btn-check:active+.btn-outline-brown:focus,
.btn-outline-brown:active:focus,
.btn-outline-brown.active:focus,
.btn-outline-brown.dropdown-toggle.show:focus {
	box-shadow: none;
}

.btn-outline-brown:disabled,
.btn-outline-brown.disabled {
	color: var(--bs-brown);
}

.btn-outline-indigo {
	color: var(--bs-indigo);
	border-color: var(--bs-indigo);
}

.btn-outline-indigo:hover {
	background-color: var(--bs-indigo);
	border-color: var(--bs-indigo);
    color: var(--bs-white);
}

.btn-check:focus+.btn-outline-indigo,
.btn-outline-indigo:focus {
	box-shadow: none;
}

.btn-check:checked+.btn-outline-indigo,
.btn-check:active+.btn-outline-indigo,
.btn-outline-indigo:active,
.btn-outline-indigo.active,
.btn-outline-indigo.dropdown-toggle.show {
	background-color: var(--bs-indigo);
	border-color: var(--bs-indigo);
}

.btn-check:checked+.btn-outline-indigo:focus,
.btn-check:active+.btn-outline-indigo:focus,
.btn-outline-indigo:active:focus,
.btn-outline-indigo.active:focus,
.btn-outline-indigo.dropdown-toggle.show:focus {
	box-shadow: none;
}

.btn-outline-indigo:disabled,
.btn-outline-indigo.disabled {
	color: var(--bs-indigo);
}

.btn-outline-teal {
	color: var(--bs-teal);
	border-color: var(--bs-teal);
}

.btn-outline-teal:hover {
	background-color: var(--bs-teal);
	border-color: var(--bs-teal);
    color: var(--bs-white);
}

.btn-check:focus+.btn-outline-teal,
.btn-outline-teal:focus {
	box-shadow: none;
}

.btn-check:checked+.btn-outline-teal,
.btn-check:active+.btn-outline-teal,
.btn-outline-teal:active,
.btn-outline-teal.active,
.btn-outline-teal.dropdown-toggle.show {
	background-color: var(--bs-teal);
	border-color: var(--bs-teal);
}

.btn-check:checked+.btn-outline-teal:focus,
.btn-check:active+.btn-outline-teal:focus,
.btn-outline-teal:active:focus,
.btn-outline-teal.active:focus,
.btn-outline-teal.dropdown-toggle.show:focus {
	box-shadow: none;
}

.btn-outline-teal:disabled,
.btn-outline-teal.disabled {
	color: var(--bs-teal);
}

.btn-lg,
.btn-group-lg>.btn {
	padding: 0.5rem 1rem;
	font-size: 1.25rem;
}

.btn-sm,
.btn-group-sm>.btn {
	padding: 0.25rem 0.5rem;
	font-size: 0.875rem;
}

.btn-sm+.dropdown-toggle-split,
.btn-group-sm>.btn+.dropdown-toggle-split {
	padding-right: 0.375rem;
	padding-left: 0.375rem;
}

.btn-lg+.dropdown-toggle-split,
.btn-group-lg>.btn+.dropdown-toggle-split {
	padding-right: 0.75rem;
	padding-left: 0.75rem;
}

.btn-round {
	-webkit-border-radius: 10rem;
	border-radius: 10rem;
}

.btn-label {
	position: relative;
	padding-left: 52px;
	overflow: hidden;
}

.btn-label label {
	position: absolute;
	left: 0;
	top: 0;
	bottom: 0;
	width: 36px;
	line-height: 1.5;
	padding-top: 4px;
	padding-bottom: 5px;
	background-color: rgba(var(--bs-dark-rgb), 0.1);
	cursor: pointer;
	margin-bottom: 0;
}

.btn-label label i {
	font-size: 16px;
}

/* 输入框 & 下拉选择 */
.form-control,
.form-select {
	font-size: 0.875rem;
	color: var(--bs-body-color);
	border-color: var(--bs-gray-300);
	padding: 0.4695rem 0.75rem;
	-webkit-border-radius: 0.125rem;
	border-radius: 0.125rem;
}

.form-select {
    padding: 0.4695rem 2.25rem 0.4695rem 0.75rem;
}

.form-control-plaintext:focus {
    box-shadow: none;
    outline: none;
}

.form-control:focus,
.form-select:focus {
	color: var(--bs-body-color);
	border-color: var(--bs-primary);
	box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
}

.form-control-lg,
.form-select-lg {
	font-size: 1.125rem;
}

.form-control-sm,
.form-select-sm {
	font-size: 0.625rem;
}

/* 复选框 */
.form-check {
	padding-left: 1.75em;
}

.form-check .form-check-input {
	margin-left: -1.75em;
}

.form-check-input[type=checkbox] {
	border-radius: 0.3em;
}

/* 单选框 */
.form-check-input {
	width: 1.286em;
	height: 1.286em;
}

.form-check-input:focus {
	border-color: var(--bs-primary);
	box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
}

.form-check-input:checked,
.form-check-input[type=checkbox]:indeterminate {
	background-color: var(--bs-primary);
	border-color: var(--bs-primary);
}

/* 开关 */
.form-switch {
	padding-left: 2.5em;
}

.form-switch .form-check-input {
	margin-left: -2.5em;
}

.form-switch .form-check-input {
	border-radius: 2em;
}

/* 遗留的问题，这类图像背景的颜色怎么替换成变量 */
.form-switch .form-check-input:focus {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23007bff'/%3e%3c/svg%3e");
}

.form-switch .form-check-blue.form-check-input:focus {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%2348b0f7'/%3e%3c/svg%3e");
}

.form-switch .form-check-indigo.form-check-input:focus {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%236610f2'/%3e%3c/svg%3e");
}

.form-switch .form-check-purple.form-check-input:focus {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23926dde'/%3e%3c/svg%3e");
}

.form-switch .form-check-pink.form-check-input:focus {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23f96197'/%3e%3c/svg%3e");
}

.form-switch .form-check-red.form-check-input:focus {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23f44236'/%3e%3c/svg%3e");
}

.form-switch .form-check-orange.form-check-input:focus {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23faa64b'/%3e%3c/svg%3e");
}

.form-switch .form-check-yellow.form-check-input:focus {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fcc525'/%3e%3c/svg%3e");
}

.form-switch .form-check-green.form-check-input:focus {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%2315c377'/%3e%3c/svg%3e");
}

.form-switch .form-check-brown.form-check-input:focus {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%238d6658'/%3e%3c/svg%3e");
}

.form-switch .form-check-gray.form-check-input:focus {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%236c757d'/%3e%3c/svg%3e");
}

.form-switch .form-check-dark.form-check-input:focus {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23212529'/%3e%3c/svg%3e");
}

.form-switch .form-check-teal.form-check-input:focus {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%2333cabb'/%3e%3c/svg%3e") !important;
}

.form-switch .form-check-input:checked {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e") !important;
}

/* 滑块（范围） */
.form-range:focus::-webkit-slider-thumb {
	box-shadow: 0 0 0 1px var(--bs-white), 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
}

.form-range:focus::-moz-range-thumb {
	box-shadow: 0 0 0 1px var(--bs-white), 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
}

.form-range::-webkit-slider-thumb {
	background-color: var(--bs-primary);
}

.form-range::-webkit-slider-thumb:active {
	background-color: rgba(var(--bs-primary-rgb), 0.375);
}

.form-range::-webkit-slider-runnable-track {
	background-color: var(--bs-gray-300);
}

.form-range::-moz-range-thumb {
	background-color: var(--bs-primary);
}

.form-range::-moz-range-thumb:active {
	background-color: rgba(var(--bs-primary-rgb), 0.375);
}

.form-range::-moz-range-track {
	background-color: var(--bs-gray-300);
}

/* input其他着色 */
.form-check-blue.form-check-input:checked,
.form-check-blue.form-check-input[type=checkbox]:indeterminate {
	background-color: var(--bs-info)!important;
	border-color: var(--bs-info)!important;
}

.form-check-blue.form-check-input:focus {
	border-color: var(--bs-info);
	box-shadow: 0 0 0 0.25rem rgba(var(--bs-info-rgb), 0.25);
}

.form-check-indigo.form-check-input:checked,
.form-check-indigo.form-check-input[type=checkbox]:indeterminate {
	background-color: var(--bs-indigo)!important;
	border-color: var(--bs-indigo)!important;
}

.form-check-indigo.form-check-input:focus {
	border-color: var(--bs-indigo);
	box-shadow: 0 0 0 0.25rem rgba(var(--bs-indigo-rgb), 0.25);
}

.form-check-purple.form-check-input:checked,
.form-check-purple.form-check-input[type=checkbox]:indeterminate {
	background-color: var(--bs-purple)!important;
	border-color: var(--bs-purple)!important;
}

.form-check-purple.form-check-input:focus {
	border-color: var(--bs-purple);
	box-shadow: 0 0 0 0.25rem rgba(var(--bs-purple-rgb), 0.25);
}

.form-check-pink.form-check-input:checked,
.form-check-pink.form-check-input[type=checkbox]:indeterminate {
	background-color: var(--bs-pink)!important;
	border-color: var(--bs-pink)!important;
}

.form-check-pink.form-check-input:focus {
	border-color: var(--bs-pink);
	box-shadow: 0 0 0 0.25rem rgba(var(--bs-pink-rgb), 0.25);
}

.form-check-red.form-check-input:checked,
.form-check-red.form-check-input[type=checkbox]:indeterminate {
	background-color: var(--bs-danger)!important;
	border-color: var(--bs-danger)!important;
}

.form-check-red.form-check-input:focus {
	border-color: var(--bs-danger);
	box-shadow: 0 0 0 0.25rem rgba(var(--bs-danger-rgb), 0.25);
}

.form-check-orange.form-check-input:checked,
.form-check-orange.form-check-input[type=checkbox]:indeterminate {
	background-color: var(--bs-warning)!important;
	border-color: var(--bs-warning)!important;
}

.form-check-orange.form-check-input:focus {
	border-color: var(--bs-warning);
	box-shadow: 0 0 0 0.25rem rgba(var(--bs-warning-rgb), 0.25);
}

.form-check-yellow.form-check-input:checked,
.form-check-yellow.form-check-input[type=checkbox]:indeterminate {
	background-color: var(--bs-yellow)!important;
	border-color: var(--bs-yellow)!important;
}

.form-check-yellow.form-check-input:focus {
	border-color: var(--bs-yellow);
	box-shadow: 0 0 0 0.25rem rgba(var(--bs-yellow-rgb), 0.25);
}

.form-check-green.form-check-input:checked,
.form-check-green.form-check-input[type=checkbox]:indeterminate {
	background-color: var(--bs-success)!important;
	border-color: var(--bs-success)!important;
}

.form-check-green.form-check-input:focus {
	border-color: var(--bs-success);
	box-shadow: 0 0 0 0.25rem rgba(var(--bs-success-rgb), 0.25);
}

.form-check-cyan.form-check-input:checked,
.form-check-cyan.form-check-input[type=checkbox]:indeterminate {
	background-color: var(--bs-cyan)!important;
	border-color: var(--bs-cyan)!important;
}

.form-check-cyan.form-check-input:focus {
	border-color: var(--bs-cyan);
	box-shadow: 0 0 0 0.25rem rgba(var(--bs-cyan-rgb), 0.25);
}

.form-check-brown.form-check-input:checked,
.form-check-brown.form-check-input[type=checkbox]:indeterminate {
	background-color: var(--bs-brown)!important;
	border-color: var(--bs-brown)!important;
}

.form-check-brown.form-check-input:focus {
	border-color: var(--bs-brown);
	box-shadow: 0 0 0 0.25rem rgba(var(--bs-brown-rgb), 0.25);
}

.form-check-gray.form-check-input:checked,
.form-check-gray.form-check-input[type=checkbox]:indeterminate {
	background-color: var(--bs-gray)!important;
	border-color: var(--bs-gray)!important;
}

.form-check-gray.form-check-input:focus {
	border-color: var(--bs-gray);
	box-shadow: 0 0 0 0.25rem rgba(var(--bs-secondary-rgb), 0.25);
}

.form-check-dark.form-check-input:checked,
.form-check-dark.form-check-input[type=checkbox]:indeterminate {
	background-color: var(--bs-dark)!important;
	border-color: var(--bs-dark)!important;
}

.form-check-dark.form-check-input:focus {
	border-color: var(--bs-dark);
	box-shadow: 0 0 0 0.25rem rgba(var(--bs-dark-rgb), 0.25);
}

.form-check-teal.form-check-input:checked,
.form-check-teal.form-check-input[type=checkbox]:indeterminate {
	background-color: var(--bs-teal)!important;
	border-color: var(--bs-teal)!important;
}

.form-check-teal.form-check-input:focus {
	border-color: var(--bs-teal);
	box-shadow: 0 0 0 0.25rem rgba(var(--bs-teal-rgb), 0.25);
}

/* 输入组 */
.input-group-text {
	color: var(--bs-body-color);
	border-color: var(--bs-gray-300);
	background-color: var(--bs-gray-100);
	border-radius: 0.125rem;
	font-size: 14px;
}

/* file控件 */
.form-control::-webkit-file-upload-button,
.form-control::file-selector-button {
	background-color: var(--bs-gray-100);
	padding-top: 0.55rem;
	padding-bottom: 0.55rem;
	margin-top: -0.55rem;
	margin-bottom: -0.55rem;
}

.form-control:hover:not(:disabled):not([readonly])::-webkit-file-upload-button,
.form-control:hover:not(:disabled):not([readonly])::file-selector-button {
	background-color: var(--bs-gray-200);
}

.form-control-sm::-webkit-file-upload-button,
.form-control-sm::file-selector-button {
	margin-left: -0.75rem;
}

/* 引用 */
.blockquote {
	font-size: 1rem;
}

/* 徽章 */
.badge {
	-webkit-border-radius: 0.125rem;
	border-radius: 0.125rem;
	font-weight: 300;
}

[class*='badge-outline-'] {
	position: relative;
	border: 1px solid var(--bs-gray-300);
	color: var(--bs-body-color);
	font-size: 12px;
}

[class*='badge-outline-']:before {
	content: "";
	margin-right: 5px;
	width: 8px;
	height: 8px;
	display: inline-block;
	-wekit-border-radius: 50%;
	border-radius: 50%;
}

.badge-outline-primary::before {
	background-color: var(--bs-primary);
}

.badge-outline-secondary::before {
	background-color: var(--bs-secondary);
}

.badge-outline-success::before {
	background-color: var(--bs-success);
}

.badge-outline-danger::before {
	background-color: var(--bs-danger);
}

.badge-outline-warning::before {
	background-color: var(--bs-warning);
}

.badge-outline-info::before {
	background-color: var(--bs-info);
}

.badge-outline-light::before {
	background-color: var(--bs-light);
}

.badge-outline-dark::before {
	background-color: var(--bs-dark);
}

.badge-outline-indigo::before {
	background-color: var(--bs-indigo);
}

.badge-outline-purple::before {
	background-color: var(--bs-purple);
}

.badge-outline-pink::before {
	background-color: var(--bs-pink);
}

.badge-outline-cyan::before {
	background-color: var(--bs-cyan);
}

.badge-outline-yellow::before {
	background-color: var(--bs-yellow);
}

.badge-outline-brown::before {
	background-color: var(--bs-brown);
}

.badge-outline-teal::before {
	background-color: var(--bs-teal);
}

/* 表格 */
.table {
	--bs-table-striped-color: #495057;
	--bs-table-striped-bg: rgba(249, 250, 251, 0.75);
	--bs-table-active-color: #495057;
	--bs-table-active-bg: rgba(var(--bs-black-rgb), .0375);
	--bs-table-hover-color: #495057;
	--bs-table-hover-bg: rgba(241, 251, 251, 0.75);
	color: var(--bs-body-color);
	border-color: var(--bs-border-color);
}

.table> :not(:first-child) {
	border-top-color: var(--bs-border-color) !important;
}

.table-primary {
	--bs-table-bg: #d5f5f3 !important;
	--bs-table-striped-bg: #cef3f1 !important;
	--bs-table-striped-color: #495057 !important;
	--bs-table-active-bg: #bdefec !important;
	--bs-table-active-color: #495057 !important;
	--bs-table-hover-bg: #c6f1ef !important;
	--bs-table-hover-color: #495057 !important;
	color: var(--bs-body-color) !important;
	border-color: var(--bs-border-color) !important;
}

.table-secondary {
	--bs-table-bg: #e2e3e5 !important;
	--bs-table-striped-bg: #dddee1 !important;
	--bs-table-striped-color: #495057 !important;
	--bs-table-active-bg: #d7d9dc !important;
	--bs-table-active-color: #495057 !important;
	--bs-table-hover-bg: #d0d3d6 !important;
	--bs-table-hover-color: #495057 !important;
	color: var(--bs-body-color) !important;
	border-color: var(--bs-border-color) !important;
}

.table-success {
	--bs-table-bg: #d4edda !important;
	--bs-table-striped-bg: #cdead4 !important;
	--bs-table-striped-color: #495057 !important;
	--bs-table-active-bg: #c5e6cd !important;
	--bs-table-active-color: #495057 !important;
	--bs-table-hover-bg: #bce2c5 !important;
	--bs-table-hover-color: #495057 !important;
	color: var(--bs-body-color) !important;
	border-color: var(--bs-border-color) !important;
}

.table-info {
	--bs-table-bg: #cce5ff !important;
	--bs-table-striped-bg: #c4e1ff !important;
	--bs-table-striped-color: #495057 !important;
	--bs-table-active-bg: #bbdcff !important;
	--bs-table-active-color: #495057 !important;
	--bs-table-hover-bg: #b1d6ff !important;
	--bs-table-hover-color: #495057 !important;
	color: var(--bs-body-color) !important;
	border-color: var(--bs-border-color) !important;
}

.table-warning {
	--bs-table-striped-bg: #fff1c5 !important;
	--bs-table-striped-color: #495057 !important;
	--bs-table-active-bg: #ffefbc !important;
	--bs-table-active-color: #495057 !important;
	--bs-table-hover-bg: #ffecb2 !important;
	--bs-table-hover-color: #495057 !important;
	color: var(--bs-body-color) !important;
	border-color: var(--bs-border-color) !important;
}

.table-danger {
	--bs-table-striped-bg: #f7d0d4 !important;
	--bs-table-striped-color: #495057 !important;
	--bs-table-active-bg: #f6c8cd !important;
	--bs-table-active-color: #495057 !important;
	--bs-table-hover-bg: #f4bfc5 !important;
	--bs-table-hover-color: #495057 !important;
	color: var(--bs-body-color) !important;
	border-color: var(--bs-border-color) !important;
}

.table-light {
	--bs-table-bg: #f7fafc !important;
	--bs-table-striped-bg: #f6f9fc !important;
	--bs-table-striped-color: #495057 !important;
	--bs-table-active-bg: #f4f8fc !important;
	--bs-table-active-color: #495057 !important;
	--bs-table-hover-bg: #f2f7fc !important;
	--bs-table-hover-color: #495057 !important;
	color: var(--bs-body-color) !important;
	border-color: var(--bs-border-color) !important;
}

.table-dark {
	--bs-table-striped-bg: #1c1f22 !important;
	--bs-table-striped-color: #fff !important;
	--bs-table-active-bg: #171a1c !important;
	--bs-table-active-color: #fff !important;
	--bs-table-hover-bg: #131617 !important;
	--bs-table-hover-color: #fff !important;
	color: var(--bs-white) !important;
	border-color: var(--bs-border-color) !important;
}

/* 警告框 */
.alert {
    border-radius: 0.125rem;
}

.alert .alert-link:hover {
	text-decoration: underline;
}

.alert p:last-child {
	margin-bottom: 0px;
}

.alert .mdi:before {
	vertical-align: middle;
}

/* 标注 */
.callout {
	padding: 0.75rem 1rem;
	border: 1px solid var(--bs-border-color);
	border-left: 3px solid transparent;
}

.callout-primary {
	border-left-color: var(--bs-primary);
}

.callout-success {
	border-left-color: var(--bs-success);
}

.callout-info {
	border-left-color: var(--bs-info);
}

.callout-warning {
	border-left-color: var(--bs-warning);
}

.callout-danger {
	border-left-color: var(--bs-danger);
}

.callout p:last-child {
	margin-bottom: 0px;
}

/* 背景色 */
.bg-indigo {
	--bs-bg-opacity: 1;
	background-color: rgba(var(--bs-indigo-rgb), var(--bs-bg-opacity)) !important;
}

.bg-purple {
	--bs-bg-opacity: 1;
	background-color: rgba(var(--bs-purple-rgb), var(--bs-bg-opacity)) !important;
}

.bg-pink {
	--bs-bg-opacity: 1;
	background-color: rgba(var(--bs-pink-rgb), var(--bs-bg-opacity)) !important;
}

.bg-yellow {
	--bs-bg-opacity: 1;
	background-color: rgba(var(--bs-yellow-rgb), var(--bs-bg-opacity)) !important;
}

.bg-cyan {
	--bs-bg-opacity: 1;
	background-color: rgba(var(--bs-cyan-rgb), var(--bs-bg-opacity)) !important;
}

.bg-brown {
	--bs-bg-opacity: 1;
	background-color: rgba(var(--bs-brown-rgb), var(--bs-bg-opacity)) !important;
}

.bg-teal {
	--bs-bg-opacity: 1;
	background-color: rgba(var(--bs-teal-rgb), var(--bs-bg-opacity)) !important;
}

/* 链接颜色 */
.link-primary {
    color: var(--bs-primary) !important;
}
.link-secondary {
    color: var(--bs-secondary) !important;
}
.link-success {
    color: var(--bs-success) !important;
}
.link-danger {
    color: var(--bs-danger) !important;
}
.link-warning {
    color: var(--bs-warning) !important;
}
.link-info {
    color: var(--bs-info) !important;
}
.link-light {
    color: var(--bs-light) !important;
}
.link-dark {
    color: var(--bs-dark) !important;
}

/* 边框颜色 */
.border-primary {
	border-color: var(--bs-primary) !important;
}

.border-success {
	border-color: var(--bs-success) !important;
}

.border-info {
	border-color: var(--bs-info) !important;
}

.border-warning {
	border-color: var(--bs-warning) !important;
}

.border-danger {
	border-color: var(--bs-danger) !important;
}

/* 文本颜色 */
.text-indigo {
	--bs-text-opacity: 1;
	color: rgba(var(--bs-indigo-rgb), var(--bs-text-opacity)) !important;
}

.text-purple {
	--bs-text-opacity: 1;
	color: rgba(var(--bs-purple-rgb), var(--bs-text-opacity)) !important;
}

.text-pink {
	--bs-text-opacity: 1;
	color: rgba(var(--bs-pink-rgb), var(--bs-text-opacity)) !important;
}

.text-yellow {
	--bs-text-opacity: 1;
	color: rgba(var(--bs-yellow-rgb), var(--bs-text-opacity)) !important;
}

.text-cyan {
	--bs-text-opacity: 1;
	color: rgba(var(--bs-cyan-rgb), var(--bs-text-opacity)) !important;
}

.text-brown {
	--bs-text-opacity: 1;
	color: rgba(var(--bs-brown-rgb), var(--bs-text-opacity)) !important;
}

.text-muted {
	--bs-text-opacity: 1;
	color: rgba(var(--bs-secondary-rgb), var(--bs-text-opacity)) !important;
}

/* 列表组 */
.list-group-item {
	color: var(--bs-body-color);
	border-color: var(--bs-border-color);
}

.list-group-item.active {
	border-color: var(--bs-primary);
	background-color: var(--bs-primary);
}

/* 分页 */
.page-link {
	margin-right: 6px;
	border-radius: 0.125rem !important;
	color: var(--bs-gray);
    white-space: nowrap;
}

.page-link:hover {
	color: var(--bs-body-color);
	background-color: var(--bs-light-hover);
}

.page-link:focus {
	color: var(--bs-body-color);
	box-shadow: none;
	background-color: var(--bs-light-hover);
}

.page-item.active .page-link {
	background-color: var(--bs-primary);
	border-color: var(--bs-primary);
}

.page-item.disabled .page-link {
	opacity: .6;
}

.page-item:last-child .page-link {
	margin-right: 0px;
}

.pagination-sm .page-item .page-link {
	border-radius: 0.1rem !important;
}

.pagination-lg .page-item .page-link {
	border-radius: 0.15rem !important;
}

/* offcanvas */
.offcanvas-start,
.offcanvas-end,
.offcanvas-top,
.offcanvas-bottom {
	border-color: var(--bs-border-color)
}

/* popover */
.popover {
	border-color: var(--bs-border-color);
	border-radius: .125rem;
}

.bs-popover-top>.popover-arrow::before,
.bs-popover-auto[data-popper-placement^=top]>.popover-arrow::before {
	border-top-color: var(--bs-border-color);
}

.bs-popover-end>.popover-arrow::before,
.bs-popover-auto[data-popper-placement^=right]>.popover-arrow::before {
	border-right-color: var(--bs-border-color);
}

.bs-popover-bottom>.popover-arrow::before,
.bs-popover-auto[data-popper-placement^=bottom]>.popover-arrow::before {
	border-bottom-color: var(--bs-border-color);
}

.bs-popover-start>.popover-arrow::before,
.bs-popover-auto[data-popper-placement^=left]>.popover-arrow::before {
	border-left-color: var(--bs-border-color);
}

.popover-header {
	background-color: var(--bs-light);
	border-bottom-color: var(--bs-border-color);
}

.bs-popover-bottom .popover-header::before,
.bs-popover-auto[data-popper-placement^=bottom] .popover-header::before {
	border-bottom-color: var(--bs-light);
}

.popover-body {
	color: var(--bs-body-color);
}

/* 下拉菜单 */
.dropdown-menu {
	font-size: 14px;
	border-radius: 0;
	border: none;
	-webkit-box-shadow: 0 3px 8px rgb(var(--bs-dark-rgb), .1), 0 0 1px rgb(var(--bs-dark-rgb), .15);
	box-shadow: 0 3px 8px rgb(var(--bs-dark-rgb), .1), 0 0 1px rgb(var(--bs-dark-rgb), .15);
}

.dropdown-item,
.dropdown-header,
.dropdown-item-text {
	padding: 8px 15px;
}

.dropleft .dropdown-toggle::before {
	vertical-align: 1px;
}

.dropdown-item:hover,
.dropdown-item:focus {
	background-color: var(--bs-light-hover);
}

.dropdown-item.active,
.dropdown-item:active {
	background-color: var(--bs-primary);
}

.dropdown-divider {
	border-top-color: var(--bs-border-color);
}

hr.dropdown-divider {
	background-color: var(--bs-border-color);
	border-top-color: var(--bs-border-color);
}

/* 标签页 */
.nav-tabs {
	border-color: var(--bs-border-color);
	margin-bottom: 1rem;
}

.nav-tabs .nav-link {
	color: var(--bs-body-color);
	border-top: 0px;
	border-left: 0px;
	border-right: 0px;
	border-radius: 0px;
}

.nav-tabs .nav-link:hover,
.nav-tabs .nav-link:focus {
	color: var(--bs-body-color);
	border-color: var(--bs-primary);
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
	color: var(--bs-body-color);
	background-color: var(--bs-white);
	border-color: var(--bs-primary);
}
.tab-content > .tab-pane {
    overflow: inherit!important;
}

/* 卡片 */
.card {
	border: none;
	border-radius: var(--border-radius-mini);
    margin-bottom: 1rem;
	-webkit-transition: .5s;
	-moz-transition: .5s;
	transition: .5s;
	-webkit-box-shadow: 0 0 4px rgba(var(--bs-dark-rgb), 0.075);
	-moz-box-shadow: 0 0 4px rgba(var(--bs-dark-rgb), 0.075);
	box-shadow: 0 0 4px rgba(var(--bs-dark-rgb), 0.075);
}

.card[class*="border-"] {
	border-width: 1px;
	border-style: solid;
}

.card-header {
	padding: 0.75rem 1.25rem;
	background-color: transparent;
	border-bottom: 1px solid var(--bs-border-color);
}

.card-header .card-title {
	margin-bottom: 0;
}

.card-header div.card-title {
	font-size: 16px;
}

.card-header>*:last-child {
	margin-right: 0;
}

.card>.card-header+.callout {
	background-color: var(--bs-light-hover);
	border-top-width: 0px;
	border-right-width: 0px;
	border-bottom-width: 0px;
}

.card-body {
	padding: 1.25rem;
}

.card-body>*:last-child {
	margin-bottom: 0;
}

.card-body>p:last-child {
	margin-bottom: 0;
}

.card-bordered {
	border: 1px solid var(--bs-gray-200);
}

.card-shadowed,
.card-hover-shadow:hover {
	-webkit-box-shadow: 0 0 25px rgba(var(--bs-dark-rgb), 0.375);
	box-shadow: 0 0 25px rgba(var(--bs-dark-rgb), 0.375);
}

.card-footer {
	background-color: var(--bs-light);
	border-top: 1px solid var(--bs-border-color);
	padding: 0.75rem 1rem;
}
.card-footer:last-child {
    border-radius: 0 0 var(--border-radius-mini) var(--border-radius-mini);
}

.card>.list-group {
	border-bottom: 1px solid var(--bs-border-color);
}

.card-header-tabs {
	margin-bottom: -0.75rem;
}

.card-actions {
	list-style-type: none;
	padding-left: 0;
	margin-bottom: 0;
	display: -webkit-box;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: reverse;
	flex-direction: row-reverse;
}

.card-actions>li>a {
	display: inline-block;
	padding: 0 4px;
	margin: 0 4px;
	color: var(--bs-secondary);
	opacity: .8;
	-webkit-transition: 0.3s linear;
	transition: 0.3s linear;
}

.card-actions>li:first-child>a {
	margin-right: 0;
}

.card-actions>li>a>i {
	height: 20px;
	text-align: center;
	vertical-align: middle;
	cursor: pointer;
	line-height: 20px;
}

.card-actions>li>a>.mdi {
	font-size: 18px;
}

.card-actions>li.dropdown {
	line-height: 26px;
}

.card-fullscreen {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	margin-bottom: 0 !important;
	z-index: 998;
}

.card-fullscreen .card-btn-fullscreen {
	color: var(--bs-primary);
}

/* 通用导航 */
.nav-link {
	color: var(--bs-primary);
}

.nav-link:hover,
.nav-link:focus {
	color: var(--bs-primary-hover);
}

.nav-pills .nav-link {
	border-radius: 0.125rem;
}

.nav-pills .nav-link.active,
.nav-pills .show>.nav-link {
	background-color: var(--bs-primary);
}

/* toast */
.toast {
	border-color: var(--bs-border-color);
	box-shadow: 0 0.125rem 0.75rem rgba(var(--bs-dark-rgb), .0375);
	border-radius: 0.125rem;
}

.toast-header i {
	line-height: 1em;
}

/* 模态框 */
.modal-content,
.modal-header,
.modal-footer {
	border-color: var(--bs-border-color);
}

/* 进度条 */
.progress {
	height: .75rem;
	border-radius: .15rem;
}

.progress-bar {
	background-color: var(--bs-primary);
}

.progress-xs {
	height: .25rem;
}

.progress-sm {
	height: .5rem;
}

/* 工具提示 */
.tooltip-inner {
	border-radius: 0.125rem;
}

/* 加载动画 */
.spinner-border {
	border-width: .125em;
}

/* 手风琴折叠效果 */
.accordion-item {
	border-color: var(--bs-border-color);
}

.accordion-button:focus {
	border-color: inherit;
	box-shadow: none;
}

.accordion-button:not(.collapsed) {
	background-color: rgba(var(--bs-primary-rgb), .075);
	color: var(--bs-primary);
	box-shadow: inset 0 -1px 0 var(--bs-border-color);
}

.accordion-button:not(.collapsed)::after {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23007bff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

/* 图库 */
.gallery-grid {
    -webkit-column-count: 3;
    -moz-column-count: 3;
    column-count: 3;
    -webkit-column-gap: 30px;
    -moz-column-gap: 30px;
    column-gap: 30px;
}
.gallery-grid img {
    max-width: 100%;
}
.gallery-item {
    display: block;
    -webkit-column-break-inside: avoid;
    break-inside: avoid;
    padding-bottom: 30px;
}
.gallery-grid {
    -webkit-column-gap: 16px;
    -moz-column-gap: 16px;
    column-gap: 16px;
}
.gallery-grid .gallery-item {
    padding-bottom: 16px;
}

/** ----------------------------------
 * 页面加载loading
 -------------------------------------- */
#lyear-loading {
	position: absolute;
	width: 100%;
	height: calc(100% - 48px);
	z-index: 9990;
	background: var(--bs-body-bg);
	display: flex;
	align-items: center;
	justify-content: center;
}

/** ----------------------------------
 * 步骤条
 -------------------------------------- */
.nav-step {
    margin-bottom: 1rem;
}
.nav-step .nav-item {
	position: relative;
	display: -webkit-box;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	flex-direction: column;
	-webkit-box-flex: 1;
	-webkit-box-align: center;
	align-items: center;
	flex: 1 1;
	padding: 0 12px
}
.nav-step .nav-item:first-child a::before {
	display: none
}
.nav-step .nav-item.complete .nav-link,
.nav-step .nav-item.complete .nav-link::before,
.nav-step .nav-item  .nav-link.active,
.nav-step .nav-item  .nav-link.active::before {
	background-color: rgba(var(--bs-primary-rgb), .2)
}
.nav-step .nav-item.complete .nav-link::after,
.nav-step .nav-item .nav-link.active::after {
	background-color: var(--bs-primary);
	width: 24px;
	height: 24px;
	-webkit-transform: translateX(0);
	transform: translateX(0);
	color: #fff
}
.nav-step .nav-item.complete .nav-link::after {
	width: 29px;
	height: 29px;
	-webkit-transform: translateX(0);
	transform: translateX(0);
	color: #fff
}
.nav-step .nav-item .nav-link.active::after {
	width: 13px;
	height: 13px;
	margin-top: 8px;
	-webkit-transform: translateX(8px);
	transform: translateX(8px);
	color: transparent
}
.nav-step .nav-link {
	display: -webkit-inline-box;
	display: inline-flex;
    padding: 0;
	margin: 10px 0;
	width: 29px;
	height: 29px;
	max-height: 29px;
	border-radius: 50%;
	background-color: var(--bs-pre);
	-webkit-transition: .5s;
	transition: .5s;
	z-index: 1
}
.nav-step .nav-link::before {
	content: '';
	position: absolute;
	left: calc(-50% + 14.5px);
	right: calc(50% + 14.5px);
	height: 10px;
	margin-top: 9.5px;
	background-color: var(--bs-pre);
	cursor: default;
	-webkit-transition: .5s;
	transition: .5s;
}
.nav-step .nav-link::after {
	content: "\F012C";
	font-family: "Material Design Icons";
	width: 0;
	height: 0;
	text-align: center;
	font-size: 18px;
	position: absolute;
	border-radius: 50%;
	background-color: transparent;
	color: transparent;
	-webkit-transform: translate(14.5px, 14.5px);
	transform: translate(14.5px, 14.5px);
	-webkit-transition: .5s;
	transition: .5s;
	z-index: 1;
	display: -webkit-inline-box;
	display: inline-flex;
	-webkit-box-align: center;
	align-items: center;
	-webkit-box-pack: center;
	justify-content: center
}
.nav-step-pane.active {
    display: block!important;
}

/* 上传 */
.lyear-uploads-pic {
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: stretch;
    align-items: stretch;
    flex-direction: row;
    flex-wrap: wrap;
    margin-bottom: -10px;
}
.lyear-uploads-pic li {
    position: relative;
    margin-bottom: 10px;
}
.lyear-uploads-pic .list-images-item .progress {
    position: absolute;
    bottom: 0px;
    margin-bottom: 0px;
    left: 12px;
    right: 12px;
}
.lyear-uploads-pic figure  {
    position: relative;
    margin: 0px;
    background: #4d5259;
    overflow: hidden;
    text-align: center;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 165px;
    max-height: 165px;
}
.lyear-uploads-pic figure img {
    position: relative;
    display: block;
    min-height: 100%;
    max-width: 100%;
    width: 100%;
    vertical-align: bottom;
    opacity: 1;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    -webkit-transition: opacity 0.5s;
    transition: opacity 0.5s;
}
.lyear-uploads-pic figure:hover img {
    opacity: 0.5;
}
.lyear-uploads-pic figure figcaption,
.lyear-uploads-pic figure figcaption > a:not(.btn) {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.lyear-uploads-pic figure figcaption {
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    text-transform: none;
    padding: 2em;
    color: #fff;
    -webkit-transform: scale(0);
    transform: scale(0);
    -webkit-transition: .35s;
    transition: .35s;
}
.lyear-uploads-pic figure figcaption > a {
    position: static;
    z-index: auto;
    text-indent: 0;
    white-space: nowrap;
    opacity: 1;
	margin-left: 2px;
	margin-right: 2px
}
.lyear-uploads-pic figure figcaption > *:first-child {
    margin-left: 0;
}
.lyear-uploads-pic figure:hover figcaption {
	-webkit-transform: scale(1);
	transform: scale(1)
}
.lyear-uploads-pic .success,
.lyear-uploads-pic .error {
    position: absolute;
    top: 0px;
    background-color: rgba(0, 0, 0, .125);
    right: 12px;
    left: 12px;
    color: #fff;
    text-align: center;
}

/* 时光轴 */
/* 时光轴 */
.lyear-timeline {
    margin: 0px;
    padding: 0px;
    list-style: none;
}
.lyear-timeline .lyear-timeline-item {
    display: -webkit-box;
    display: flex;
}
.lyear-timeline-date {
    position: relative;
    padding: 30px 0;
    text-align: center;
}
.lyear-timeline-date time {
    font-size: 16px!important;
    color: #8b95a5;
}
.lyear-timeline-item-dot {
    position: relative;
    flex-shrink: 0;
    -webkit-box-flex: 0;
    flex-grow: 0;
    -webkit-box-ordinal-group: 3;
    order: 2;
    width: 80px;
    padding-bottom: 30px;
    text-align: center;
}
.lyear-timeline-item-dot .badge {
    display: inline-block;
    vertical-align: inherit;
    width: 11px;
    height: 11px;
    padding: 0px;
    -wekit-border-radius: 50%;
    border-radius: 50%;
    position: relative;
    z-index: 3;
}
.lyear-timeline-item-dot::before {
    content: '';
    position: absolute;
    top: 9px;
    left: 50%;
    bottom: -9px;
    width: 1px;
    margin-left: -1px;
    z-index: 1;
    background-color: #f2f3f3;
}
.lyear-timeline-item-action {
    -webkit-box-flex: 1;
    flex: 1 1;
    padding-bottom: 30px;
    color: #8b95a5;
}
.lyear-timeline-item-content {
    -webkit-box-flex: 1;
    flex: 1 1;
    margin-bottom: 50px;
}
.lyear-timeline-item-content .card {
    margin-bottom: 0px;
}
.lyear-timeline-item-content time {
    color: #8b95a5;
}
.lyear-timeline-left .lyear-timeline-item .lyear-timeline-item-content {
    -webkit-box-ordinal-group: 4!important;
    order: 3!important;
}
.lyear-timeline-right .lyear-timeline-item .lyear-timeline-item-content {
    text-align: right;
    -webkit-box-ordinal-group: 2!important;
    order: 1!important;
}
.lyear-timeline-left .lyear-timeline-item-content,
.lyear-timeline-right .lyear-timeline-item-content {
    margin-bottom: 10px;
}
.lyear-timeline-left .lyear-timeline-item-dot,
.lyear-timeline-right .lyear-timeline-item-dot {
    width: 40px;
}
.lyear-timeline-left .lyear-timeline-item-dot::before,
.lyear-timeline-right .lyear-timeline-item-dot::before {
    margin-left: 0px;
}
.lyear-timeline-item.text-muted .badge {
    background-color: #8b95a5;
}

/** ----------------------------------
 * 滚动条插件样式
 -------------------------------------- */
.ps {
	overflow: hidden !important;
	overflow-anchor: none;
	-ms-overflow-style: none;
	touch-action: auto;
	-ms-touch-action: auto;
}

.ps__rail-x {
	display: none;
	opacity: 0;
	transition: background-color .2s linear, opacity .2s linear;
	-webkit-transition: background-color .2s linear, opacity .2s linear;
	height: 6px;
	bottom: 2px;
	position: absolute;
}

.ps__rail-y {
	display: none;
	opacity: 0;
	transition: background-color .2s linear, opacity .2s linear;
	-webkit-transition: background-color .2s linear, opacity .2s linear;
	width: 6px;
	right: 2px;
	position: absolute;
}

.ps--active-x>.ps__rail-x,
.ps--active-y>.ps__rail-y {
	display: block;
	background-color: transparent;
}

.ps:hover>.ps__rail-x,
.ps:hover>.ps__rail-y,
.ps--focus>.ps__rail-x,
.ps--focus>.ps__rail-y,
.ps--scrolling-x>.ps__rail-x,
.ps--scrolling-y>.ps__rail-y {
	opacity: 0.6;
}

.ps .ps__rail-x:hover,
.ps .ps__rail-y:hover,
.ps .ps__rail-x:focus,
.ps .ps__rail-y:focus,
.ps .ps__rail-x.ps--clicking,
.ps .ps__rail-y.ps--clicking {
	background-color: var(--bs-gray-200);
	opacity: 0.9;
}

.ps__thumb-x {
	background-color: var(--bs-gray-400);
	border-radius: 6px;
	transition: background-color .2s linear, height .2s ease-in-out;
	-webkit-transition: background-color .2s linear, height .2s ease-in-out;
	height: 6px;
	bottom: 2px;
	position: absolute;
}

.ps__thumb-y {
	background-color: var(--bs-gray-400);
	border-radius: 6px;
	transition: background-color .2s linear, width .2s ease-in-out;
	-webkit-transition: background-color .2s linear, width .2s ease-in-out;
	width: 3px;
	right: 0;
	position: absolute;
}

.ps__rail-x:hover>.ps__thumb-x,
.ps__rail-x:focus>.ps__thumb-x,
.ps__rail-x.ps--clicking .ps__thumb-x {
	background-color: var(--bs-gray-500);
	height: 6px;
}

.ps__rail-y:hover>.ps__thumb-y,
.ps__rail-y:focus>.ps__thumb-y,
.ps__rail-y.ps--clicking .ps__thumb-y {
	background-color: var(--bs-gray-500);
	width: 6px;
}

@supports (-ms-overflow-style: none) {
	.ps {
		overflow: auto !important;
	}
}

@media screen and (-ms-high-contrast: active),
(-ms-high-contrast: none) {
	.ps {
		overflow: auto !important;
	}
}


/** ----------------------------------
 * 网站布局
 -------------------------------------- */
body {
    background-color: transparent;
}
body.lyear-index {
    background-color: var(--bs-body-bg);
}

/** ----------------------------------
 * 左侧导航
 -------------------------------------- */
.lyear-layout-sidebar {
	position: fixed;
	top: 0;
	bottom: 0;
	z-index: 5;
	display: block;
	width: 240px;
	font-weight: 500;
	-webkit-backface-visibility: hidden;
	backface-visibility: hidden;
	-webkit-transition: 0.3s transform;
	transition: 0.3s transform;
	transform: translateX(0);
	-webkit-box-shadow: 0px 0px 5px rgba(var(--bs-dark-rgb), 0.075);
	-moz-box-shadow: 0px 0px 5px rgba(var(--bs-dark-rgb), 0.075);
	box-shadow: 0px 0px 5px rgba(var(--bs-dark-rgb), 0.075);
}

.lyear-layout-sidebar-close .lyear-layout-sidebar {
	width: 60px;
}

.lyear-layout-sidebar-close .lyear-layout-header,
.lyear-layout-sidebar-close .lyear-layout-content {
	padding-left: 60px;
}

.lyear-layout-sidebar-info {
	height: -moz-calc(100% - 68px);
	height: -webkit-calc(100% - 68px);
	height: calc(100% - 68px);
	position: relative;
	background-color: var(--bs-white);
}

/* logo */
.sidebar-header {
	position: relative;
	overflow: hidden;
	z-index: 999;
	background-color: var(--bs-white);
	width: 100%;
	-webkit-box-shadow: 0 1px 1px -1px rgba(var(--bs-dark-rgb), 0.25);
	box-shadow: 0 1px 1px -1px rgba(var(--bs-dark-rgb), 0.25);
}

.sidebar-header:before,
.sidebar-header:after {
	content: " ";
	display: table;
}

.sidebar-header a {
	display: block;
	width: 100%;
	height: 68px;
	overflow: hidden;
	display: -webkit-box;
	display: -moz-box;
	display: box;
	-webkit-box-align: center;
	-moz-box-align: center;
	box-align: center;
	-moz-box-pack: center;
	box-pack: center;
}

.sidebar-header a img {
	max-width: 240px;
}

/* 菜单 */
.sidebar-main {
	-webkit-transform: translateZ(0);
	transform: translateZ(0);
}

.nav-drawer {
	list-style: none;
	padding: 0px;
	margin: 0px;
}

.nav-drawer>li {
	position: relative;
	display: block;
}

.nav-drawer li a {
	position: relative;
	display: block;
	padding-right: 24px;
	padding: 10px 15px 10px 52.99999px;
	color: inherit;
	font-weight: 500;
	white-space: nowrap;
}

.nav-drawer>li>a {
	border-right: 3px solid transparent;
	padding-top: 12px;
	padding-bottom: 12px;
}

.nav-drawer>li>a>span {
	white-space: nowrap;
}

.nav-drawer>.active>a {
	background-color: rgba(var(--bs-dark-rgb), .0125);
	border-color: var(--bs-primary);
}

.nav-drawer>li.active>a {
	background-color: rgba(var(--bs-dark-rgb), .0125) !important;
}

.nav-drawer>.active>a:hover,
.nav-drawer>.active>a:focus,
.nav-drawer>.active>a:active {
	background-color: rgba(var(--bs-dark-rgb), .0125);
	border-color: var(--bs-primary);
}

.nav-drawer .nav-subnav>li.active>a,
.nav-drawer .nav-subnav>li>a:hover {
	color: var(--bs-primary);
	background-color: transparent;
}

.nav-drawer>li>a>i {
	position: absolute;
	left: 21px;
	top: 8px;
	font-size: 1.25em;
}

.nav-drawer ul li ul {
	padding-left: 15px;
}

.nav-item-has-subnav>a:after {
	position: absolute;
	top: 12px;
	right: 24px;
	font-family: 'Material Design Icons';
	font-size: 10px;
	line-height: 2;
	content: '\F0142';
	-webkit-transition: -webkit-transform 0.3s linear;
	transition: -webkit-transform 0.3s linear;
	transition: transform 0.3s linear;
	transition: transform 0.3s linear, -webkit-transform 0.3s linear;
}

.nav-item-has-subnav .nav-item-has-subnav>a:after {
	top: 10px;
}

.nav-item-has-subnav.open>a:after {
	-webkit-transform: rotate(90deg);
	transform: rotate(90deg);
}

.nav-item-has-subnav.open>.nav-subnav {
	display: block;
}

.nav-subnav {
	display: none;
	margin-top: 8px;
	margin-bottom: 8px;
}

/* 左侧底部Copyright */
.sidebar-footer {
	bottom: 0;
	width: 100%;
	height: 96px;
	border-top: 1px solid rgba(77, 82, 89, 0.1);
	margin-top: 24px;
	padding-top: 24px;
	padding-right: 24px;
	padding-bottom: 24px;
	padding-left: 24px;
	font-size: 13px;
	line-height: 24px;
}

/** ----------------------------------
 * 头部导航
 -------------------------------------- */
.lyear-layout-header {
	position: fixed;
	top: 0;
	right: 0;
	left: 0;
	z-index: 4;
	padding-left: 240px;
	background-color: var(--bs-white);
	-webkit-transition: padding 0.3s;
	transition: padding 0.3s;
	-webkit-box-shadow: 0 1px 1px -1px rgb(var(--bs-dark-rgb), 0.25);
	-moz-box-shadow: 0 1px 1px -1px rgb(var(--bs-dark-rgb), 0.25);
	box-shadow: 0 1px 1px -1px rgb(var(--bs-dark-rgb), 0.25);
}

.lyear-layout-header .navbar {
	min-height: 68px;
	padding: 0 1rem;
	margin-bottom: 0;
	border: 0px;
	background-color: transparent;
}

/* 侧边栏开关 */
.lyear-aside-toggler {
	margin-right: .25rem;
	padding: .25rem .95rem .25rem .25rem;
	line-height: 1.5;
	cursor: pointer;
}

.lyear-aside-toggler .lyear-toggler-bar {
	display: block;
	height: 2px;
	width: 20px;
	background-color: var(--bs-gray-700);
	margin: 4px 0px;
	-webkit-transition: 0.3s;
	transition: 0.3s;
}

.lyear-aside-toggler .lyear-toggler-bar:nth-child(2) {
	width: 15px;
}

.lyear-aside-toggler:hover .lyear-toggler-bar:nth-child(2) {
	width: 20px;
}

.lyear-layout-sidebar-close .lyear-aside-toggler .lyear-toggler-bar {
	width: 20px;
}

/* 头部右侧内容 */
.navbar-right {
	list-style: none;
	padding: 0px;
	margin: 0px;
}

.navbar-right .icon-item {
	cursor: pointer;
	display: block;
	height: 68px;
	padding: 15px 10px;
}

/* 头像菜单 */
.navbar-right > li > a {
	position: relative;
	display: block;
	padding: 10px 0px 10px 15px;
}

/* 消息列表 */
.dropdown-notice .dropdown-menu {
	min-width: 200px;
	max-width: 280px;
}

.dropdown-notice .badge {
	font-size: .5em;
	top: 25px;
}

.dropdown-notice .dropdown-item {
	text-overflow: ellipsis;
	overflow: hidden;
	word-break: break-all;
}

.dropdown-notice .lyear-notifications-title {
	padding: 5px 15px 10px 15px;
	border-bottom: 1px solid var(--bs-border-color);
}

.dropdown-notice .lyear-notifications-info {
	position: relative;
	height: 200px;
	max-height: 200px;
	margin: 0;
	padding: 0;
}

/* 主题配置选择 */
.dropdown-skin .dropdown-menu {
    width: 262px;
    -moz-user-select: none; /* 火狐 */
    -webkit-user-select: none; /* webkit浏览器 */
    -ms-user-select: none; /* IE10 */
    -khtml-user-select: none; /* 早期浏览器 */
    user-select: none;
}
.lyear-skin-title p {
    padding: 5px 15px 0px 15px;
}
.lyear-skin-li {
    padding: 0px 12px;
}
.lyear-skin-li .form-check {
    padding: 0px;
    margin: 0px;
}
.lyear-skin-li .form-check-input {
    display: none;
}
.lyear-skin-li .form-check-input+label {
    display: inline-block;
    width: 20px;
    height: 20px;
    cursor: pointer;
    margin: 3px;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	-webkit-transition: all .1s ease;
    transition: all .1s ease;
}
.lyear-skin-li .form-check-input:checked+label {
    position: relative;
}
.lyear-skin-li .form-check-input:checked+label::after {
    content: "\F012C";
    font-family: "Material Design Icons";
    font-size: 1rem;
    display: block;
    color: var(--bs-white);
    width: 100%;
    text-align: center;
	line-height: 20px;
    position: absolute;
    top: 0px;
    -webkit-transition: .2s;
    transition: .2s;
}
li.lyear-skin-li .form-check:first-child .form-check-input:checked+label::after {
    color: var(--bs-body-color);
}
#header_bg_1+label, #logo_bg_1+label, #sidebar_bg_1+label, #site_theme_1+label {
    background-color: var(--bs-white);
	border: 1px solid var(--bs-border-color);
}
#header_bg_2+label, #logo_bg_2+label, #sidebar_bg_2+label {
    background-color: var(--bs-success);
	border: 1px solid var(--bs-success);
}
#header_bg_3+label, #logo_bg_3+label, #sidebar_bg_3+label {
    background-color: var(--bs-info);
	border: 1px solid var(--bs-info);
}
#header_bg_4+label, #logo_bg_4+label, #sidebar_bg_4+label {
    background-color: var(--bs-warning);
	border: 1px solid var(--bs-warning);
}
#header_bg_5+label, #logo_bg_5+label, #sidebar_bg_5+label {
    background-color: var(--bs-danger);
	border: 1px solid var(--bs-danger);
}
#header_bg_6+label, #logo_bg_6+label, #sidebar_bg_6+label {
    background-color: var(--bs-purple);
	border: 1px solid var(--bs-purple);
}
#header_bg_7+label, #logo_bg_7+label, #sidebar_bg_7+label {
    background-color: var(--bs-teal);
	border: 1px solid var(--bs-teal);
}
#header_bg_8+label, #logo_bg_8+label, #sidebar_bg_8+label, #site_theme_8+label {
    background-color: var(--bs-dark);
	border: 1px solid var(--bs-dark);
}
#site_theme_2+label {
    background-image: -webkit-gradient(linear,left top,right top,from(#00cef9),to(#00e6af));
    background-image: -webkit-linear-gradient(left,#00cef9,#00e6af);
    background-image: -moz-linear-gradient(left,#00cef9,#00e6af);
    background-image: -o-linear-gradient(left,#00cef9,#00e6af);
    background-image: linear-gradient(to right,#00cef9,#00e6af);
    background-repeat: repeat-x;
}
#site_theme_3+label {
    background-image: -webkit-gradient(linear,left top,right top,from(#9f78ff),to(#32cafe));
    background-image: -webkit-linear-gradient(left,#9f78ff,#32cafe);
    background-image: -moz-linear-gradient(left,#9f78ff,#32cafe);
    background-image: -o-linear-gradient(left,#9f78ff,#32cafe);
    background-image: linear-gradient(to right,#9f78ff,#32cafe);
    background-repeat: repeat-x;
}
#site_theme_4+label {
    background-image: -webkit-gradient(linear,left top,right top,from(#ff8008),to(#ffc837));
    background-image: -webkit-linear-gradient(left,#ff8008,#ffc837);
    background-image: -moz-linear-gradient(left,#ff8008,#ffc837);
    background-image: -o-linear-gradient(left,#ff8008,#ffc837);
    background-image: linear-gradient(to right,#ff8008,#ffc837);
    background-repeat: repeat-x;
}
#site_theme_5+label {
    background-image: -webkit-gradient(linear,left top,right top,from(#ff5858),to(#e888b7));
    background-image: -webkit-linear-gradient(left,#ff5858,#e888b7);
    background-image: -moz-linear-gradient(left,#ff5858,#e888b7);
    background-image: -o-linear-gradient(left,#ff5858,#e888b7);
    background-image: linear-gradient(to right,#ff5858,#e888b7);
    background-repeat: repeat-x;
}
#site_theme_6+label {
    background-image: -webkit-gradient(linear,left top,right top,from(#a376fc),to(#f96f9b));
    background-image: -webkit-linear-gradient(left,#a376fc,#f96f9b);
    background-image: -moz-linear-gradient(left,#a376fc,#f96f9b);
    background-image: -o-linear-gradient(left,#a376fc,#f96f9b);
    background-image: linear-gradient(to right,#a376fc,#f96f9b);
    background-repeat: repeat-x;
}
#site_theme_7+label {
    background-image: -webkit-gradient(linear,left top,right top,from(#514a9d),to(#24c6dc));
    background-image: -webkit-linear-gradient(left,#514a9d,#24c6dc);
    background-image: -moz-linear-gradient(left,#514a9d,#24c6dc);
    background-image: -o-linear-gradient(left,#514a9d,#24c6dc);
    background-image: linear-gradient(to right,#514a9d,#24c6dc);
    background-repeat: repeat-x;
}


/** ----------------------------------
 * 主要内容
 -------------------------------------- */
.lyear-layout-content {
	position: absolute;
	height: 100%;
	width: 100%;
	padding-top: 68px;
	padding-left: 240px;
	-webkit-transition: padding 0.3s;
	transition: padding 0.3s;
}

.lyear-layout-content #iframe-content {
	height: 100%;
}

/** ----------------------------------
 * 响应式处理
 -------------------------------------- */
@media (max-width: 1279.98px) {
    .table-responsive-xxl>.table>tbody>tr>td,
    .table-responsive-xxl>.table>tbody>tr>th,
    .table-responsive-xxl>.table>tfoot>tr>td,
    .table-responsive-xxl>.table>tfoot>tr>th,
    .table-responsive-xxl>.table>thead>tr>td,
    .table-responsive-xxl>.table>thead>tr>th {
        white-space: nowrap;
    }
}
@media (max-width: 1199.98px) {
    .table-responsive-xl>.table>tbody>tr>td,
    .table-responsive-xl>.table>tbody>tr>th,
    .table-responsive-xl>.table>tfoot>tr>td,
    .table-responsive-xl>.table>tfoot>tr>th,
    .table-responsive-xl>.table>thead>tr>td,
    .table-responsive-xl>.table>thead>tr>th {
        white-space: nowrap;
    }
}
@media (max-width: 1024px) {
	.lyear-layout-sidebar {
		transform: translateX(-100%);
	}

	.lyear-layout-header,
	.lyear-layout-content {
		padding-left: 0;
	}

	.lyear-layout-sidebar {
		-webkit-box-shadow: none;
		-moz-webkit-box-shadow: none;
		box-shadow: none;
	}

	.lyear-layout-sidebar.lyear-aside-open {
		transform: translateX(0);
	}

	/* 遮罩层 */
	.lyear-mask-modal {
		background-color: rgba(var(--bs-dark-rgb), 0.5);
		height: 100%;
		left: 0;
		opacity: 1;
		top: 0;
		visibility: visible;
		width: 100%;
		z-index: 5;
		position: fixed;
		-webkit-transition: visibility 0 linear 0.4s, opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		transition: visibility 0 linear 0.4s, opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		-webkit-transform: translateZ(0);
		transform: translateZ(0);
	}

	.lyear-layout-sidebar-close .lyear-layout-sidebar {
		width: 240px;
	}

	.lyear-layout-sidebar-close .lyear-layout-header,
	.lyear-layout-sidebar-close .lyear-layout-content {
		padding-left: 0px;
	}
}

@media (min-width: 1024px) {
	.lyear-layout-sidebar-close .nav-drawer>li>a {
		padding: 12px 28px 12px 29px;
		height: 48px;
	}

	.lyear-layout-sidebar-close .nav-drawer>li>a span {
		display: none;
	}

	.lyear-layout-sidebar-close .nav-drawer>li>a:after {
		content: '';
	}

	.lyear-layout-sidebar-close .ps__rail-x,
	.lyear-layout-sidebar-close .ps__rail-y {
		pointer-events: none;
	}

	.lyear-layout-sidebar-close .lyear-layout-sidebar:not(:hover) .ps {
		overflow: visible !important;
	}

	.lyear-layout-sidebar-close .lyear-layout-sidebar:not(:hover) .nav-item-has-subnav>.nav-subnav {
		display: none !important;
	}

	.lyear-layout-sidebar-close .sidebar-footer {
		visibility: hidden;
		opacity: 0;
	}

	.lyear-layout-sidebar {
		-webkit-transition: width .3s ease-in-out;
		transition: width .3s ease-in-out;
	}

	.lyear-layout-sidebar-close .lyear-layout-sidebar:hover {
		width: 240px;
	}

	.lyear-layout-sidebar-close .lyear-layout-sidebar:hover .nav-drawer>li>a {
		padding-right: 24px;
		padding-left: 52.99999px;
		padding-top: 12px;
		padding-bottom: 12px;
	}

	.lyear-layout-sidebar-close .lyear-layout-sidebar:hover .nav-drawer>li>a span {
		display: block;
	}

	.lyear-layout-sidebar-close .lyear-layout-sidebar:hover .nav-drawer .nav-item-has-subnav>a:after {
		content: '\F0142';
	}

	.lyear-layout-sidebar-close .lyear-layout-sidebar:hover .sidebar-footer {
		visibility: visible;
		opacity: 1;
		-webkit-transition: opacity 0.3s ease-in-out 0.15s;
		transition: opacity 0.3s ease-in-out 0.15s;
	}

	body:not(.lyear-layout-sidebar-close) .sidebar-footer {
		-webkit-transition: opacity 0.3s ease-in-out 0.15s;
		transition: opacity 0.3s ease-in-out 0.15s;
	}
    .gallery-grid {
        -webkit-column-count: 4;
        -moz-column-count: 4;
        column-count: 4;
    }
}
@media (min-width: 992px) {
    .lyear-timeline .lyear-timeline-item:nth-child(even) .lyear-timeline-item-action {
        -webkit-box-ordinal-group: 2;
        order: 1;
        text-align: right;
    }
    .lyear-timeline .lyear-timeline-item:nth-child(odd) .lyear-timeline-item-action {
        -webkit-box-ordinal-group: 4;
        order: 3;
        text-align: left;
    }
    .lyear-timeline .lyear-timeline-item:nth-child(even) .lyear-timeline-item-content {
        -webkit-box-ordinal-group: 4;
        order: 3;
    }
    .lyear-timeline .lyear-timeline-item:nth-child(odd) .lyear-timeline-item-content {
        -webkit-box-ordinal-group: 2;
        order: 1;
    }
}
@media (max-width: 991.98px) {
    .table-responsive-lg>.table>tbody>tr>td,
    .table-responsive-lg>.table>tbody>tr>th,
    .table-responsive-lg>.table>tfoot>tr>td,
    .table-responsive-lg>.table>tfoot>tr>th,
    .table-responsive-lg>.table>thead>tr>td,
    .table-responsive-lg>.table>thead>tr>th {
        white-space: nowrap;
    }
}
@media (max-width: 767.98px) {
    .table-responsive-md>.table>tbody>tr>td,
    .table-responsive-md>.table>tbody>tr>th,
    .table-responsive-md>.table>tfoot>tr>td,
    .table-responsive-md>.table>tfoot>tr>th,
    .table-responsive-md>.table>thead>tr>td,
    .table-responsive-md>.table>thead>tr>th {
        white-space: nowrap;
    }
    .gallery-grid {
        -webkit-column-count: 2;
        -moz-column-count: 2;
        column-count: 2;
    }
}
@media (max-width: 575.98px) {
    .table-responsive-sm>.table>tbody>tr>td,
    .table-responsive-sm>.table>tbody>tr>th,
    .table-responsive-sm>.table>tfoot>tr>td,
    .table-responsive-sm>.table>tfoot>tr>th,
    .table-responsive-sm>.table>thead>tr>td,
    .table-responsive-sm>.table>thead>tr>th {
        white-space: nowrap;
    }    
    .pagination .page-item {
        display: none;
    }
    .pagination .page-item:first-child,
    .pagination .page-item:last-child {
        display: block;
    }
    .lyear-timeline-center .lyear-timeline-item .lyear-timeline-item-action {
        display: none;
    }
}
@media screen and (max-width: 430px) {
    .dropdown-skin .dropdown-menu {
        left: -64px!important;
    }
    .dropdown-notice .dropdown-menu {
        left: -38px!important;
    }
    .nav-step .nav-step-item p {
        display: none;
    }
}

/* 配色 */
[data-headerbg='color_2'] .lyear-layout-header,
[data-logobg='color_2'] .sidebar-header,
[data-sidebarbg='color_2'] .lyear-layout-sidebar-info {
    background-color: var(--bs-success);
}
[data-headerbg='color_3'] .lyear-layout-header,
[data-logobg='color_3'] .sidebar-header,
[data-sidebarbg='color_3'] .lyear-layout-sidebar-info {
    background-color: var(--bs-info);
}
[data-headerbg='color_4'] .lyear-layout-header,
[data-logobg='color_4'] .sidebar-header,
[data-sidebarbg='color_4'] .lyear-layout-sidebar-info {
    background-color: var(--bs-warning);
}
[data-headerbg='color_5'] .lyear-layout-header,
[data-logobg='color_5'] .sidebar-header,
[data-sidebarbg='color_5'] .lyear-layout-sidebar-info {
    background-color: var(--bs-danger);
}
[data-headerbg='color_6'] .lyear-layout-header,
[data-logobg='color_6'] .sidebar-header,
[data-sidebarbg='color_6'] .lyear-layout-sidebar-info {
    background-color: var(--bs-purple);
}
[data-headerbg='color_7'] .lyear-layout-header,
[data-logobg='color_7'] .sidebar-header,
[data-sidebarbg='color_7'] .lyear-layout-sidebar-info {
    background-color: var(--bs-teal);
}
[data-headerbg='color_8'] .lyear-layout-header,
[data-logobg='color_8'] .sidebar-header,
[data-sidebarbg='color_8'] .lyear-layout-sidebar-info {
    background-color: var(--bs-dark);
}

[data-logobg*='color_'] .sidebar-header img {
    position: relative;
    -webkit-filter: brightness(275%);
	-moz-filter: brightness(275%);
    -ms-filter: brightness(275%);
    -o-filter: brightness(275%);
    filter: brightness(275%);
}
[data-headerbg*='color_'] .lyear-layout-header,
[data-headerbg*='color_'] .lyear-layout-header .topbar-right > li > a,
[data-sidebarbg*='color_'] .lyear-layout-sidebar-info a,
[data-sidebarbg*='color_'] .sidebar-footer {
    color: rgba(var(--bs-white-rgb), .85);
}
[data-sidebarbg*='color_'] .nav-drawer .nav-subnav > li.active > a,
[data-sidebarbg*='color_'] .nav-drawer .nav-subnav > li > a:hover {
    color: var(--bs-white);
}
[data-headerbg*='color_'] .lyear-aside-toggler .lyear-toggler-bar {
    background-color: var(--bs-white);
}
[data-sidebarbg*='color_'] .nav-drawer > .active > a {
    border-color: rgba(var(--bs-white-rgb), .35);
	background-color: rgba(var(--bs-white-rgb), .075)!important;
}
[data-sidebarbg*='color_'] .nav > li > a:hover {
    background-color: rgba(var(--bs-white-rgb), .035);
}
[data-sidebarbg*='color_'] .nav-drawer > .active > a:hover,
[data-sidebarbg*='color_'] .nav-drawer > .active > a:focus,
[data-sidebarbg*='color_'] .nav-drawer > .active > a:active {
    border-color: rgba(var(--bs-white-rgb), .35);
}
[data-headerbg*='color_'] .navbar-right > li > a {
    color: var(--bs-white);
}

/* 暗黑 */
body.lyear-index[data-theme='dark'],
body[data-theme='dark'] .jconfirm .jconfirm-box,
[data-theme='dark'] #lyear-loading {
    background-color: #282E38;
    color: var(--bs-gray-400);
}
body[data-theme='dark'] {
    color: var(--bs-gray-400);
}
[data-theme='dark'] .nav-tabs .nav-link,
[data-theme='dark'] .navbar-right a,
[data-theme='dark'] .lyear-notifications span,
[data-theme='dark'] .lyear-skin-title p,
[data-theme='dark'] .form-control,
[data-theme='dark'] .form-control-plaintext,
[data-theme='dark'] .form-control,
[data-theme='dark'] .form-select,
[data-theme='dark'] .input-group-text,
[data-theme='dark'] .dropdown-item,
[data-theme='dark'] .lyear-divider,
[data-theme='dark'] [class*='badge-outline-'],
[data-theme='dark'] .breadcrumb-item.active,
[data-theme='dark'] .breadcrumb-item+.breadcrumb-item::before,
[data-theme='dark'] .page-link,
[data-theme='dark'] .popover-header,
[data-theme='dark'] .popover-body,
[data-theme='dark'] .toast-header,
[data-theme|='translucent'] .bg-body {
    color: var(--bs-gray-400);
}
[data-theme='dark'] .lyear-toggler-bar {
    background-color: var(--bs-gray-400);
}
[data-theme='dark'] .sidebar-header,
[data-theme='dark'] .lyear-layout-sidebar-info,
[data-theme='dark'] .lyear-layout-header,
[data-theme='dark'] .card,
[data-theme='dark'] .mt-nav-bar .mt-nav,
[data-theme='dark'] .nav-tabs .nav-link.active,
[data-theme='dark'] .nav-tabs .nav-item.show .nav-link {
    background-color: #313844;
}
[data-theme='dark'] .lyear-dragging-tab > a,
[data-theme|='translucent'] .lyear-dragging-tab > a {
    background-color: transparent!important;
}
[data-theme='dark'] .lyear-layout-sidebar,
[data-theme|='translucent'] .lyear-layout-sidebar {
    -webkit-box-shadow: 0px 0px 1px rgba(var(--bs-white-rgb), .95);
    -moz-box-shadow: 0px 0px 1px rgba(var(--bs-white-rgb), .95);
    box-shadow: 0px 0px 1px rgba(var(--bs-white-rgb), .95);
}
[data-theme='dark'] .sidebar-header,
[data-theme='dark'] .lyear-layout-header,
[data-theme|='translucent'] .sidebar-header,
[data-theme|='translucent'] .lyear-layout-header {
    -webkit-box-shadow: 0 1px 1px -1px rgba(var(--bs-white-rgb), 0.75);
    -moz-box-shadow: 0 1px 1px -1px rgba(var(--bs-white-rgb), 0.75);
    box-shadow: 0 1px 1px -1px rgba(var(--bs-white-rgb), 0.75);
}
[data-theme='dark'] .mt-nav-bar .nav-tabs,
[data-theme='dark'] .card-header,
[data-theme='dark'] .table>tbody>tr>td,
[data-theme='dark'] .table>tbody>tr>th,
[data-theme='dark'] .table>tfoot>tr>td,
[data-theme='dark'] .table>tfoot>tr>th,
[data-theme='dark'] .table>thead>tr>td,
[data-theme='dark'] .table>thead>tr>th,
[data-theme='dark'] .border-example,
[data-theme='dark'] .border-example-row,
[data-theme|='translucent'] .table>tbody>tr>td,
[data-theme|='translucent'] .table>tbody>tr>th,
[data-theme|='translucent'] .table>tfoot>tr>td,
[data-theme|='translucent'] .table>tfoot>tr>th,
[data-theme|='translucent'] .table>thead>tr>td,
[data-theme|='translucent'] .table>thead>tr>th,
[data-theme|='translucent'] .border-example,
[data-theme|='translucent'] .border-example-row,
[data-theme='dark'] .dropdown-notice .lyear-notifications-title,
[data-theme='dark'] #header_bg_8+label,
[data-theme='dark'] #logo_bg_8+label,
[data-theme='dark'] #sidebar_bg_8+label,
[data-theme='dark'] #site_theme_8+label,
[data-theme='dark'] .lyear-divider::before,
[data-theme='dark'] .lyear-divider::after,
[data-theme='dark'] [class*='badge-outline-'],
[data-theme='dark'] .card>.list-group,
[data-theme='dark'] .list-group-item,
[data-theme|='translucent'] .list-group-item,
[data-theme='dark'] .card-footer,
[data-theme='dark'] .modal-header,
[data-theme='dark'] .modal-footer,
[data-theme|='translucent'] .modal-header,
[data-theme|='translucent'] .modal-footer,
[data-theme='dark'] .offcanvas-start,
[data-theme='dark'] .offcanvas-end,
[data-theme='dark'] .offcanvas-top,
[data-theme='dark'] .offcanvas-bottom,
[data-theme='dark'] .popover-header,
[data-theme='dark'] .sidebar-footer {
    border-color: rgba(var(--bs-white-rgb), 0.125);
}
[data-theme='dark'] .table> :not(:first-child) ,
[data-theme|='translucent'] .table> :not(:first-child) {
    border-top-color: rgba(var(--bs-white-rgb), 0.125) !important;
}
[data-theme='dark'] .dropdown-menu,
[data-theme='dark'] .modal-content,
[data-theme='dark'] .popover {
    background-color: #292F39;
    border: none;
    -webkit-box-shadow: 0 0 5px rgba(var(--bs-black-rgb), 0.75);
    -moz-box-shadow: 0px 0px 5px rgba(var(--bs-black-rgb), 0.75);
    box-shadow: 0 0 5px rgba(var(--bs-black-rgb), 0.75);
}
[data-theme='dark'] .offcanvas-start,
[data-theme='dark'] .offcanvas-end,
[data-theme='dark'] .offcanvas-top,
[data-theme='dark'] .offcanvas-bottom,
[data-theme='dark'] .popover-header {
    background-color: #292F39;
}
[data-theme='dark'] .bs-popover-auto[data-popper-placement^=right]>.popover-arrow::after,
[data-theme='dark'] .bs-popover-end>.popover-arrow::after {
    border-right-color: #292F39;
}
[data-theme='dark'] .bs-popover-auto[data-popper-placement^=top]>.popover-arrow::after,
[data-theme='dark'] .bs-popover-top>.popover-arrow::after {
    border-top-color: #292F39;
}
[data-theme='dark'] .bs-popover-auto[data-popper-placement^=bottom]>.popover-arrow::after,
[data-theme='dark'] .bs-popover-bottom>.popover-arrow::after {
    border-bottom-color: #292F39;
}
[data-theme='dark'] .bs-popover-auto[data-popper-placement^=left]>.popover-arrow::after,
[data-theme='dark'] .bs-popover-start>.popover-arrow::after {
    border-left-color: #292F39;
}
[data-theme='dark'] .bs-popover-auto[data-popper-placement^=right]>.popover-arrow::before,
[data-theme='dark'] .bs-popover-end>.popover-arrow::before {
    border-right-color: #191924;
}
[data-theme='dark'] .bs-popover-auto[data-popper-placement^=top]>.popover-arrow::before,
[data-theme='dark'] .bs-popover-top>.popover-arrow::before {
    border-top-color: #191924;
}
[data-theme='dark'] .bs-popover-auto[data-popper-placement^=bottom]>.popover-arrow::before,
[data-theme='dark'] .bs-popover-bottom>.popover-arrow::before {
    border-bottom-color: #191924;
}
[data-theme='dark'] .bs-popover-auto[data-popper-placement^=left]>.popover-arrow::before,
[data-theme='dark'] .bs-popover-start>.popover-arrow::before {
    border-left-color: #191924;
}
[data-theme='dark'] .dropdown-menu .dropdown-item:focus,
[data-theme='dark'] .dropdown-menu .dropdown-item:hover,
[data-theme='dark'] .dropdown-menu .dropdown-item.active,
[data-theme='dark'] .dropdown-menu .dropdown-item.active:focus,
[data-theme='dark'] .dropdown-menu .dropdown-item.active:hover,
[data-theme|='translucent'] .dropdown-menu .dropdown-item:focus,
[data-theme|='translucent'] .dropdown-menu .dropdown-item:hover,
[data-theme|='translucent'] .dropdown-menu .dropdown-item.active,
[data-theme|='translucent'] .dropdown-menu .dropdown-item.active:focus,
[data-theme|='translucent'] .dropdown-menu .dropdown-item.active:hover,
[data-theme='dark'] .progress,
[data-theme|='translucent'] .progress,
[data-theme='dark'] .border-example-position-utils .position-relative,
[data-theme|='translucent'] .border-example-position-utils .position-relative,
[data-theme='dark'] .card>.card-header+.callout {
    background-color: rgba(var(--bs-white-rgb), 0.125);
}
[data-theme='dark'] .table {
    --bs-table-striped-color: var(--bs-gray-400);
    --bs-table-striped-bg: rgba(var(--bs-white-rgb), 0.075);
    --bs-table-active-color: var(--bs-gray-400);
    --bs-table-active-bg: rgba(var(--bs-black-rgb), .0375);
    --bs-table-hover-color: var(--bs-gray-400);
    --bs-table-hover-bg: rgba(var(--bs-white-rgb), 0.075);
    color: var(--bs-gray-400);
    border-color: rgba(var(--bs-white-rgb), 0.125);
}
[data-theme='dark'] .callout {
    background: rgba(var(--bs-white-rgb), 0.075);
    border-top-color: rgba(var(--bs-white-rgb), 0.125);
    border-right-color: rgba(var(--bs-white-rgb), 0.125);
    border-bottom-color: rgba(var(--bs-white-rgb), 0.125);
}
[data-theme='dark'] pre,
[data-theme='dark'] .list-group-item {
    background: rgba(var(--bs-white-rgb), 0.075);
    color: var(--bs-gray-400);
}
[data-theme='dark'] .border-example-row .row>.col,
[data-theme='dark'] .border-example-row .row>[class^=col-],
[data-theme='dark'] .img-thumbnail,
[data-theme='dark'] .form-control,
[data-theme='dark'] .form-check-input,
[data-theme='dark'] .form-select,
[data-theme='dark'] .input-group-text,
[data-theme='dark'] .page-link,
[data-theme='dark'] .border-example-border-utils [class^="border"],
[data-theme='dark'] .border-example-ratios .ratio,
[data-theme|='translucent'] .border-example-row .row>.col,
[data-theme|='translucent'] .border-example-row .row>[class^=col-],
[data-theme|='translucent'] .img-thumbnail,
[data-theme|='translucent'] .form-control,
[data-theme|='translucent'] .form-check-input,
[data-theme|='translucent'] .form-select,
[data-theme|='translucent'] .input-group-text,
[data-theme|='translucent'] .page-link,
[data-theme|='translucent'] .border-example-border-utils [class^="border"],
[data-theme|='translucent'] .border-example-ratios .ratio {
    background-color: rgba(var(--bs-white-rgb), 0.075);
    border: 1px solid rgba(var(--bs-white-rgb), 0.125);
}
[data-theme='dark'] .form-control:focus {
    border-color: rgba(var(--bs-white-rgb), 0.25);
	box-shadow: 0 0 0 0.25rem rgba(var(--bs-white-rgb), 0.125);
}
[data-theme='dark'] .form-check-input:checked,
[data-theme='dark'] .form-check-input[type=checkbox]:indeterminate,
[data-theme|='translucent'] .form-check-input:checked,
[data-theme|='translucent'] .form-check-input[type=checkbox]:indeterminate {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}
[data-theme='dark'] .form-select:disabled,
[data-theme='dark'] .form-control:disabled,
[data-theme='dark'] .form-control[readonly] {
    color: var(--bs-gray-600);
    background-color: rgba(var(--bs-white-rgb), .15);
}
[data-theme='dark'] .form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ced4da' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
}
[data-theme='dark'] .form-select:disabled {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%236c757d' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
}
[data-theme='dark'] .form-range::-webkit-slider-runnable-track {
	background-color: rgba(var(--bs-white-rgb), 0.075);
}
[data-theme='dark'] .form-range::-moz-range-track {
	background-color: rgba(var(--bs-white-rgb), 0.075);
}
[data-theme|='translucent'] .form-range::-webkit-slider-runnable-track {
	background-color: rgba(var(--bs-white-rgb), 0.075);
}
[data-theme|='translucent'] .form-range::-moz-range-track {
	background-color: rgba(var(--bs-white-rgb), 0.075);
}
[data-theme='dark'] .dropdown-divider,
[data-theme|='translucent'] .dropdown-divider {
    border-top-color: rgba(var(--bs-white-rgb), 0.125);
}
[data-theme='dark'] hr.dropdown-divider,
[data-theme|='translucent'] hr.dropdown-divider,
[data-theme='dark'] .card-footer {
    background-color: rgba(var(--bs-white-rgb), 0.125);
}
[data-theme='dark'] .form-control.is-valid,
[data-theme='dark'] .was-validated .form-control:valid,
[data-theme='dark'] .form-select.is-valid,
[data-theme='dark'] .was-validated .form-select:valid {
    border-color: var(--bs-success)
}
[data-theme='dark'] .form-control.is-invalid,
[data-theme='dark'] .was-validated .form-control:invalid,
[data-theme='dark'] .form-select.is-invalid,
[data-theme='dark'] .was-validated .form-select:invalid {
    border-color: var(--bs-danger)
}
[data-theme='dark'] .form-select.is-invalid:not([multiple]):not([size]),
[data-theme='dark'] .form-select.is-invalid:not([multiple])[size="1"],
[data-theme='dark'] .was-validated .form-select:invalid:not([multiple]):not([size]),
[data-theme='dark'] .was-validated .form-select:invalid:not([multiple])[size="1"] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ced4da' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"),url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
}
[data-theme='dark'] .accordion-button {
    color: var(--bs-gray-400);
    background-color: transparent;
}
[data-theme='dark'] .accordion-button:not(.collapsed),
[data-theme|='translucent'] .accordion-button:not(.collapsed) {
    box-shadow: inset 0 -1px 0 rgba(var(--bs-white-rgb), 0.125);
    background-color: rgba(var(--bs-white-rgb), 0.075);
}
[data-theme='dark'] .accordion-item,
[data-theme|='translucent'] .accordion-item {
    background-color: rgba(var(--bs-white-rgb), 0.075);
    border-color: rgba(var(--bs-white-rgb), 0.125);
}
[data-theme='dark'] .accordion-button::after,
[data-theme|='translucent'] .accordion-button::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ced4da'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e")
}
[data-theme='dark'] .btn-outline-dark {
    color: var(--bs-pure-black);
    border-color: var(--bs-pure-black);
}
[data-theme='dark'] .border-dark {
    border-color: var(--bs-pure-black)!important;
}
[data-theme='dark'] .text-dark {
    color: var(--bs-pure-black)!important;
}
[data-theme='dark'] .btn-close,
[data-theme|='translucent'] .btn-close {
    background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ced4da'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat
}
[data-theme='dark'] .list-group-item.active,
[data-theme|='translucent'] .list-group-item.active {
    border-color: rgba(var(--bs-white-rgb), 0.125);
    background-color: rgba(var(--bs-white-rgb), 0.125);
}
[data-theme='dark'] .list-group-item.disabled,
[data-theme='dark'] .list-group-item:disabled,
[data-theme='dark'] .nav-tabs .nav-link.disabled {
    color: var(--bs-gray-600);
}
[data-theme='dark'] .navbar.navbar-light.bg-light .form-control {
    border-color: var(--bs-gray-300);
}
[data-theme='dark'] .page-link:hover,
[data-theme='dark'] .page-link:focus {
	color: var(--bs-gray-200);
	background-color: rgba(var(--bs-white-rgb), 0.125);
}
[data-theme='dark'] .page-link:focus {
	box-shadow: none;
}
[data-theme='dark'] .page-item.disabled .page-link {
    background-color: rgba(var(--bs-white-rgb), 0.075);
    border-color: rgba(var(--bs-white-rgb), 0.125);
}
[data-theme='dark'] .toast {
    border: none;
    background-color: rgba(41, 47, 57, .85);
}
[data-theme='dark'] .toast-header {
    background-color: rgba(41, 47, 57, .85);
}
[data-theme='dark'] .border,
[data-theme='dark'] .border-start,
[data-theme='dark'] .border-top,
[data-theme='dark'] .border-end,
[data-theme='dark'] .border-bottom,
[data-theme|='translucent'] .border,
[data-theme|='translucent'] .border-start,
[data-theme|='translucent'] .border-top,
[data-theme|='translucent'] .border-end,
[data-theme|='translucent'] .border-bottom {
    border-color: rgba(var(--bs-white-rgb), 0.125)!important;
}
[data-theme='dark'] .shadow-sm {
    box-shadow: 0 .125rem .25rem rgba(var(--bs-black-rgb), 1)!important;
}
[data-theme='dark'] .shadow {
    box-shadow: 0 .5rem 1rem rgba(var(--bs-black-rgb), 1)!important;
}
[data-theme='dark'] .shadow-lg {
    box-shadow: 0 1rem 3rem rgba(var(--bs-black-rgb), 1)!important;
}
[data-theme='dark'] .card-shadowed,
[data-theme='dark'] .card-hover-shadow:hover {
    box-shadow: 0 1rem 3rem rgba(var(--bs-black-rgb), 1)!important;
}
[data-theme='dark'] .datepicker-dropdown:before,
[data-theme='dark'] .datepicker-dropdown:after {
    border-bottom-color: #292F39;
}
[data-theme='dark'] .datepicker-dropdown.datepicker-orient-top:before,
[data-theme='dark'] .datepicker-dropdown.datepicker-orient-top:after {
    border-top-color: #292F39;
}
[data-theme='dark'] .datepicker.datepicker-dropdown,
[data-theme|='translucent'] .datepicker.datepicker-dropdown {
    color: var(--bs-white);
}
[data-theme='dark'] .datepicker table tr td.day:hover,
[data-theme='dark'] .datepicker table tr td.focused,
[data-theme|='translucent'] .datepicker table tr td.day:hover,
[data-theme|='translucent'] .datepicker table tr td.focused,
[data-theme|='translucent'] .datepicker table tr td span.focused,
[data-theme|='translucent'] .datepicker table tr td span:hover {
    background-color: rgba(var(--bs-white-rgb), .2);
}
[data-theme='dark'] .datepicker table tr td.range:hover,
[data-theme|='translucent'] .datepicker table tr td.range:hover {
    color: var(--bs-white);
}
[data-theme='dark'] .form-control::-webkit-file-upload-button {
	color: #fff;
	background-color: rgba(var(--bs-white-rgb), .075);
}
[data-theme='dark'] .form-control:hover:not(:disabled):not([readonly])::-webkit-file-upload-button {
	background-color: rgba(var(--bs-white-rgb), .125);
}

/* 半透明 */
body[data-theme|='translucent'],
[data-theme|='translucent'] .card.card-fullscreen {
    color: rgba(var(--bs-white-rgb), .85);
    background-size: cover;
    background-attachment: fixed;
}
body[data-theme|='translucent'] .jconfirm .jconfirm-box {
    background: rgba(var(--bs-black-rgb), .125);
    backdrop-filter: blur(10px);
}
body[data-theme|='translucent'] caption,
body[data-theme|='translucent'] .figure-caption,
body[data-theme|='translucent'] .lyear-timeline-date time,
body[data-theme|='translucent'] .lyear-timeline-item-action {
    color: rgba(var(--bs-white-rgb), .85);
}
[data-theme|='translucent'] .card.card-fullscreen {
    backdrop-filter: blur(10px);
}
[data-theme|='translucent'] .modal-content {
    background: rgba(var(--bs-black-rgb), .125);
    backdrop-filter: blur(10px);
}
body.lyear-index[data-theme='translucent-blue'],
[data-theme='translucent-blue'] .popover,
[data-theme='translucent-blue'] .dropdown-menu,
[data-theme='translucent-blue'] .offcanvas,
[data-theme='translucent-blue'] #lyear-loading {
    background-image: -webkit-gradient(linear,left top,right top,from(#9f78ff),to(#32cafe));
    background-image: -webkit-linear-gradient(left,#9f78ff,#32cafe);
    background-image: -moz-linear-gradient(left,#9f78ff,#32cafe);
    background-image: -o-linear-gradient(left,#9f78ff,#32cafe);
    background-image: linear-gradient(to right,#9f78ff,#32cafe);
    background-repeat: repeat-x;
}
body.lyear-index[data-theme='translucent-red'],
[data-theme='translucent-red'] .popover,
[data-theme='translucent-red'] .dropdown-menu,
[data-theme='translucent-red'] .offcanvas,
[data-theme='translucent-red'] #lyear-loading {
    background-image: -webkit-gradient(linear,left top,right top,from(#ff5858),to(#e888b7));
    background-image: -webkit-linear-gradient(left,#ff5858,#e888b7);
    background-image: -moz-linear-gradient(left,#ff5858,#e888b7);
    background-image: -o-linear-gradient(left,#ff5858,#e888b7);
    background-image: linear-gradient(to right,#ff5858,#e888b7);
    background-repeat: repeat-x;
}
body.lyear-index[data-theme='translucent-green'],
[data-theme='translucent-green'] .popover,
[data-theme='translucent-green'] .dropdown-menu,
[data-theme='translucent-green'] .offcanvas,
[data-theme='translucent-green'] #lyear-loading {
    background-image: -webkit-gradient(linear,left top,right top,from(#00cef9),to(#00e6af));
    background-image: -webkit-linear-gradient(left,#00cef9,#00e6af);
    background-image: -moz-linear-gradient(left,#00cef9,#00e6af);
    background-image: -o-linear-gradient(left,#00cef9,#00e6af);
    background-image: linear-gradient(to right,#00cef9,#00e6af);
    background-repeat: repeat-x;
}
body.lyear-index[data-theme='translucent-yellow'],
[data-theme='translucent-yellow'] .popover,
[data-theme='translucent-yellow'] .dropdown-menu,
[data-theme='translucent-yellow'] .offcanvas,
[data-theme='translucent-yellow'] #lyear-loading {
    background-image: -webkit-gradient(linear,left top,right top,from(#ff8008),to(#ffc837));
    background-image: -webkit-linear-gradient(left,#ff8008,#ffc837);
    background-image: -moz-linear-gradient(left,#ff8008,#ffc837);
    background-image: -o-linear-gradient(left,#ff8008,#ffc837);
    background-image: linear-gradient(to right,#ff8008,#ffc837);
    background-repeat: repeat-x;
}
body.lyear-index[data-theme='translucent-cyan'],
[data-theme='translucent-cyan'] .popover,
[data-theme='translucent-cyan'] .dropdown-menu,
[data-theme='translucent-cyan'] .offcanvas,
[data-theme='translucent-cyan'] #lyear-loading {
    background-image: -webkit-gradient(linear,left top,right top,from(#514a9d),to(#24c6dc));
    background-image: -webkit-linear-gradient(left,#514a9d,#24c6dc);
    background-image: -moz-linear-gradient(left,#514a9d,#24c6dc);
    background-image: -o-linear-gradient(left,#514a9d,#24c6dc);
    background-image: linear-gradient(to right,#514a9d,#24c6dc);
    background-repeat: repeat-x;
}
body.lyear-index[data-theme='translucent-pink'],
[data-theme='translucent-pink'] .popover,
[data-theme='translucent-pink'] .dropdown-menu,
[data-theme='translucent-pink'] .offcanvas,
[data-theme='translucent-pink'] #lyear-loading {
    background-image: -webkit-gradient(linear,left top,right top,from(#a376fc),to(#f96f9b));
    background-image: -webkit-linear-gradient(left,#a376fc,#f96f9b);
    background-image: -moz-linear-gradient(left,#a376fc,#f96f9b);
    background-image: -o-linear-gradient(left,#a376fc,#f96f9b);
    background-image: linear-gradient(to right,#a376fc,#f96f9b);
    background-repeat: repeat-x;
}
[data-theme|='translucent'] .datepicker-dropdown:before,
[data-theme|='translucent'] .datepicker-dropdown:after {
    border-bottom-color: transparent;
}
[data-theme|='translucent'] .datepicker-dropdown.datepicker-orient-top:before,
[data-theme|='translucent'] .datepicker-dropdown.datepicker-orient-top:after {
    border-top-color: transparent;
}
[data-theme|='translucent'] .sidebar-header,
[data-theme|='translucent'] .nav-tabs .nav-link.active,
[data-theme|='translucent'] .nav-tabs .nav-item.show .nav-link {
    background-color: transparent;
}
[data-theme|='translucent'] .lyear-layout-sidebar-info,
[data-theme|='translucent'] .lyear-layout-header,
[data-theme|='translucent'] .card,
[data-theme|='translucent'] .mt-nav-bar .mt-nav,
[data-theme|='translucent'] .card>.card-header+.callout {
    background-color: rgba(var(--bs-black-rgb), .175);
}
[data-theme|='translucent'] ::-webkit-input-placeholder {
    color: rgba(var(--bs-white-rgb), .85);
}
[data-theme|='translucent'] :-moz-placeholder {
    color: rgba(var(--bs-white-rgb), .85);
}
[data-theme|='translucent'] ::-moz-placeholder {
    color: rgba(var(--bs-white-rgb), .85);
}
[data-theme|='translucent'] :-ms-input-placeholder {
    color: rgba(var(--bs-white-rgb), .85);
}
[data-theme|='translucent'] .sidebar-footer {
    border-color: rgba(var(--bs-white-rgb), .075);
}
[data-theme|='translucent'] .lyear-aside-toggler .lyear-toggler-bar {
    background-color: rgba(var(--bs-white-rgb), .85);
}
[data-theme|='translucent'] .mt-nav-bar {
    box-shadow: none;
}
[data-theme|='translucent'] .mt-nav-bar .nav-tabs,
[data-theme|='translucent'] .card-header,
[data-theme|='translucent'] .card-footer,
[data-theme|='translucent'] .lyear-divider::before,
[data-theme|='translucent'] .lyear-divider::after,
[data-theme|='translucent'] .card>.list-group,
[data-theme|='translucent'] .lyear-notifications-title,
[data-theme|='translucent'] .nav-tabs,
[data-theme|='translucent'] .popover-header {
    border-color: rgba(var(--bs-white-rgb), 0.125);
}
[data-theme|='translucent'] .lyear-close-tab,
[data-theme|='translucent'] .nav-tabs .nav-link,
[data-theme|='translucent'] .nav-tabs .nav-link.active,
[data-theme|='translucent'] .nav-tabs .nav-item.show .nav-link
[data-theme|='translucent'] .navbar-right a,
[data-theme|='translucent'] .lyear-notifications span,
[data-theme|='translucent'] .lyear-skin-title p,
[data-theme|='translucent'] .form-control,
[data-theme|='translucent'] .form-control-plaintext,
[data-theme|='translucent'] .form-control,
[data-theme|='translucent'] .form-select,
[data-theme|='translucent'] .input-group-text,
[data-theme|='translucent'] .dropdown-item,
[data-theme|='translucent'] .lyear-divider,
[data-theme|='translucent'] [class*='badge-outline-'],
[data-theme|='translucent'] .breadcrumb-item.active,
[data-theme|='translucent'] .breadcrumb-item+.breadcrumb-item::before,
[data-theme|='translucent'] .page-link,
[data-theme|='translucent'] .popover-header,
[data-theme|='translucent'] .popover-body,
[data-theme|='translucent'] .toast-header,
[data-theme|='translucent'] pre,
[data-theme|='translucent'] #lyear-loading,
[data-theme|='translucent'] .list-group-item,
[data-theme|='translucent'] .card-actions>li>a,
[data-theme|='translucent'] .lyear-notifications-title a,
[data-theme|='translucent'] .navbar-right a,
[data-theme|='translucent'] .navbar-light .navbar-brand,
[data-theme|='translucent'] .navbar-light .navbar-nav .nav-link.active,
[data-theme|='translucent'] .navbar-light .navbar-nav .show>.nav-link,
[data-theme|='translucent'] .navbar-light .navbar-nav .nav-link {
    color: rgba(var(--bs-white-rgb), .85);
}
[data-theme|='translucent'] pre,
[data-theme|='translucent'] .list-group-item {
    background-color: rgba(var(--bs-white-rgb), .075);
}
[data-theme|='translucent'] .callout {
    border-top-color: rgba(var(--bs-white-rgb), .075);
    border-right-color: rgba(var(--bs-white-rgb), .075);
    border-bottom-color: rgba(var(--bs-white-rgb), .075);
}
[data-theme|='translucent'] .card-footer {
    background-color: transparent;
}
[data-theme|='translucent'] .form-control,
[data-theme|='translucent'] .form-check-input,
[data-theme|='translucent'] .form-select,
[data-theme|='translucent'] .input-group-text,
[data-theme|='translucent'] .page-link,
[data-theme|='translucent'] .border-example-border-utils [class^="border"],
[data-theme|='translucent'] div.tagsinput,
[data-theme='dark'] div.tagsinput {
    background-color: rgba(var(--bs-white-rgb), 0.075);
    border: 1px solid rgba(var(--bs-white-rgb), 0.125);
}
[data-theme|='translucent'] .table {
    --bs-table-striped-color: rgba(var(--bs-white-rgb), .85);
    --bs-table-striped-bg: rgba(var(--bs-white-rgb), 0.075);
    --bs-table-active-color: rgba(var(--bs-white-rgb), .85);
    --bs-table-active-bg: rgba(var(--bs-black-rgb), .0375);
    --bs-table-hover-color: rgba(var(--bs-white-rgb), .85);
    --bs-table-hover-bg: rgba(var(--bs-white-rgb), 0.075);
    color: rgba(var(--bs-white-rgb), .85);
    border-color: rgba(var(--bs-white-rgb), 0.125);
}
[data-theme|='translucent'] .form-text,
[data-theme|='translucent'] .blockquote-footer,
[data-theme|='translucent'] .nav-tabs .nav-link.disabled,
[data-theme|='translucent'] .nav-link.disabled,
[data-theme|='translucent'] .list-group-item.disabled,
[data-theme|='translucent'] .list-group-item:disabled,
[data-theme|='translucent'] .nav-tabs .nav-link.disabled,
[data-theme|='translucent'] .navbar-light .navbar-nav .nav-link.disabled {
    color: rgba(var(--bs-white-rgb), .65);
}
[data-theme|='translucent'] .text-muted {
    color: rgba(var(--bs-white-rgb), .45)!important;
}
[data-theme|='translucent'] .form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23f8f9fa' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
}
[data-theme|='translucent'] .form-select:disabled {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23e9ecef' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
}
[data-theme='dark'] .form-select[multiple],
[data-theme='dark'] .form-select[size]:not([size="1"]),
[data-theme|='translucent'] .form-select[multiple],
[data-theme|='translucent'] .form-select[size]:not([size="1"]) {
    background-image: none;
}
[data-theme|='translucent'] .form-select:not([multiple]) option {
    background-color: var(--bs-gray-500);
}
[data-theme|='translucent'] .form-select[size]:not([size="1"]) option {
    background-color: transparent;
}
[data-theme|='translucent'] .form-select.is-invalid:not([multiple]):not([size]),
[data-theme|='translucent'] .form-select.is-invalid:not([multiple])[size="1"],
[data-theme|='translucent'] .was-validated .form-select:invalid:not([multiple]):not([size]),
[data-theme|='translucent'] .was-validated .form-select:invalid:not([multiple])[size="1"] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ced4da' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"),url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
}
[data-theme|='translucent'] .accordion-button {
    color: rgba(var(--bs-white-rgb), .85);
    background-color: transparent;
}
[data-theme|='translucent'] .modal-content,
[data-theme|='translucent'] .popover,
[data-theme|='translucent'] .dropdown-menu,
[data-theme|='translucent'] .offcanvas {
    border: none;
    -webkit-box-shadow: 0 0 4px rgba(var(--bs-black-rgb), .175);
    -moz-box-shadow: 0 0 4px rgba(var(--bs-black-rgb), .175);
    box-shadow: 0 0 4px rgba(var(--bs-black-rgb), .175);
}
[data-theme|='translucent'] .popover-header {
    background-color: transparent;
}
[data-theme|='translucent'] .toast-header {
    background-color: rgb(var(--bs-white-rgb), .175);
}
[data-theme|='translucent'] .toast {
    border: none;
    background-color: rgb(var(--bs-white-rgb), .175);
}
[data-theme|='translucent'] .bs-popover-auto[data-popper-placement^=right]>.popover-arrow::after,
[data-theme|='translucent'] .bs-popover-end>.popover-arrow::after {
    border-right-color: rgb(var(--bs-white-rgb), .375);
}
[data-theme|='translucent'] .bs-popover-auto[data-popper-placement^=top]>.popover-arrow::after,
[data-theme|='translucent'] .bs-popover-top>.popover-arrow::after {
    border-top-color: rgb(var(--bs-white-rgb), .375);
}
[data-theme|='translucent'] .bs-popover-auto[data-popper-placement^=bottom]>.popover-arrow::after,
[data-theme|='translucent'] .bs-popover-bottom>.popover-arrow::after {
    border-bottom-color: rgb(var(--bs-white-rgb), .375);
}
[data-theme|='translucent'] .bs-popover-auto[data-popper-placement^=left]>.popover-arrow::after,
[data-theme|='translucent'] .bs-popover-start>.popover-arrow::after {
    border-left-color: rgb(var(--bs-white-rgb), .375);
}
[data-theme|='translucent'] .bs-popover-auto[data-popper-placement^=right]>.popover-arrow::before,
[data-theme|='translucent'] .bs-popover-end>.popover-arrow::before {
    border-right-color: rgb(var(--bs-white-rgb), .375);
}
[data-theme|='translucent'] .bs-popover-auto[data-popper-placement^=top]>.popover-arrow::before,
[data-theme|='translucent'] .bs-popover-top>.popover-arrow::before {
    border-top-color: rgb(var(--bs-white-rgb), .375);
}
[data-theme|='translucent'] .bs-popover-auto[data-popper-placement^=bottom]>.popover-arrow::before,
[data-theme|='translucent'] .bs-popover-bottom>.popover-arrow::before,
[data-theme|='translucent'] .bs-popover-bottom .popover-header::before,
[data-theme|='translucent'] .bs-popover-auto[data-popper-placement^=bottom] .popover-header::before {
    border-bottom-color: rgb(var(--bs-white-rgb), .375);
}
[data-theme|='translucent'] .bs-popover-auto[data-popper-placement^=left]>.popover-arrow::before,
[data-theme|='translucent'] .bs-popover-start>.popover-arrow::before {
    border-left-color: rgb(var(--bs-white-rgb), .375);
}
[data-theme='dark'] .list-group-item-primary,
[data-theme|='translucent'] .list-group-item-primary {
    color: #084298;
    background-color: rgba(207, 226, 255, .375);
}
[data-theme='dark'] .list-group-item-secondary,
[data-theme|='translucent'] .list-group-item-secondary {
    color: #41464b;
    background-color: rgba(226, 227, 229, .375);
}
[data-theme='dark'] .list-group-item-success,
[data-theme|='translucent'] .list-group-item-success {
    color: #0f5132;
    background-color: rgba(209, 231, 221, .375);
}
[data-theme='dark'] .list-group-item-danger,
[data-theme|='translucent'] .list-group-item-danger {
    color: #842029;
    background-color: rgba(248, 215, 218, .375);
}
[data-theme='dark'] .list-group-item-warning,
[data-theme|='translucent'] .list-group-item-warning {
    color: #664d03;
    background-color: rgba(255, 243, 205, .375);
}
[data-theme='dark'] .list-group-item-info,
[data-theme|='translucent'] .list-group-item-info {
    color: #055160;
    background-color: rgba(207, 244, 252, .375);
}
[data-theme='dark'] .list-group-item-light,
[data-theme|='translucent'] .list-group-item-light {
    color: #636464;
    background-color: rgba(254, 254, 254, .375);
}
[data-theme='dark'] .list-group-item-dark,
[data-theme|='translucent'] .list-group-item-dark {
    color: #141619;
    background-color: rgba(211, 211, 212, .375);
}
[data-theme='dark'] .bg-light,
[data-theme|='translucent'] .bg-light {
    --bs-bg-opacity: 0.075;
    background-color: rgba(var(--bs-dark-rgb),var(--bs-bg-opacity))!important;
}
[data-theme|='translucent'] .nav-drawer .nav-subnav>li.active>a,
[data-theme|='translucent'] .nav-drawer .nav-subnav>li>a:hover {
    color: var(--bs-white);
}
[data-theme|='translucent'] .nav-drawer>.active>a,
[data-theme|='translucent'] .nav-tabs .nav-link.active,
[data-theme|='translucent'] .nav-tabs .nav-item.show .nav-link,
[data-theme|='translucent'] .nav-tabs .nav-link:hover,
[data-theme|='translucent'] .nav-tabs .nav-link:focus {
    border-color: var(--bs-gray-300);
}
[data-logobg*='color_'] .sidebar-header img,
[data-theme='dark'] .sidebar-header img,
[data-theme|='translucent'] .sidebar-header img {
    position: relative;
    -webkit-filter: brightness(275%);
	-moz-filter: brightness(275%);
    -ms-filter: brightness(275%);
    -o-filter: brightness(275%);
    filter: brightness(275%);
}
[data-theme='dark'] .nav-step .nav-link,
[data-theme='dark'] .nav-step .nav-link:before {
    background-color: rgba(var(--bs-gray-rgb), .3);
}
[data-theme|='translucent'] .nav-step .nav-link,
[data-theme|='translucent'] .nav-step .nav-link:before {
    background-color: rgba(var(--bs-white-rgb), .1);
}
[data-theme|='translucent'] .nav-step .nav-item.complete .nav-link,
[data-theme|='translucent'] .nav-step .nav-item.complete .nav-link::before,
[data-theme|='translucent'] .nav-step .nav-item  .nav-link.active,
[data-theme|='translucent'] .nav-step .nav-item  .nav-link.active::before {
	background-color: rgba(var(--bs-white-rgb), .2);
}
[data-theme|='translucent'] .nav-step .nav-item.complete .nav-link::after,
[data-theme|='translucent'] .nav-step .nav-item .nav-link.active::after {
	background-color: var(--bs-gray-300);
}
[data-theme|='translucent'] .nav-step .nav-item.complete .nav-link::after {
    color: rgba(var(--bs-black-rgb), 1);
}
[data-theme|='translucent'] div.tagsinput span.tag {
	background-color: rgba(var(--bs-black-rgb), .075);
}
[data-theme|='translucent'] div.tagsinput input {
	color: var(--bs-white)!important;
}
[data-theme|='translucent'] .form-control:focus,
[data-theme|='translucent'] .form-select:focus {
	border-color: var(--bs-gray-300);
	box-shadow: 0 0 0 0.25rem rgba(var(--bs-white-rgb), 0.25);
}
[data-theme|='translucent'] a:not(.btn),
[data-theme|='translucent'] .nav-link {
    color: var(--bs-gray-300);
}
[data-theme|='translucent'] .nav-link:hover,
[data-theme|='translucent'] .nav-link:focus {
	color: var(--bs-white);
}
[data-theme|='translucent'] .nav-pills .nav-link.active,
[data-theme|='translucent'] .nav-pills .show>.nav-link {
	background-color: rgba(var(--bs-white-rgb), .375);
}
[data-theme|='translucent'] .form-range:focus::-webkit-slider-thumb {
	box-shadow: 0 0 0 1px var(--bs-white), 0 0 0 0.25rem rgba(var(--bs-white-rgb), 0.25);
}
[data-theme|='translucent'] .form-range:focus::-moz-range-thumb {
	box-shadow: 0 0 0 1px var(--bs-white), 0 0 0 0.25rem rgba(var(--bs-white-rgb), 0.25);
}
[data-theme|='translucent'] .form-range::-webkit-slider-thumb {
	background-color: var(--bs-gray-300);
}
[data-theme|='translucent'] .form-range::-webkit-slider-thumb:active {
	background-color: rgba(var(--bs-white-rgb), 0.375);
}
[data-theme|='translucent'] .form-range::-moz-range-thumb {
	background-color: var(--bs-gray-300);
}
[data-theme|='translucent'] .form-range::-moz-range-thumb:active {
	background-color: rgba(var(--bs-white-rgb), 0.375);
}
[data-theme|='translucent'] .datepicker table tr td.active:active,
[data-theme|='translucent'] .datepicker table tr td.active.highlighted:active,
[data-theme|='translucent'] .datepicker table tr td.active.active,
[data-theme|='translucent'] .datepicker table tr td.active.highlighted.active,
[data-theme|='translucent'] .datepicker table tr td span.active:active,
[data-theme|='translucent'] .datepicker table tr td span.active:hover:active,
[data-theme|='translucent'] .datepicker table tr td span.active.disabled:active,
[data-theme|='translucent'] .datepicker table tr td span.active.disabled:hover:active,
[data-theme|='translucent'] .datepicker table tr td span.active.active,
[data-theme|='translucent'] .datepicker table tr td span.active:hover.active,
[data-theme|='translucent'] .datepicker table tr td span.active.disabled.active,
[data-theme|='translucent'] .datepicker table tr td span.active.disabled:hover.active {
    background-color: var(--bs-gray-300);
    border-color: var(--bs-gray-300);
}
[data-theme|='translucent'] .datepicker table tr td.active:active:hover,
[data-theme|='translucent'] .datepicker table tr td.active.highlighted:active:hover,
[data-theme|='translucent'] .datepicker table tr td.active.active:hover,
[data-theme|='translucent'] .datepicker table tr td.active.highlighted.active:hover,
[data-theme|='translucent'] .datepicker table tr td.active:active:focus,
[data-theme|='translucent'] .datepicker table tr td.active.highlighted:active:focus,
[data-theme|='translucent'] .datepicker table tr td.active.active:focus,
[data-theme|='translucent'] .datepicker table tr td.active.highlighted.active:focus,
[data-theme|='translucent'] .datepicker table tr td.active:active.focus,
[data-theme|='translucent'] .datepicker table tr td.active.highlighted:active.focus,
[data-theme|='translucent'] .datepicker table tr td.active.active.focus,
[data-theme|='translucent'] .datepicker table tr td.active.highlighted.active.focus {
    background-color: var(--bs-white-rgb);
    border-color: var(--bs-white-rgb);
}
[data-theme|='translucent'] .bootstrap-datetimepicker-widget.dropdown-menu.top:before,
[data-theme|='translucent'] .bootstrap-datetimepicker-widget.dropdown-menu.top:after {
    border-color: transparent;
}
[data-theme|='translucent'] .bootstrap-datetimepicker-widget table td span:hover,
[data-theme|='translucent'] .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,
[data-theme|='translucent'] .bootstrap-datetimepicker-widget table td.day:hover,
[data-theme|='translucent'] .bootstrap-datetimepicker-widget table td.hour:hover,
[data-theme|='translucent'] .bootstrap-datetimepicker-widget table td.minute:hover,
[data-theme|='translucent'] .bootstrap-datetimepicker-widget table td.second:hover {
    background:rgba(255,255,255,.375);
}


[data-theme|='translucent'] .fc-unthemed th,
[data-theme|='translucent'] .fc-unthemed td,
[data-theme|='translucent'] .fc-unthemed thead,
[data-theme|='translucent'] .fc-unthemed tbody,
[data-theme|='translucent'] .fc-unthemed .fc-divider,
[data-theme|='translucent'] .fc-unthemed .fc-row,
[data-theme|='translucent'] .fc-unthemed .fc-content,
[data-theme|='translucent'] .fc-unthemed .fc-popover,
[data-theme|='translucent'] .fc-unthemed .fc-list-view,
[data-theme|='translucent'] .fc-unthemed .fc-list-heading td,
[data-theme|='translucent'] .card-bordered {
    border-color: rgba(var(--bs-white-rgb), .375);
}
[data-theme|='translucent'] .fc-unthemed td.fc-today {
    background: rgba(var(--bs-white-rgb), .375);
}
[data-theme|='translucent'] .form-control::-webkit-file-upload-button {
	color: #fff;
	background-color: rgba(var(--bs-black-rgb), .075);
}
[data-theme|='translucent'] .form-control:hover:not(:disabled):not([readonly])::-webkit-file-upload-button,
[data-theme|='translucent'] .lyear-timeline-item-dot::before {
	background-color: rgba(var(--bs-black-rgb), .125);
}
[data-theme|='translucent'] [class*='badge-outline-'] {
	border-color: rgba(var(--bs-white-rgb), .375);
}
[data-theme|='translucent'] .lyear-timeline-item-content time {
    color: rgba(var(--bs-white-rgb), .375);
}
[data-theme|='translucent'] .alert {
    backdrop-filter: blur(10px);
}
[data-theme|='translucent'] .alert-primary {
    background-color: rgba(0, 123, 255, .45);
    border-color: rgba(0, 123, 255, .01);
    color: #00EFFF;
}
[data-theme|='translucent'] .alert-secondary {
    background-color: rgba(108, 117, 125, .45);
    border-color: rgba(108, 117, 125, .01);
    color: #E9EAED;
}
[data-theme|='translucent'] .alert-success {
    background-color: rgba(21, 195, 119, .45);
    border-color: rgba(21, 195, 119, .01);
    color: #C8F6EA;
}
[data-theme|='translucent'] .alert-info {
    background-color: rgba(72, 176, 247, .45);
    border-color: rgba(72, 176, 247, .01);
    color: #DFF4FB;
}
[data-theme|='translucent'] .alert-warning {
    background-color: rgba(250, 166, 75, .45);
    border-color: rgba(250, 166, 75, .01);
    color: #FBF4E4;
}
[data-theme|='translucent'] .alert-danger {
    background-color: rgba(244, 66, 54, .45);
    border-color: rgba(244, 66, 54, .01);
    color: #fa8181;
}
[data-theme|='translucent'] .alert-light {
    background-color: rgba(253,253,254, .45);
    border-color: rgba(253,253,254, .01);
    color: #FCFDFE;
}
[data-theme|='translucent'] .alert-dark {
    background-color: rgba(33, 37, 41, .45);
    border-color: rgba(33, 37, 41, .01);
    color: #D5D9DA;
}