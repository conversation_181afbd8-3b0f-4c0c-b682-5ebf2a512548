.lyear-dropdown-tree {
    position: relative;
}
.lyear-dropdown-tree .checkbox-box,
.lyear-dropdown-tree .radio-box {
    font-size: 15px;
    cursor: pointer;
}
.lyear-dropdown-tree .arrow {
    margin-left: -15px;
}
.lyear-dropdown-tree .checkbox-box:before {
    content: "\F0131";
}
.lyear-dropdown-tree .checkbox-box.lyear-dropdown-tree-checked:before {
    content: "\F0135";
}
.lyear-dropdown-tree .checkbox-box.lyear-dropdown-tree-half-checked:before {
    content: "\F06F2";
}
.lyear-dropdown-tree .radio-box:before {
    content: "\F0130";
}
.lyear-dropdown-tree .radio-box.lyear-dropdown-tree-checked:before {
    content: "\F0134";
}
.lyear-dropdown-tree .radio-box.lyear-dropdown-tree-half-checked:before {
    content: "\F0377";
}
.lyear-dropdown-tree .dropdown-menu {
    min-width: 100%;
    overflow-y: auto;
    overflow-x: auto;
    padding-bottom: 13px;
}
.lyear-dropdown-tree .dropdown-menu > li {
    margin-left:  15px;
}
.lyear-dropdown-tree .dropdown-menu li ul {
    list-style: none;
    padding-left: 25px;
}
.lyear-dropdown-tree .dropdown-menu li ul a {
    clear: both;
    font-weight: 400;
    line-height: 1.42857143;
    color: #333;
    white-space: nowrap;
    padding: 3px 20px;
}
.lyear-dropdown-tree .dropdown-menu li a {
    display: inline-block;
    padding-left: 15px!important;
    width: auto;
}
.lyear-dropdown-tree .dropdown-menu li a.lyear-dropdown-tree-a-checked {
    color: #33cabb;
}
.lyear-dropdown-tree .dropdown-menu a:hover,
.lyear-dropdown-tree .dropdown-menu a:active,
.lyear-dropdown-tree .dropdown-menu a:focus {
    background-color: transparent;
}
.lyear-dropdown-tree .form-control {
    padding-right: 30px;
    cursor: pointer;
}
.lyear-dropdown-tree i.lyear-cert {
    font-size: 20px;
    position: absolute;
    top: 3px;
    right: 5px;
}