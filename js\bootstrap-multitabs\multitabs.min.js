if("undefined"==typeof jQuery)throw new Error("MultiTabs requires jQuery");!function(a){"use strict";var b=".multitabs",d=function(a,c,d,e,f){var g=f?c:c.split(" ").join(b+" ")+b;a.off(g,d,e).on(g,d,e)},e=function(a,b){if("main"===a)return 0;var c=window.btoa(b);return c=c.replace(/\+/g,"_43"),c=c.replace(/\//g,"_47"),c=c.replace(/=/g,"_61")},h=function(b,c){var d,e,f,g;for(c=c||a.fn.multitabs.defaults.navTab.maxTitleLength,d=(b+"").split(" "),e="",f=0;f<d.length;f++)g=a.trim(d[f]),e+=g?g+" ":"";return e.length>c&&(e=e.substr(0,c),e+="..."),e},i=function(a){return!(void 0===sessionStorage)&&a},g=function(b){var c=0;return a(b).each(function(){c+=a(this).outerWidth(!0)}),c},f=function(a){var b=function(a){var b=document.createElement("a");return b.href=a,b.href}(a),c=window.location.protocol+"//"+window.location.host+"/",d=b.substr(0,c.length);return!(d===c)},j={"default":'<div class="mt-wrapper {mainClass}" style="height: 100%;" >  <div class="mt-nav-bar {navClass}" style="background-color: {backgroundColor};">    <div class="mt-nav mt-nav-tools-left">      <ul  class="nav {nav-tabs}">        <li class="nav-item mt-move-left"><a class="nav-link"><i class="mdi mdi-skip-backward"></i></a></li>      </ul>    </div>    <nav class="mt-nav mt-nav-panel">      <ul class="nav {nav-tabs}"></ul>    </nav>    <div class="mt-nav mt-nav-tools-right">      <ul  class="nav {nav-tabs}">        <li class="nav-item mt-move-right"><a class="nav-link"><i class="mdi mdi-skip-forward"></i></a></li>        <li class="nav-item mt-dropdown dropdown">          <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">{dropdown}<span class="caret"></span></a>          <ul role="menu" class="dropdown-menu dropdown-menu-right">            <li class="mt-show-actived-tab"><a class="dropdown-item">{showActivedTab}</a></li>            <li class="dropdown-divider"></li>            <li class="mt-close-all-tabs"><a class="dropdown-item">{closeAllTabs}</a></li>            <li class="mt-close-other-tabs"><a class="dropdown-item">{closeOtherTabs}</a></li>          </ul>        </li>      </ul>    </div>  </div>  <div class="tab-content mt-tab-content">    <div id="lyear-loading"><div class="spinner-border" role="status"><span class="visually-hidden"></span></div></div>  </div></div>',classic:'<div class="mt-wrapper {mainClass}" style="height: 100%;" >  <div class="mt-nav-bar {navClass}" style="background-color: {backgroundColor};">    <nav class="mt-nav mt-nav-panel">      <ul  class="nav {nav-tabs}"> </ul>    </nav>    <div class="mt-nav mt-nav-tools-right">      <ul  class="nav {nav-tabs}">        <li class="mt-dropdown dropdown">          <a href="#"  class="dropdown-toggle dropdown-item" data-toggle="dropdown">{dropdown}<span class="caret"></span></a>          <ul role="menu" class="mt-hidden-list dropdown-menu dropdown-menu-right"></ul>        </li>      </ul>    </div>  </div>  <div class="tab-content mt-tab-content " > </div></div>',simple:'<div class="mt-wrapper {mainClass}" style="height: 100%;" >  <div class="mt-nav-bar {navClass}" style="background-color: {backgroundColor};">    <nav class="mt-nav mt-nav-panel">      <ul  class="nav {nav-tabs}"> </ul>    </nav>  </div>  <div class="tab-content mt-tab-content " > </div></div>',navTab:'<a data-id="{navTabId}" class="nav-link mt-nav-tab" data-type="{type}" data-index="{index}" data-url="{url}">{title}</a>',closeBtn:' <i class="mt-close-tab mdi mdi-close" style="{style}"></i>',ajaxTabPane:'<div id="{tabPaneId}" class="tab-pane {class}">{content}</div>',iframeTabPane:'<iframe id="{tabPaneId}" allowtransparency="true" class="tab-pane {class}"  width="100%" height="100%" frameborder="0" src="" seamless></iframe>'},k=[{type:"main",title:"main",content:"<h1>Demo page</h1><h2>Welcome to use bootstrap multi-tabs :) </h2>"}],c=function(b,c){var d=this;d.$element=a(b),d._init(c)._listen()._final()};c.prototype={constructor:c,create:function(a,b){this.options;var d,e;return(d=this._getParam(a))?(e=this._exist(d),e&&!d.isNewTab?(this.active(e),this):(d.active=d.active?d.active:b,e=this._createNavTab(d),this._createTabPane(d),this._storage(d.did,d),d.active&&this.active(e),this)):this},_createTabPane:function(a){var b=this,c=b.$element;return c.tabContent.append(b._getTabPaneHtml(a)),c.tabContent.find("#"+a.did)},_getTabPaneHtml:function(a){var b=this,c=b.options;return!a.content&&a.iframe?j.iframeTabPane.replace("{class}",c.content.iframe.class).replace("{tabPaneId}",a.did):j.ajaxTabPane.replace("{class}",c.content.ajax.class).replace("{tabPaneId}",a.did).replace("{content}",a.content)},_createNavTab:function(a){var b=this,c=b.$element,d=b._getNavTabHtml(a),e=c.navPanelList.find('a[data-type="'+a.type+'"][data-index="'+a.index+'"]').parent("li");return e.length?(e.html(d),b._getTabPane(e.find("a:first")).remove()):c.navPanelList.append('<li class="nav-item">'+d+"</li>"),c.navPanelList.find('a[data-type="'+a.type+'"][data-index="'+a.index+'"]:first')},_getNavTabHtml:function(a){var b=this,c=b.options,e=c.nav.showCloseOnHover?"":"display:inline;",d="main"===a.type?"":j.closeBtn.replace("{style}",e);return j.navTab.replace("{index}",a.index).replace("{navTabId}",a.did).replace("{url}",a.url).replace("{title}",a.title).replace("{type}",a.type)+d},_generateId:function(a){return"multitabs_"+a.type+"_"+a.index},active:function(a,b){var e,f,g,h,i,j,c=this,d=c.$element;return b=0==b?!1:!0,e=c._getNavTab(a),f=c._getTabPane(e),g=d.navPanelList.find("li a.active"),h=g.length?c._getParam(g):{},i=e.length?c._getParam(e):{},j=c._storage(),j[h.did]&&(j[h.did].active=!1),j[i.did]&&(j[i.did].active=!0),c._resetStorage(j),g.removeClass("active"),e.addClass("active"),c._fixTabPosition(e),c._getTabPane(g).removeClass("active"),f.addClass("active"),c._fixTabContentLayout(f),c._fillTabPane(f,i,b),c},_fillTabPane:function(b,c,d){var e=this,f=e.options,g=a(b);g.html()||(g.is("iframe")?(!g.attr("src")||!g.attr("src")&&"no"==f.refresh||"nav"==f.refresh&&d||"all"==f.refresh)&&(a("#lyear-loading").fadeIn("fast",function(){g.attr("src",c.url)}),g[0].addEventListener||g[0].attachEvent("onload",function(){callbackTheme(g,function(){a("#lyear-loading").fadeOut("slow")})}),g[0].addEventListener("load",function(){callbackTheme(g,function(){a("#lyear-loading").fadeOut("slow")})},!0)):a.ajax({url:c.url,dataType:"html",success:function(a){g.html(f.content.ajax.success(a))},error:function(a){g.html(f.content.ajax.error(a))}}))},moveLeft:function(){var i,a=this,b=a.$element,c=Math.abs(parseInt(b.navPanelList.css("margin-left"))),d=b.navPanel.outerWidth(!0),e=g(b.navPanelList.children("li")),f=0,h=0;if(d>e)return a;for(i=b.navPanelList.children("li:first");h+i.width()<=c;)h+=i.outerWidth(!0),i=i.next();if(h=0,g(i.prevAll())>d){for(;h+i.width()<d&&i.length>0;)h+=i.outerWidth(!0),i=i.prev();f=g(i.prevAll())}return b.navPanelList.animate({marginLeft:0-f+"px"},"fast"),a},moveRight:function(){var h,i,a=this,b=a.$element,c=Math.abs(parseInt(b.navPanelList.css("margin-left"))),d=b.navPanel.outerWidth(!0),e=g(b.navPanelList.children("li")),f=0;if(d>e)return a;for(h=b.navPanelList.children("li:first"),i=0;i+h.width()<=c;)i+=h.outerWidth(!0),h=h.next();for(i=0;i+h.width()<d&&h.length>0;)i+=h.outerWidth(!0),h=h.next();return f=g(h.prevAll()),f>0&&b.navPanelList.animate({marginLeft:0-f+"px"},"fast"),a},close:function(b){var g,h,c=this,e=c._getNavTab(b),f=e.parent("li"),d=c._getTabPane(e);return f.length&&d.length&&d.hasClass("unsave")&&!c._unsaveConfirm()?c:(f.find("a").hasClass("active")&&(g=f.next("li:first"),h=f.prev("li:last"),g.length?(c.active(g),c.activeMenu(g.find("a"))):h.length&&(c.active(h),c.activeMenu(h.find("a")))),c._delStorage(e.attr("data-id")),f.remove(),d.remove(),a(".mt-tab-content").trigger("removeNode"),c)},closeOthers:function(b){var e,c=this,d=c.$element;return e=b?d.navPanelList.find('a:not([data-type="main"])').filter(function(){return b!=a(this).data("index")?this:void 0}):d.navPanelList.find('li a:not([data-type="main"],.active)'),e.each(function(){var b=a(this);c._delStorage(b.attr("data-id")),c._getTabPane(b).remove(),b.parent("li").remove()}),b&&(c.active(d.navPanelList.find('a[data-index="'+b+'"]')),c.activeMenu(d.navPanelList.find('a[data-index="'+b+'"]'))),d.navPanelList.css("margin-left","0"),a(".mt-tab-content").trigger("removeNode"),c},showActive:function(){var a=this,b=a.$element,c=b.navPanelList.find("li a.active");return a._fixTabPosition(c),a},closeAll:function(){var b=this,c=b.$element;return c.navPanelList.find('a:not([data-type="main"])').each(function(){var c=a(this);b._delStorage(c.attr("data-id")),b._getTabPane(c).remove(),c.parent("li").remove()}),b.active(c.navPanelList.find('a[data-type="main"]:first').parent("li")),b.activeMenu(c.navPanelList.find('a[data-type="main"]:first')),a(".mt-tab-content").trigger("removeNode"),b},activeMenu:function(b){var f,g,h,c=a("a[href$='"+a(b).data("url")+"']"),d=c.parents(".nav-item"),e=d.siblings().find(".nav-subnav:visible").outerHeight();a(".nav-drawer .nav-item").not(d).each(function(){window.innerWidth>1024&&a("body").hasClass("lyear-layout-sidebar-close")&&a(this).find(".nav-subnav").hide(),a(this).find(".nav-subnav:visible").slideUp(500),a(this).removeClass("active open")}),a(".nav-drawer").find("li").removeClass("active"),c.parent("li").addClass("active"),d.addClass("active"),c.parents(".nav-item").first().is(".nav-item-has-subnav")||(f=48*(c.parents(".nav-item").first().prevAll().length-1),a(".lyear-layout-sidebar-info").animate({scrollTop:f},300)),g=c.parents("ul.nav-subnav").filter(":hidden"),h=g.length,g.each(function(b){a(this).slideDown(500,function(){if(a(this).parent(".nav-item").addClass("open"),b===h-1){var c=0,f=a(".lyear-layout-sidebar-info"),g=d.last().prevAll().length,i=f.outerHeight(),j=a(".sidebar-main").outerHeight(),k=f.scrollTop(),l=a(this).outerHeight(),m=121;m+j-i>=48*g&&(c=48*g),1==d.length?f.animate({scrollTop:c},300):"undefined"!=typeof e&&null!=e?(c=k+l-e,f.animate({scrollTop:c},300)):0==k+i-f[0].scrollHeight&&(c=k-l,f.animate({scrollTop:c},300))}})})},_init:function(a){var b=this,c=b.$element;return c.html(j[a.nav.layout].replace("{mainClass}",a.class).replace("{navClass}",a.nav.class).replace(/\{nav-tabs\}/g,a.nav.style).replace(/\{backgroundColor\}/g,a.nav.backgroundColor).replace("{dropdown}",a.language.nav.dropdown).replace("{showActivedTab}",a.language.nav.showActivedTab).replace("{closeAllTabs}",a.language.nav.closeAllTabs).replace("{closeOtherTabs}",a.language.nav.closeOtherTabs)),c.wrapper=c.find(".mt-wrapper:first"),c.nav=c.find(".mt-nav-bar:first"),c.navToolsLeft=c.nav.find(".mt-nav-tools-left:first"),c.navPanel=c.nav.find(".mt-nav-panel:first"),c.navPanelList=c.nav.find(".mt-nav-panel:first ul"),c.navToolsRight=c.nav.find(".mt-nav-tools-right:first"),c.tabContent=c.find(".tab-content:first"),0==a.nav.showTabs&&c.nav.hide(),c.navPanel.css("width","calc(100% - 147px)"),b.options=a,b},_final:function(){var e,g,h,j,b=this,c=b.$element,d=b.options,f=d.init;if(i(d.cache)&&(e=b._storage(),b._resetStorage({}),a.each(e,function(a,c){b.create(c,!1),c.active&&(h=b._getParam(c),b.activeMenu(b._exist(h)))})),a.isEmptyObject(e))for(f=!a.isEmptyObject(f)&&f instanceof Array?f:k,j=0;j<f.length;j++)g=b._getParam(f[j]),g&&b.create(g);return c.navPanelList.children("li").find("a.active").length||b.active(c.navPanelList.find('[data-type="main"]:first')),b},_listen:function(){var f,b=this,c=b.$element,e=b.options;return d(a(document),"click",e.selector,function(){return b.create(this,!0),a(this).parent().parent("ul").hasClass("dropdown-menu")?void 0:!1}),d(c.nav,"click",".mt-nav-tab",function(){b.active(this,!1),b.activeMenu(this)}),e.nav.draggable&&d(c.navPanelList,"mousedown",".mt-nav-tab",function(b){var d=a(this),e=d.closest("li"),f=e.prev(),g=!0,h=!1,i="main"===d.data("type"),j="mt_tmp_id_"+(new Date).getTime(),k='<li id="'+j+'" class="mt-dragging" style="width:'+e.outerWidth()+"px; height:"+e.outerHeight()+'px;"><a style="width: 100%;  height: 100%; "></a></li>',l=b.pageX-e.offset().left+c.nav.offset().left;e.before(k),e.addClass("mt-dragging mt-dragging-tab").css({left:b.pageX-l+"px"}),a(document).on("mousemove",function(b){g&&!i&&(e.css({left:b.pageX-l+"px"}),c.navPanelList.children('li:not(".mt-dragging")').each(function(){var b=a(this).offset().left+a(this).outerWidth()+20;return b>e.offset().left?(a(this).next().attr("id")!==j&&(h=!0,f=a(this),a("#"+j).remove(),f.after(k)),!1):void 0}))}).on("selectstart",function(){return g?!1:void 0}).on("mouseup",function(){g&&(e.removeClass("mt-dragging mt-dragging-tab").css({left:"auto"}),h&&f.after(e),a("#"+j).remove()),g=!1})}),d(c.nav,"contextmenu",".mt-nav-tab",function(c){var d,e,f,g,h,i,j,l,k,m,n,o,p,q,r,s;for(c.preventDefault(),d=a('<ul class="dropdown-menu" role="menu" id="contextify-menu"/>'),e=a(this),f=e.closest("li"),g=b._getNavTab(f),h=b._getTabPane(g),i=g.length?b._getParam(g):{},j=[{text:"刷新",onclick:function(){var c=a(h);return c.is("iframe")?a("#lyear-loading").fadeIn("fast",function(){c.attr("src",i.url)}):a.ajax({url:i.url,dataType:"html",success:function(a){c.html(b.options.content.ajax.success(a))},error:function(a){c.html(b.options.content.ajax.error(a))}}),d.hide(),!1}}],i=b._getParam(g),"main"!==i.type&&j.push({text:"关闭",onclick:function(){return b.close(g),d.hide(),!1}}),j.push({text:"关闭其他",onclick:function(){return b.closeOthers(g.data("index")),d.hide(),!1}}),k=j.length,l=0;k>l;l++)m=j[l],n=a("<li/>"),n.append('<a class="dropdown-item" />'),o=n.find("a"),o.on("click",m.onclick),o.css("cursor","pointer"),o.html(m.text),d.append(n);p=a("#contextify-menu"),p.length>0?p!==d&&p.replaceWith(d):a("body").append(d),q=a(window).scrollTop()+c.clientY,r=d.width()+c.clientX<a(window).width()?c.clientX:c.clientX-d.width(),s=d.height()+c.clientY<a(window).height()?q:q-d.height(),d.css("top",s).css("left",r).css("position","fixed").show(),a(this).parents().on("click",function(){d.hide()}),a("#iframe-content").find("iframe").contents().find("body").on("click",function(){d.hide()})}),d(c.nav,"dblclick",".mt-nav-tab",function(){if(e.dbclickRefresh===!0){var d=a(this),f=d.closest("li"),g=b._getNavTab(f),h=b._getTabPane(g),i=g.length?b._getParam(g):{},j=a(h);j.is("iframe")?a("#lyear-loading").fadeIn("fast",function(){j.attr("src",i.url)}):a.ajax({url:i.url,dataType:"html",success:function(a){j.html(b.options.content.ajax.success(a))},error:function(a){j.html(b.options.content.ajax.error(a))}})}return!1}),d(c.nav,"click",".mt-close-tab",function(){return b.close(a(this).closest("li")),!1}),d(c.nav,"click",".mt-move-left",function(){return b.moveLeft(),!1}),d(c.nav,"click",".mt-move-right",function(){return b.moveRight(),!1}),d(c.nav,"click",".mt-show-actived-tab",function(){b.showActive()}),d(c.nav,"click",".mt-close-all-tabs",function(){b.closeAll()}),d(c.nav,"click",".mt-close-other-tabs",function(){b.closeOthers()}),f=c.nav.outerHeight(),c.tabContent.css("paddingTop",f),e.nav.fixed&&d(a(window),"scroll",function(){var b=a(this).scrollTop();return b=b<c.wrapper.height()-f?b+"px":"auto",c.nav.css("top",b),!1}),"classic"===e.nav.layout&&d(c.nav,"click",".mt-dropdown:not(.open)",function(){var a=b._getHiddenList(),d=c.navToolsRight.find(".mt-hidden-list:first").empty();if(a){for(;a.prevList.length;)d.append(a.prevList.shift().clone());for(;a.nextList.length;)d.append(a.nextList.shift().clone())}else d.append("<li>empty</li>")}),b},_getParam:function(b){if(a.isEmptyObject(b))return!1;var c=this,d=c.options,g={},i=a(b),j=i.data();return g.content=j.content||b.content||"",g.content.length?g.url="":(g.url=j.url||b.url||i.attr("href")||i.attr("url")||"",g.url=a.trim(decodeURIComponent(g.url.replace("#","")))),g.url.length||g.content.length?(g.isNewTab=j.hasOwnProperty("isNewTab")||b.hasOwnProperty("isNewTab")||d.isNewTab,g.iframe=j.iframe||b.iframe||f(g.url)||d.iframe,g.type=j.type||b.type||d.type,g.title=j.title||b.title||i.text()||g.url.replace("http://","").replace("https://","")||d.language.nav.title,g.title=h(g.title,d.nav.maxTitleLength),g.active=j.active||b.active||!1,g.index=j.index||b.index||e(g.type,g.url),g.did=j.did||b.did||this._generateId(g),g):!1},_storage:function(a,b){if(i(this.options.cache)){var c=JSON.parse(sessionStorage.multitabs||"{}");return a?b?(c[a]=b,sessionStorage.multitabs=JSON.stringify(c),c):c[a]:c}return{}},_delStorage:function(a){if(i(this.options.cache)){var b=JSON.parse(sessionStorage.multitabs||"{}");return a?(delete b[a],sessionStorage.multitabs=JSON.stringify(b),b):b}return{}},_resetStorage:function(a){i(this.options.cache)&&"object"==typeof a&&(sessionStorage.multitabs=JSON.stringify(a))},_exist:function(a){var b,c,d;return a&&a.url?(b=this,c=b.$element,d=c.navPanelList.find('a[data-url="'+a.url+'"]:first'),d.length?d:!1):!1},_getTabPane:function(b){return a("#"+a(b).attr("data-id"))},_getNavTab:function(b){var c=this,d=c.$element,e=a(b).attr("data-id")||a(b).find("a").attr("data-id");return d.navPanelList.find('a[data-id="'+e+'"]:first')},_fixTabPosition:function(b){var c=this,d=c.$element,e=a(b).parent("li"),f=e.outerWidth(!0),h=e.prev().outerWidth(!0),i=e.prev().prev().outerWidth(!0),j=g(e.prevAll()),k=g(e.nextAll()),l=d.navPanel.outerWidth(!0),m=g(d.navPanelList.children("li")),n=0;if(l>m)n=0;else if(l>=h+f+k)for(n=j;l>m-n+h;)e=e.prev(),n-=e.outerWidth();else j+f>l&&(n=j-h-i);n=n>0?n:0,d.navPanelList.animate({marginLeft:0-n+"px"},"fast")},_getHiddenList:function(){var i,j,a=this,b=a.$element,c=Math.abs(parseInt(b.navPanelList.css("margin-left"))),d=b.navPanel.outerWidth(!0),e=g(b.navPanelList.children("li")),f=[],h=[];if(d>e)return!1;for(i=b.navPanelList.children("li:first"),j=0;j+i.width()<=c;)j+=i.outerWidth(!0),f.push(i),i=i.next();if(e>j)for(i=b.navPanelList.children("li:last"),j=e;j>c+d;)j-=i.outerWidth(!0),h.unshift(i),i=i.prev();return{prevList:f,nextList:h}},_fixTabContentLayout:function(b){var c=a(b);c.is("iframe")?(a("body").addClass("full-height-layout"),c.css("height","99%"),window.setTimeout(function(){c.css("height","100%")},0)):a("body").removeClass("full-height-layout")}},a.fn.multitabs=function(b,d){var i,e=a(this),f=d?d:"multitabs",g=a(document).data(f),h="object"==typeof b&&b;return g||(i=a.extend(!0,{},a.fn.multitabs.defaults,h,e.data()),i.nav.style="nav-pills"===i.nav.style?"nav-pills":"nav-tabs",g=new c(this,i),a(document).data(f,g)),a(document).data(f)},a.fn.multitabs.defaults={selector:".multitabs",iframe:!1,cache:!1,"class":"",type:"info",init:[],isNewTab:!1,refresh:"no",dbclickRefresh:!1,nav:{backgroundColor:"#f5f5f5","class":"",draggable:!0,fixed:!1,layout:"default",showTabs:!0,maxTitleLength:25,showCloseOnHover:!1,style:"nav-tabs"},content:{ajax:{"class":"",error:function(a){return a},success:function(a){return a}},iframe:{"class":""}},language:{nav:{title:"Tab",dropdown:'<i class="mdi mdi-menu"></i>',showActivedTab:"显示当前选项卡",closeAllTabs:"关闭所有标签页",closeOtherTabs:"关闭其他标签页"}}}}(jQuery);