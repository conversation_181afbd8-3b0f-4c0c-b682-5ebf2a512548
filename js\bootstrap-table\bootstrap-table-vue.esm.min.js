/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.20.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

function t(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function e(e){for(var r=1;r<arguments.length;r++){var o=null!=arguments[r]?arguments[r]:{};r%2?t(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):t(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function n(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function r(t){return function(t){if(Array.isArray(t))return i(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||o(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(t,e){if(t){if("string"==typeof t)return i(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(t,e):void 0}}function i(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var a="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function u(t,e){return t(e={exports:{}},e.exports),e.exports}var c,f,l=function(t){return t&&t.Math==Math&&t},s=l("object"==typeof globalThis&&globalThis)||l("object"==typeof window&&window)||l("object"==typeof self&&self)||l("object"==typeof a&&a)||function(){return this}()||Function("return this")(),p=function(t){try{return!!t()}catch(t){return!0}},d=!p((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),y=!p((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),v=Function.prototype.call,b=y?v.bind(v):function(){return v.apply(v,arguments)},h={}.propertyIsEnumerable,g=Object.getOwnPropertyDescriptor,m={f:g&&!h.call({1:2},1)?function(t){var e=g(this,t);return!!e&&e.enumerable}:h},O=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},w=Function.prototype,x=w.bind,S=w.call,j=y&&x.bind(S,S),E=y?function(t){return t&&j(t)}:function(t){return t&&function(){return S.apply(t,arguments)}},A=E({}.toString),_=E("".slice),I=function(t){return _(A(t),8,-1)},T=s.Object,P=E("".split),$=p((function(){return!T("z").propertyIsEnumerable(0)}))?function(t){return"String"==I(t)?P(t,""):T(t)}:T,R=s.TypeError,C=function(t){if(null==t)throw R("Can't call method on "+t);return t},F=function(t){return $(C(t))},M=function(t){return"function"==typeof t},k=function(t){return"object"==typeof t?null!==t:M(t)},D=function(t){return M(t)?t:void 0},N=function(t,e){return arguments.length<2?D(s[t]):s[t]&&s[t][e]},z=E({}.isPrototypeOf),L=N("navigator","userAgent")||"",U=s.process,B=s.Deno,G=U&&U.versions||B&&B.version,K=G&&G.v8;K&&(f=(c=K.split("."))[0]>0&&c[0]<4?1:+(c[0]+c[1])),!f&&L&&(!(c=L.match(/Edge\/(\d+)/))||c[1]>=74)&&(c=L.match(/Chrome\/(\d+)/))&&(f=+c[1]);var W=f,X=!!Object.getOwnPropertySymbols&&!p((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&W&&W<41})),V=X&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Y=s.Object,q=V?function(t){return"symbol"==typeof t}:function(t){var e=N("Symbol");return M(e)&&z(e.prototype,Y(t))},Q=s.String,Z=s.TypeError,H=function(t){if(M(t))return t;throw Z(function(t){try{return Q(t)}catch(t){return"Object"}}(t)+" is not a function")},J=function(t,e){var n=t[e];return null==n?void 0:H(n)},tt=s.TypeError,et=Object.defineProperty,nt=function(t,e){try{et(s,t,{value:e,configurable:!0,writable:!0})}catch(n){s[t]=e}return e},rt=s["__core-js_shared__"]||nt("__core-js_shared__",{}),ot=u((function(t){(t.exports=function(t,e){return rt[t]||(rt[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.21.1",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})})),it=s.Object,at=function(t){return it(C(t))},ut=E({}.hasOwnProperty),ct=Object.hasOwn||function(t,e){return ut(at(t),e)},ft=0,lt=Math.random(),st=E(1..toString),pt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+st(++ft+lt,36)},dt=ot("wks"),yt=s.Symbol,vt=yt&&yt.for,bt=V?yt:yt&&yt.withoutSetter||pt,ht=function(t){if(!ct(dt,t)||!X&&"string"!=typeof dt[t]){var e="Symbol."+t;X&&ct(yt,t)?dt[t]=yt[t]:dt[t]=V&&vt?vt(e):bt(e)}return dt[t]},gt=s.TypeError,mt=ht("toPrimitive"),Ot=function(t,e){if(!k(t)||q(t))return t;var n,r=J(t,mt);if(r){if(void 0===e&&(e="default"),n=b(r,t,e),!k(n)||q(n))return n;throw gt("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var n,r;if("string"===e&&M(n=t.toString)&&!k(r=b(n,t)))return r;if(M(n=t.valueOf)&&!k(r=b(n,t)))return r;if("string"!==e&&M(n=t.toString)&&!k(r=b(n,t)))return r;throw tt("Can't convert object to primitive value")}(t,e)},wt=function(t){var e=Ot(t,"string");return q(e)?e:e+""},xt=s.document,St=k(xt)&&k(xt.createElement),jt=function(t){return St?xt.createElement(t):{}},Et=!d&&!p((function(){return 7!=Object.defineProperty(jt("div"),"a",{get:function(){return 7}}).a})),At=Object.getOwnPropertyDescriptor,_t={f:d?At:function(t,e){if(t=F(t),e=wt(e),Et)try{return At(t,e)}catch(t){}if(ct(t,e))return O(!b(m.f,t,e),t[e])}},It=d&&p((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Tt=s.String,Pt=s.TypeError,$t=function(t){if(k(t))return t;throw Pt(Tt(t)+" is not an object")},Rt=s.TypeError,Ct=Object.defineProperty,Ft=Object.getOwnPropertyDescriptor,Mt={f:d?It?function(t,e,n){if($t(t),e=wt(e),$t(n),"function"==typeof t&&"prototype"===e&&"value"in n&&"writable"in n&&!n.writable){var r=Ft(t,e);r&&r.writable&&(t[e]=n.value,n={configurable:"configurable"in n?n.configurable:r.configurable,enumerable:"enumerable"in n?n.enumerable:r.enumerable,writable:!1})}return Ct(t,e,n)}:Ct:function(t,e,n){if($t(t),e=wt(e),$t(n),Et)try{return Ct(t,e,n)}catch(t){}if("get"in n||"set"in n)throw Rt("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},kt=d?function(t,e,n){return Mt.f(t,e,O(1,n))}:function(t,e,n){return t[e]=n,t},Dt=E(Function.toString);M(rt.inspectSource)||(rt.inspectSource=function(t){return Dt(t)});var Nt,zt,Lt,Ut=rt.inspectSource,Bt=s.WeakMap,Gt=M(Bt)&&/native code/.test(Ut(Bt)),Kt=ot("keys"),Wt=function(t){return Kt[t]||(Kt[t]=pt(t))},Xt={},Vt=s.TypeError,Yt=s.WeakMap;if(Gt||rt.state){var qt=rt.state||(rt.state=new Yt),Qt=E(qt.get),Zt=E(qt.has),Ht=E(qt.set);Nt=function(t,e){if(Zt(qt,t))throw new Vt("Object already initialized");return e.facade=t,Ht(qt,t,e),e},zt=function(t){return Qt(qt,t)||{}},Lt=function(t){return Zt(qt,t)}}else{var Jt=Wt("state");Xt[Jt]=!0,Nt=function(t,e){if(ct(t,Jt))throw new Vt("Object already initialized");return e.facade=t,kt(t,Jt,e),e},zt=function(t){return ct(t,Jt)?t[Jt]:{}},Lt=function(t){return ct(t,Jt)}}var te={set:Nt,get:zt,has:Lt,enforce:function(t){return Lt(t)?zt(t):Nt(t,{})},getterFor:function(t){return function(e){var n;if(!k(e)||(n=zt(e)).type!==t)throw Vt("Incompatible receiver, "+t+" required");return n}}},ee=Function.prototype,ne=d&&Object.getOwnPropertyDescriptor,re=ct(ee,"name"),oe={EXISTS:re,PROPER:re&&"something"===function(){}.name,CONFIGURABLE:re&&(!d||d&&ne(ee,"name").configurable)},ie=u((function(t){var e=oe.CONFIGURABLE,n=te.get,r=te.enforce,o=String(String).split("String");(t.exports=function(t,n,i,a){var u,c=!!a&&!!a.unsafe,f=!!a&&!!a.enumerable,l=!!a&&!!a.noTargetGet,p=a&&void 0!==a.name?a.name:n;M(i)&&("Symbol("===String(p).slice(0,7)&&(p="["+String(p).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!ct(i,"name")||e&&i.name!==p)&&kt(i,"name",p),(u=r(i)).source||(u.source=o.join("string"==typeof p?p:""))),t!==s?(c?!l&&t[n]&&(f=!0):delete t[n],f?t[n]=i:kt(t,n,i)):f?t[n]=i:nt(n,i)})(Function.prototype,"toString",(function(){return M(this)&&n(this).source||Ut(this)}))})),ae=Math.ceil,ue=Math.floor,ce=function(t){var e=+t;return e!=e||0===e?0:(e>0?ue:ae)(e)},fe=Math.max,le=Math.min,se=Math.min,pe=function(t){return t>0?se(ce(t),9007199254740991):0},de=function(t){return pe(t.length)},ye=function(t){return function(e,n,r){var o,i=F(e),a=de(i),u=function(t,e){var n=ce(t);return n<0?fe(n+e,0):le(n,e)}(r,a);if(t&&n!=n){for(;a>u;)if((o=i[u++])!=o)return!0}else for(;a>u;u++)if((t||u in i)&&i[u]===n)return t||u||0;return!t&&-1}},ve={includes:ye(!0),indexOf:ye(!1)}.indexOf,be=E([].push),he=function(t,e){var n,r=F(t),o=0,i=[];for(n in r)!ct(Xt,n)&&ct(r,n)&&be(i,n);for(;e.length>o;)ct(r,n=e[o++])&&(~ve(i,n)||be(i,n));return i},ge=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],me=ge.concat("length","prototype"),Oe={f:Object.getOwnPropertyNames||function(t){return he(t,me)}},we={f:Object.getOwnPropertySymbols},xe=E([].concat),Se=N("Reflect","ownKeys")||function(t){var e=Oe.f($t(t)),n=we.f;return n?xe(e,n(t)):e},je=function(t,e,n){for(var r=Se(e),o=Mt.f,i=_t.f,a=0;a<r.length;a++){var u=r[a];ct(t,u)||n&&ct(n,u)||o(t,u,i(e,u))}},Ee=/#|\.prototype\./,Ae=function(t,e){var n=Ie[_e(t)];return n==Pe||n!=Te&&(M(e)?p(e):!!e)},_e=Ae.normalize=function(t){return String(t).replace(Ee,".").toLowerCase()},Ie=Ae.data={},Te=Ae.NATIVE="N",Pe=Ae.POLYFILL="P",$e=Ae,Re=_t.f,Ce=function(t,e){var n,r,o,i,a,u=t.target,c=t.global,f=t.stat;if(n=c?s:f?s[u]||nt(u,{}):(s[u]||{}).prototype)for(r in e){if(i=e[r],o=t.noTargetGet?(a=Re(n,r))&&a.value:n[r],!$e(c?r:u+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;je(i,o)}(t.sham||o&&o.sham)&&kt(i,"sham",!0),ie(n,r,i,t)}},Fe={};Fe[ht("toStringTag")]="z";var Me,ke="[object z]"===String(Fe),De=ht("toStringTag"),Ne=s.Object,ze="Arguments"==I(function(){return arguments}()),Le=ke?I:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=Ne(t),De))?n:ze?I(e):"Object"==(r=I(e))&&M(e.callee)?"Arguments":r},Ue=s.String,Be=function(t){if("Symbol"===Le(t))throw TypeError("Cannot convert a Symbol value to a string");return Ue(t)},Ge=function(){var t=$t(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e},Ke=s.RegExp,We=p((function(){var t=Ke("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),Xe=We||p((function(){return!Ke("a","y").sticky})),Ve={BROKEN_CARET:We||p((function(){var t=Ke("^r","gy");return t.lastIndex=2,null!=t.exec("str")})),MISSED_STICKY:Xe,UNSUPPORTED_Y:We},Ye=Object.keys||function(t){return he(t,ge)},qe=d&&!It?Object.defineProperties:function(t,e){$t(t);for(var n,r=F(e),o=Ye(e),i=o.length,a=0;i>a;)Mt.f(t,n=o[a++],r[n]);return t},Qe={f:qe},Ze=N("document","documentElement"),He=Wt("IE_PROTO"),Je=function(){},tn=function(t){return"<script>"+t+"<\/script>"},en=function(t){t.write(tn("")),t.close();var e=t.parentWindow.Object;return t=null,e},nn=function(){try{Me=new ActiveXObject("htmlfile")}catch(t){}var t,e;nn="undefined"!=typeof document?document.domain&&Me?en(Me):((e=jt("iframe")).style.display="none",Ze.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(tn("document.F=Object")),t.close(),t.F):en(Me);for(var n=ge.length;n--;)delete nn.prototype[ge[n]];return nn()};Xt[He]=!0;var rn,on,an=Object.create||function(t,e){var n;return null!==t?(Je.prototype=$t(t),n=new Je,Je.prototype=null,n[He]=t):n=nn(),void 0===e?n:Qe.f(n,e)},un=s.RegExp,cn=p((function(){var t=un(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)})),fn=s.RegExp,ln=p((function(){var t=fn("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})),sn=te.get,pn=ot("native-string-replace",String.prototype.replace),dn=RegExp.prototype.exec,yn=dn,vn=E("".charAt),bn=E("".indexOf),hn=E("".replace),gn=E("".slice),mn=(on=/b*/g,b(dn,rn=/a/,"a"),b(dn,on,"a"),0!==rn.lastIndex||0!==on.lastIndex),On=Ve.BROKEN_CARET,wn=void 0!==/()??/.exec("")[1];(mn||wn||On||cn||ln)&&(yn=function(t){var e,n,r,o,i,a,u,c=this,f=sn(c),l=Be(t),s=f.raw;if(s)return s.lastIndex=c.lastIndex,e=b(yn,s,l),c.lastIndex=s.lastIndex,e;var p=f.groups,d=On&&c.sticky,y=b(Ge,c),v=c.source,h=0,g=l;if(d&&(y=hn(y,"y",""),-1===bn(y,"g")&&(y+="g"),g=gn(l,c.lastIndex),c.lastIndex>0&&(!c.multiline||c.multiline&&"\n"!==vn(l,c.lastIndex-1))&&(v="(?: "+v+")",g=" "+g,h++),n=new RegExp("^(?:"+v+")",y)),wn&&(n=new RegExp("^"+v+"$(?!\\s)",y)),mn&&(r=c.lastIndex),o=b(dn,d?n:c,g),d?o?(o.input=gn(o.input,h),o[0]=gn(o[0],h),o.index=c.lastIndex,c.lastIndex+=o[0].length):c.lastIndex=0:mn&&o&&(c.lastIndex=c.global?o.index+o[0].length:r),wn&&o&&o.length>1&&b(pn,o[0],n,(function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(o[i]=void 0)})),o&&p)for(o.groups=a=an(null),i=0;i<p.length;i++)a[(u=p[i])[0]]=o[u[1]];return o});var xn=yn;Ce({target:"RegExp",proto:!0,forced:/./.exec!==xn},{exec:xn});var Sn=Function.prototype,jn=Sn.apply,En=Sn.call,An="object"==typeof Reflect&&Reflect.apply||(y?En.bind(jn):function(){return En.apply(jn,arguments)}),_n=ht("species"),In=RegExp.prototype,Tn=E("".charAt),Pn=E("".charCodeAt),$n=E("".slice),Rn=function(t){return function(e,n){var r,o,i=Be(C(e)),a=ce(n),u=i.length;return a<0||a>=u?t?"":void 0:(r=Pn(i,a))<55296||r>56319||a+1===u||(o=Pn(i,a+1))<56320||o>57343?t?Tn(i,a):r:t?$n(i,a,a+2):o-56320+(r-55296<<10)+65536}},Cn={codeAt:Rn(!1),charAt:Rn(!0)}.charAt,Fn=function(t,e,n){return e+(n?Cn(t,e).length:1)},Mn=Math.floor,kn=E("".charAt),Dn=E("".replace),Nn=E("".slice),zn=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,Ln=/\$([$&'`]|\d{1,2})/g,Un=function(t,e,n,r,o,i){var a=n+t.length,u=r.length,c=Ln;return void 0!==o&&(o=at(o),c=zn),Dn(i,c,(function(i,c){var f;switch(kn(c,0)){case"$":return"$";case"&":return t;case"`":return Nn(e,0,n);case"'":return Nn(e,a);case"<":f=o[Nn(c,1,-1)];break;default:var l=+c;if(0===l)return i;if(l>u){var s=Mn(l/10);return 0===s?i:s<=u?void 0===r[s-1]?kn(c,1):r[s-1]+kn(c,1):i}f=r[l-1]}return void 0===f?"":f}))},Bn=s.TypeError,Gn=function(t,e){var n=t.exec;if(M(n)){var r=b(n,t,e);return null!==r&&$t(r),r}if("RegExp"===I(t))return b(xn,t,e);throw Bn("RegExp#exec called on incompatible receiver")},Kn=ht("replace"),Wn=Math.max,Xn=Math.min,Vn=E([].concat),Yn=E([].push),qn=E("".indexOf),Qn=E("".slice),Zn="$0"==="a".replace(/./,"$0"),Hn=!!/./[Kn]&&""===/./[Kn]("a","$0");!function(t,e,n,r){var o=ht(t),i=!p((function(){var e={};return e[o]=function(){return 7},7!=""[t](e)})),a=i&&!p((function(){var e=!1,n=/a/;return"split"===t&&((n={}).constructor={},n.constructor[_n]=function(){return n},n.flags="",n[o]=/./[o]),n.exec=function(){return e=!0,null},n[o](""),!e}));if(!i||!a||n){var u=E(/./[o]),c=e(o,""[t],(function(t,e,n,r,o){var a=E(t),c=e.exec;return c===xn||c===In.exec?i&&!o?{done:!0,value:u(e,n,r)}:{done:!0,value:a(n,e,r)}:{done:!1}}));ie(String.prototype,t,c[0]),ie(In,o,c[1])}r&&kt(In[o],"sham",!0)}("replace",(function(t,e,n){var r=Hn?"$":"$0";return[function(t,n){var r=C(this),o=null==t?void 0:J(t,Kn);return o?b(o,t,r,n):b(e,Be(r),t,n)},function(t,o){var i=$t(this),a=Be(t);if("string"==typeof o&&-1===qn(o,r)&&-1===qn(o,"$<")){var u=n(e,i,a,o);if(u.done)return u.value}var c=M(o);c||(o=Be(o));var f=i.global;if(f){var l=i.unicode;i.lastIndex=0}for(var s=[];;){var p=Gn(i,a);if(null===p)break;if(Yn(s,p),!f)break;""===Be(p[0])&&(i.lastIndex=Fn(a,pe(i.lastIndex),l))}for(var d,y="",v=0,b=0;b<s.length;b++){for(var h=Be((p=s[b])[0]),g=Wn(Xn(ce(p.index),a.length),0),m=[],O=1;O<p.length;O++)Yn(m,void 0===(d=p[O])?d:String(d));var w=p.groups;if(c){var x=Vn([h],m,g,a);void 0!==w&&Yn(x,w);var S=Be(An(o,void 0,x))}else S=Un(h,a,g,m,w,o);g>=v&&(y+=Qn(a,v,g)+S,v=g+h.length)}return y+Qn(a,v)}]}),!!p((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!Zn||Hn);var Jn=Array.isArray||function(t){return"Array"==I(t)},tr=function(t,e,n){var r=wt(e);r in t?Mt.f(t,r,O(0,n)):t[r]=n},er=function(){},nr=[],rr=N("Reflect","construct"),or=/^\s*(?:class|function)\b/,ir=E(or.exec),ar=!or.exec(er),ur=function(t){if(!M(t))return!1;try{return rr(er,nr,t),!0}catch(t){return!1}},cr=function(t){if(!M(t))return!1;switch(Le(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return ar||!!ir(or,Ut(t))}catch(t){return!0}};cr.sham=!0;var fr,lr=!rr||p((function(){var t;return ur(ur.call)||!ur(Object)||!ur((function(){t=!0}))||t}))?cr:ur,sr=ht("species"),pr=s.Array,dr=function(t,e){return new(function(t){var e;return Jn(t)&&(e=t.constructor,(lr(e)&&(e===pr||Jn(e.prototype))||k(e)&&null===(e=e[sr]))&&(e=void 0)),void 0===e?pr:e}(t))(0===e?0:e)},yr=ht("species"),vr=ht("isConcatSpreadable"),br=s.TypeError,hr=W>=51||!p((function(){var t=[];return t[vr]=!1,t.concat()[0]!==t})),gr=(fr="concat",W>=51||!p((function(){var t=[];return(t.constructor={})[yr]=function(){return{foo:1}},1!==t[fr](Boolean).foo}))),mr=function(t){if(!k(t))return!1;var e=t[vr];return void 0!==e?!!e:Jn(t)};Ce({target:"Array",proto:!0,forced:!hr||!gr},{concat:function(t){var e,n,r,o,i,a=at(this),u=dr(a,0),c=0;for(e=-1,r=arguments.length;e<r;e++)if(mr(i=-1===e?a:arguments[e])){if(c+(o=de(i))>9007199254740991)throw br("Maximum allowed index exceeded");for(n=0;n<o;n++,c++)n in i&&tr(u,c,i[n])}else{if(c>=9007199254740991)throw br("Maximum allowed index exceeded");tr(u,c++,i)}return u.length=c,u}});var Or=window.jQuery,wr=function(t){return void 0===t?t:Or.extend(!0,Array.isArray(t)?[]:{},t)};function xr(t,e,n,r,o,i,a,u,c,f){"boolean"!=typeof a&&(c=u,u=a,a=!1);const l="function"==typeof n?n.options:n;let s;if(t&&t.render&&(l.render=t.render,l.staticRenderFns=t.staticRenderFns,l._compiled=!0,o&&(l.functional=!0)),r&&(l._scopeId=r),i?(s=function(t){(t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),e&&e.call(this,c(t)),t&&t._registeredComponents&&t._registeredComponents.add(i)},l._ssrRegister=s):e&&(s=a?function(t){e.call(this,f(t,this.$root.$options.shadowRoot))}:function(t){e.call(this,u(t))}),s)if(l.functional){const t=l.render;l.render=function(e,n){return s.call(n),t(e,n)}}else{const t=l.beforeCreate;l.beforeCreate=t?[].concat(t,s):[s]}return n}const Sr=xr({render:function(){var t=this.$createElement;return(this._self._c||t)("table")},staticRenderFns:[]},undefined,{name:"BootstrapTable",props:{columns:{type:Array,require:!0},data:{type:[Array,Object],default:function(){}},options:{type:Object,default:function(){return{}}}},mounted:function(){var t=this;this.$table=Or(this.$el),this.$table.on("all.bs.table",(function(e,n,o){var i=Or.fn.bootstrapTable.events[n];i=i.replace(/([A-Z])/g,"-$1").toLowerCase(),t.$emit.apply(t,["on-all"].concat(r(o))),t.$emit.apply(t,[i].concat(r(o)))})),this._initTable()},methods:e({_initTable:function(){var t=e(e({},wr(this.options)),{},{columns:wr(this.columns),data:wr(this.data)});this._hasInit?this.refreshOptions(t):(this.$table.bootstrapTable(t),this._hasInit=!0)}},function(){var t,e={},n=function(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=o(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,c=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return u=t.done,t},e:function(t){c=!0,a=t},f:function(){try{u||null==n.return||n.return()}finally{if(c)throw a}}}}(Or.fn.bootstrapTable.methods);try{var r=function(){var n=t.value;e[n]=function(){for(var t,e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];return(t=this.$table).bootstrapTable.apply(t,[n].concat(r))}};for(n.s();!(t=n.n()).done;)r()}catch(t){n.e(t)}finally{n.f()}return e}()),watch:{options:{handler:function(){this._initTable()},deep:!0},columns:{handler:function(){this._initTable()},deep:!0},data:{handler:function(){this.load(wr(this.data))},deep:!0}}},undefined,false,undefined,!1,void 0,void 0,void 0);export{Sr as default};
