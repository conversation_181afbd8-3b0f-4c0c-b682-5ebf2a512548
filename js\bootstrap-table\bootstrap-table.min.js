/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.20.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):(t="undefined"!=typeof globalThis?globalThis:t||self).BootstrapTable=e(t.jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var i=e(t);function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function r(t,e,i){return e&&a(t.prototype,e),i&&a(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==i)return;var n,o,a=[],r=!0,s=!1;try{for(i=i.call(t);!(r=(n=i.next()).done)&&(a.push(n.value),!e||a.length!==e);r=!0);}catch(t){s=!0,o=t}finally{try{r||null==i.return||i.return()}finally{if(s)throw o}}return a}(t,e)||c(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t){return function(t){if(Array.isArray(t))return h(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||c(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t,e){if(t){if("string"==typeof t)return h(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?h(t,e):void 0}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function u(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=c(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,r=!0,s=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return r=t.done,t},e:function(t){s=!0,a=t},f:function(){try{r||null==i.return||i.return()}finally{if(s)throw a}}}}var d="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function f(t,e){return t(e={exports:{}},e.exports),e.exports}var p,g,v=function(t){return t&&t.Math==Math&&t},b=v("object"==typeof globalThis&&globalThis)||v("object"==typeof window&&window)||v("object"==typeof self&&self)||v("object"==typeof d&&d)||function(){return this}()||Function("return this")(),m=function(t){try{return!!t()}catch(t){return!0}},y=!m((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),w=!m((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),S=Function.prototype.call,x=w?S.bind(S):function(){return S.apply(S,arguments)},k={}.propertyIsEnumerable,O=Object.getOwnPropertyDescriptor,C=O&&!k.call({1:2},1)?function(t){var e=O(this,t);return!!e&&e.enumerable}:k,T={f:C},P=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},I=Function.prototype,A=I.bind,$=I.call,R=w&&A.bind($,$),E=w?function(t){return t&&R(t)}:function(t){return t&&function(){return $.apply(t,arguments)}},j=E({}.toString),F=E("".slice),_=function(t){return F(j(t),8,-1)},N=b.Object,D=E("".split),V=m((function(){return!N("z").propertyIsEnumerable(0)}))?function(t){return"String"==_(t)?D(t,""):N(t)}:N,B=b.TypeError,L=function(t){if(null==t)throw B("Can't call method on "+t);return t},H=function(t){return V(L(t))},M=function(t){return"function"==typeof t},U=function(t){return"object"==typeof t?null!==t:M(t)},z=function(t){return M(t)?t:void 0},q=function(t,e){return arguments.length<2?z(b[t]):b[t]&&b[t][e]},W=E({}.isPrototypeOf),G=q("navigator","userAgent")||"",K=b.process,Y=b.Deno,J=K&&K.versions||Y&&Y.version,X=J&&J.v8;X&&(g=(p=X.split("."))[0]>0&&p[0]<4?1:+(p[0]+p[1])),!g&&G&&(!(p=G.match(/Edge\/(\d+)/))||p[1]>=74)&&(p=G.match(/Chrome\/(\d+)/))&&(g=+p[1]);var Q=g,Z=!!Object.getOwnPropertySymbols&&!m((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&Q&&Q<41})),tt=Z&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,et=b.Object,it=tt?function(t){return"symbol"==typeof t}:function(t){var e=q("Symbol");return M(e)&&W(e.prototype,et(t))},nt=b.String,ot=function(t){try{return nt(t)}catch(t){return"Object"}},at=b.TypeError,rt=function(t){if(M(t))return t;throw at(ot(t)+" is not a function")},st=function(t,e){var i=t[e];return null==i?void 0:rt(i)},lt=b.TypeError,ct=Object.defineProperty,ht=function(t,e){try{ct(b,t,{value:e,configurable:!0,writable:!0})}catch(i){b[t]=e}return e},ut="__core-js_shared__",dt=b[ut]||ht(ut,{}),ft=f((function(t){(t.exports=function(t,e){return dt[t]||(dt[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.21.1",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})})),pt=b.Object,gt=function(t){return pt(L(t))},vt=E({}.hasOwnProperty),bt=Object.hasOwn||function(t,e){return vt(gt(t),e)},mt=0,yt=Math.random(),wt=E(1..toString),St=function(t){return"Symbol("+(void 0===t?"":t)+")_"+wt(++mt+yt,36)},xt=ft("wks"),kt=b.Symbol,Ot=kt&&kt.for,Ct=tt?kt:kt&&kt.withoutSetter||St,Tt=function(t){if(!bt(xt,t)||!Z&&"string"!=typeof xt[t]){var e="Symbol."+t;Z&&bt(kt,t)?xt[t]=kt[t]:xt[t]=tt&&Ot?Ot(e):Ct(e)}return xt[t]},Pt=b.TypeError,It=Tt("toPrimitive"),At=function(t,e){if(!U(t)||it(t))return t;var i,n=st(t,It);if(n){if(void 0===e&&(e="default"),i=x(n,t,e),!U(i)||it(i))return i;throw Pt("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var i,n;if("string"===e&&M(i=t.toString)&&!U(n=x(i,t)))return n;if(M(i=t.valueOf)&&!U(n=x(i,t)))return n;if("string"!==e&&M(i=t.toString)&&!U(n=x(i,t)))return n;throw lt("Can't convert object to primitive value")}(t,e)},$t=function(t){var e=At(t,"string");return it(e)?e:e+""},Rt=b.document,Et=U(Rt)&&U(Rt.createElement),jt=function(t){return Et?Rt.createElement(t):{}},Ft=!y&&!m((function(){return 7!=Object.defineProperty(jt("div"),"a",{get:function(){return 7}}).a})),_t=Object.getOwnPropertyDescriptor,Nt={f:y?_t:function(t,e){if(t=H(t),e=$t(e),Ft)try{return _t(t,e)}catch(t){}if(bt(t,e))return P(!x(T.f,t,e),t[e])}},Dt=y&&m((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Vt=b.String,Bt=b.TypeError,Lt=function(t){if(U(t))return t;throw Bt(Vt(t)+" is not an object")},Ht=b.TypeError,Mt=Object.defineProperty,Ut=Object.getOwnPropertyDescriptor,zt="enumerable",qt="configurable",Wt="writable",Gt={f:y?Dt?function(t,e,i){if(Lt(t),e=$t(e),Lt(i),"function"==typeof t&&"prototype"===e&&"value"in i&&Wt in i&&!i.writable){var n=Ut(t,e);n&&n.writable&&(t[e]=i.value,i={configurable:qt in i?i.configurable:n.configurable,enumerable:zt in i?i.enumerable:n.enumerable,writable:!1})}return Mt(t,e,i)}:Mt:function(t,e,i){if(Lt(t),e=$t(e),Lt(i),Ft)try{return Mt(t,e,i)}catch(t){}if("get"in i||"set"in i)throw Ht("Accessors not supported");return"value"in i&&(t[e]=i.value),t}},Kt=y?function(t,e,i){return Gt.f(t,e,P(1,i))}:function(t,e,i){return t[e]=i,t},Yt=E(Function.toString);M(dt.inspectSource)||(dt.inspectSource=function(t){return Yt(t)});var Jt,Xt,Qt,Zt=dt.inspectSource,te=b.WeakMap,ee=M(te)&&/native code/.test(Zt(te)),ie=ft("keys"),ne=function(t){return ie[t]||(ie[t]=St(t))},oe={},ae="Object already initialized",re=b.TypeError,se=b.WeakMap;if(ee||dt.state){var le=dt.state||(dt.state=new se),ce=E(le.get),he=E(le.has),ue=E(le.set);Jt=function(t,e){if(he(le,t))throw new re(ae);return e.facade=t,ue(le,t,e),e},Xt=function(t){return ce(le,t)||{}},Qt=function(t){return he(le,t)}}else{var de=ne("state");oe[de]=!0,Jt=function(t,e){if(bt(t,de))throw new re(ae);return e.facade=t,Kt(t,de,e),e},Xt=function(t){return bt(t,de)?t[de]:{}},Qt=function(t){return bt(t,de)}}var fe={set:Jt,get:Xt,has:Qt,enforce:function(t){return Qt(t)?Xt(t):Jt(t,{})},getterFor:function(t){return function(e){var i;if(!U(e)||(i=Xt(e)).type!==t)throw re("Incompatible receiver, "+t+" required");return i}}},pe=Function.prototype,ge=y&&Object.getOwnPropertyDescriptor,ve=bt(pe,"name"),be={EXISTS:ve,PROPER:ve&&"something"===function(){}.name,CONFIGURABLE:ve&&(!y||y&&ge(pe,"name").configurable)},me=f((function(t){var e=be.CONFIGURABLE,i=fe.get,n=fe.enforce,o=String(String).split("String");(t.exports=function(t,i,a,r){var s,l=!!r&&!!r.unsafe,c=!!r&&!!r.enumerable,h=!!r&&!!r.noTargetGet,u=r&&void 0!==r.name?r.name:i;M(a)&&("Symbol("===String(u).slice(0,7)&&(u="["+String(u).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!bt(a,"name")||e&&a.name!==u)&&Kt(a,"name",u),(s=n(a)).source||(s.source=o.join("string"==typeof u?u:""))),t!==b?(l?!h&&t[i]&&(c=!0):delete t[i],c?t[i]=a:Kt(t,i,a)):c?t[i]=a:ht(i,a)})(Function.prototype,"toString",(function(){return M(this)&&i(this).source||Zt(this)}))})),ye=Math.ceil,we=Math.floor,Se=function(t){var e=+t;return e!=e||0===e?0:(e>0?we:ye)(e)},xe=Math.max,ke=Math.min,Oe=function(t,e){var i=Se(t);return i<0?xe(i+e,0):ke(i,e)},Ce=Math.min,Te=function(t){return t>0?Ce(Se(t),9007199254740991):0},Pe=function(t){return Te(t.length)},Ie=function(t){return function(e,i,n){var o,a=H(e),r=Pe(a),s=Oe(n,r);if(t&&i!=i){for(;r>s;)if((o=a[s++])!=o)return!0}else for(;r>s;s++)if((t||s in a)&&a[s]===i)return t||s||0;return!t&&-1}},Ae={includes:Ie(!0),indexOf:Ie(!1)},$e=Ae.indexOf,Re=E([].push),Ee=function(t,e){var i,n=H(t),o=0,a=[];for(i in n)!bt(oe,i)&&bt(n,i)&&Re(a,i);for(;e.length>o;)bt(n,i=e[o++])&&(~$e(a,i)||Re(a,i));return a},je=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Fe=je.concat("length","prototype"),_e={f:Object.getOwnPropertyNames||function(t){return Ee(t,Fe)}},Ne={f:Object.getOwnPropertySymbols},De=E([].concat),Ve=q("Reflect","ownKeys")||function(t){var e=_e.f(Lt(t)),i=Ne.f;return i?De(e,i(t)):e},Be=function(t,e,i){for(var n=Ve(e),o=Gt.f,a=Nt.f,r=0;r<n.length;r++){var s=n[r];bt(t,s)||i&&bt(i,s)||o(t,s,a(e,s))}},Le=/#|\.prototype\./,He=function(t,e){var i=Ue[Me(t)];return i==qe||i!=ze&&(M(e)?m(e):!!e)},Me=He.normalize=function(t){return String(t).replace(Le,".").toLowerCase()},Ue=He.data={},ze=He.NATIVE="N",qe=He.POLYFILL="P",We=He,Ge=Nt.f,Ke=function(t,e){var i,n,o,a,r,s=t.target,l=t.global,c=t.stat;if(i=l?b:c?b[s]||ht(s,{}):(b[s]||{}).prototype)for(n in e){if(a=e[n],o=t.noTargetGet?(r=Ge(i,n))&&r.value:i[n],!We(l?n:s+(c?".":"#")+n,t.forced)&&void 0!==o){if(typeof a==typeof o)continue;Be(a,o)}(t.sham||o&&o.sham)&&Kt(a,"sham",!0),me(i,n,a,t)}},Ye=Object.keys||function(t){return Ee(t,je)},Je=Object.assign,Xe=Object.defineProperty,Qe=E([].concat),Ze=!Je||m((function(){if(y&&1!==Je({b:1},Je(Xe({},"a",{enumerable:!0,get:function(){Xe(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},i=Symbol(),n="abcdefghijklmnopqrst";return t[i]=7,n.split("").forEach((function(t){e[t]=t})),7!=Je({},t)[i]||Ye(Je({},e)).join("")!=n}))?function(t,e){for(var i=gt(t),n=arguments.length,o=1,a=Ne.f,r=T.f;n>o;)for(var s,l=V(arguments[o++]),c=a?Qe(Ye(l),a(l)):Ye(l),h=c.length,u=0;h>u;)s=c[u++],y&&!x(r,l,s)||(i[s]=l[s]);return i}:Je;Ke({target:"Object",stat:!0,forced:Object.assign!==Ze},{assign:Ze});var ti={};ti[Tt("toStringTag")]="z";var ei,ii="[object z]"===String(ti),ni=Tt("toStringTag"),oi=b.Object,ai="Arguments"==_(function(){return arguments}()),ri=ii?_:function(t){var e,i,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=function(t,e){try{return t[e]}catch(t){}}(e=oi(t),ni))?i:ai?_(e):"Object"==(n=_(e))&&M(e.callee)?"Arguments":n},si=b.String,li=function(t){if("Symbol"===ri(t))throw TypeError("Cannot convert a Symbol value to a string");return si(t)},ci="\t\n\v\f\r                　\u2028\u2029\ufeff",hi=E("".replace),ui="["+ci+"]",di=RegExp("^"+ui+ui+"*"),fi=RegExp(ui+ui+"*$"),pi=function(t){return function(e){var i=li(L(e));return 1&t&&(i=hi(i,di,"")),2&t&&(i=hi(i,fi,"")),i}},gi={start:pi(1),end:pi(2),trim:pi(3)},vi=be.PROPER,bi=gi.trim;Ke({target:"String",proto:!0,forced:(ei="trim",m((function(){return!!ci[ei]()||"​᠎"!=="​᠎"[ei]()||vi&&ci[ei].name!==ei})))},{trim:function(){return bi(this)}});var mi=function(t,e){var i=[][t];return!!i&&m((function(){i.call(null,e||function(){return 1},1)}))},yi=E([].join),wi=V!=Object,Si=mi("join",",");Ke({target:"Array",proto:!0,forced:wi||!Si},{join:function(t){return yi(H(this),void 0===t?",":t)}});var xi,ki=function(){var t=Lt(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e},Oi=b.RegExp,Ci=m((function(){var t=Oi("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),Ti=Ci||m((function(){return!Oi("a","y").sticky})),Pi={BROKEN_CARET:Ci||m((function(){var t=Oi("^r","gy");return t.lastIndex=2,null!=t.exec("str")})),MISSED_STICKY:Ti,UNSUPPORTED_Y:Ci},Ii=y&&!Dt?Object.defineProperties:function(t,e){Lt(t);for(var i,n=H(e),o=Ye(e),a=o.length,r=0;a>r;)Gt.f(t,i=o[r++],n[i]);return t},Ai={f:Ii},$i=q("document","documentElement"),Ri=ne("IE_PROTO"),Ei=function(){},ji=function(t){return"<script>"+t+"</"+"script>"},Fi=function(t){t.write(ji("")),t.close();var e=t.parentWindow.Object;return t=null,e},_i=function(){try{xi=new ActiveXObject("htmlfile")}catch(t){}var t,e;_i="undefined"!=typeof document?document.domain&&xi?Fi(xi):((e=jt("iframe")).style.display="none",$i.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(ji("document.F=Object")),t.close(),t.F):Fi(xi);for(var i=je.length;i--;)delete _i.prototype[je[i]];return _i()};oe[Ri]=!0;var Ni=Object.create||function(t,e){var i;return null!==t?(Ei.prototype=Lt(t),i=new Ei,Ei.prototype=null,i[Ri]=t):i=_i(),void 0===e?i:Ai.f(i,e)},Di=b.RegExp,Vi=m((function(){var t=Di(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)})),Bi=b.RegExp,Li=m((function(){var t=Bi("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})),Hi=fe.get,Mi=ft("native-string-replace",String.prototype.replace),Ui=RegExp.prototype.exec,zi=Ui,qi=E("".charAt),Wi=E("".indexOf),Gi=E("".replace),Ki=E("".slice),Yi=function(){var t=/a/,e=/b*/g;return x(Ui,t,"a"),x(Ui,e,"a"),0!==t.lastIndex||0!==e.lastIndex}(),Ji=Pi.BROKEN_CARET,Xi=void 0!==/()??/.exec("")[1];(Yi||Xi||Ji||Vi||Li)&&(zi=function(t){var e,i,n,o,a,r,s,l=this,c=Hi(l),h=li(t),u=c.raw;if(u)return u.lastIndex=l.lastIndex,e=x(zi,u,h),l.lastIndex=u.lastIndex,e;var d=c.groups,f=Ji&&l.sticky,p=x(ki,l),g=l.source,v=0,b=h;if(f&&(p=Gi(p,"y",""),-1===Wi(p,"g")&&(p+="g"),b=Ki(h,l.lastIndex),l.lastIndex>0&&(!l.multiline||l.multiline&&"\n"!==qi(h,l.lastIndex-1))&&(g="(?: "+g+")",b=" "+b,v++),i=new RegExp("^(?:"+g+")",p)),Xi&&(i=new RegExp("^"+g+"$(?!\\s)",p)),Yi&&(n=l.lastIndex),o=x(Ui,f?i:l,b),f?o?(o.input=Ki(o.input,v),o[0]=Ki(o[0],v),o.index=l.lastIndex,l.lastIndex+=o[0].length):l.lastIndex=0:Yi&&o&&(l.lastIndex=l.global?o.index+o[0].length:n),Xi&&o&&o.length>1&&x(Mi,o[0],i,(function(){for(a=1;a<arguments.length-2;a++)void 0===arguments[a]&&(o[a]=void 0)})),o&&d)for(o.groups=r=Ni(null),a=0;a<d.length;a++)r[(s=d[a])[0]]=o[s[1]];return o});var Qi=zi;Ke({target:"RegExp",proto:!0,forced:/./.exec!==Qi},{exec:Qi});var Zi=Function.prototype,tn=Zi.apply,en=Zi.call,nn="object"==typeof Reflect&&Reflect.apply||(w?en.bind(tn):function(){return en.apply(tn,arguments)}),on=Tt("species"),an=RegExp.prototype,rn=function(t,e,i,n){var o=Tt(t),a=!m((function(){var e={};return e[o]=function(){return 7},7!=""[t](e)})),r=a&&!m((function(){var e=!1,i=/a/;return"split"===t&&((i={}).constructor={},i.constructor[on]=function(){return i},i.flags="",i[o]=/./[o]),i.exec=function(){return e=!0,null},i[o](""),!e}));if(!a||!r||i){var s=E(/./[o]),l=e(o,""[t],(function(t,e,i,n,o){var r=E(t),l=e.exec;return l===Qi||l===an.exec?a&&!o?{done:!0,value:s(e,i,n)}:{done:!0,value:r(i,e,n)}:{done:!1}}));me(String.prototype,t,l[0]),me(an,o,l[1])}n&&Kt(an[o],"sham",!0)},sn=Tt("match"),ln=function(t){var e;return U(t)&&(void 0!==(e=t[sn])?!!e:"RegExp"==_(t))},cn=function(){},hn=[],un=q("Reflect","construct"),dn=/^\s*(?:class|function)\b/,fn=E(dn.exec),pn=!dn.exec(cn),gn=function(t){if(!M(t))return!1;try{return un(cn,hn,t),!0}catch(t){return!1}},vn=function(t){if(!M(t))return!1;switch(ri(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return pn||!!fn(dn,Zt(t))}catch(t){return!0}};vn.sham=!0;var bn=!un||m((function(){var t;return gn(gn.call)||!gn(Object)||!gn((function(){t=!0}))||t}))?vn:gn,mn=b.TypeError,yn=Tt("species"),wn=function(t,e){var i,n=Lt(t).constructor;return void 0===n||null==(i=Lt(n)[yn])?e:function(t){if(bn(t))return t;throw mn(ot(t)+" is not a constructor")}(i)},Sn=E("".charAt),xn=E("".charCodeAt),kn=E("".slice),On=function(t){return function(e,i){var n,o,a=li(L(e)),r=Se(i),s=a.length;return r<0||r>=s?t?"":void 0:(n=xn(a,r))<55296||n>56319||r+1===s||(o=xn(a,r+1))<56320||o>57343?t?Sn(a,r):n:t?kn(a,r,r+2):o-56320+(n-55296<<10)+65536}},Cn={codeAt:On(!1),charAt:On(!0)}.charAt,Tn=function(t,e,i){return e+(i?Cn(t,e).length:1)},Pn=function(t,e,i){var n=$t(e);n in t?Gt.f(t,n,P(0,i)):t[n]=i},In=b.Array,An=Math.max,$n=function(t,e,i){for(var n=Pe(t),o=Oe(e,n),a=Oe(void 0===i?n:i,n),r=In(An(a-o,0)),s=0;o<a;o++,s++)Pn(r,s,t[o]);return r.length=s,r},Rn=b.TypeError,En=function(t,e){var i=t.exec;if(M(i)){var n=x(i,t,e);return null!==n&&Lt(n),n}if("RegExp"===_(t))return x(Qi,t,e);throw Rn("RegExp#exec called on incompatible receiver")},jn=Pi.UNSUPPORTED_Y,Fn=4294967295,_n=Math.min,Nn=[].push,Dn=E(/./.exec),Vn=E(Nn),Bn=E("".slice),Ln=!m((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var i="ab".split(t);return 2!==i.length||"a"!==i[0]||"b"!==i[1]}));rn("split",(function(t,e,i){var n;return n="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,i){var n=li(L(this)),o=void 0===i?Fn:i>>>0;if(0===o)return[];if(void 0===t)return[n];if(!ln(t))return x(e,n,t,o);for(var a,r,s,l=[],c=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),h=0,u=new RegExp(t.source,c+"g");(a=x(Qi,u,n))&&!((r=u.lastIndex)>h&&(Vn(l,Bn(n,h,a.index)),a.length>1&&a.index<n.length&&nn(Nn,l,$n(a,1)),s=a[0].length,h=r,l.length>=o));)u.lastIndex===a.index&&u.lastIndex++;return h===n.length?!s&&Dn(u,"")||Vn(l,""):Vn(l,Bn(n,h)),l.length>o?$n(l,0,o):l}:"0".split(void 0,0).length?function(t,i){return void 0===t&&0===i?[]:x(e,this,t,i)}:e,[function(e,i){var o=L(this),a=null==e?void 0:st(e,t);return a?x(a,e,o,i):x(n,li(o),e,i)},function(t,o){var a=Lt(this),r=li(t),s=i(n,a,r,o,n!==e);if(s.done)return s.value;var l=wn(a,RegExp),c=a.unicode,h=(a.ignoreCase?"i":"")+(a.multiline?"m":"")+(a.unicode?"u":"")+(jn?"g":"y"),u=new l(jn?"^(?:"+a.source+")":a,h),d=void 0===o?Fn:o>>>0;if(0===d)return[];if(0===r.length)return null===En(u,r)?[r]:[];for(var f=0,p=0,g=[];p<r.length;){u.lastIndex=jn?0:p;var v,b=En(u,jn?Bn(r,p):r);if(null===b||(v=_n(Te(u.lastIndex+(jn?p:0)),r.length))===f)p=Tn(r,p,c);else{if(Vn(g,Bn(r,f,p)),g.length===d)return g;for(var m=1;m<=b.length-1;m++)if(Vn(g,b[m]),g.length===d)return g;p=f=v}}return Vn(g,Bn(r,f)),g}]}),!Ln,jn);var Hn=E(T.f),Mn=E([].push),Un=function(t){return function(e){for(var i,n=H(e),o=Ye(n),a=o.length,r=0,s=[];a>r;)i=o[r++],y&&!Hn(n,i)||Mn(s,t?[i,n[i]]:n[i]);return s}},zn={entries:Un(!0),values:Un(!1)}.entries;Ke({target:"Object",stat:!0},{entries:function(t){return zn(t)}});var qn=Tt("unscopables"),Wn=Array.prototype;null==Wn[qn]&&Gt.f(Wn,qn,{configurable:!0,value:Ni(null)});var Gn=function(t){Wn[qn][t]=!0},Kn=Ae.includes;Ke({target:"Array",proto:!0},{includes:function(t){return Kn(this,t,arguments.length>1?arguments[1]:void 0)}}),Gn("includes");var Yn=Array.isArray||function(t){return"Array"==_(t)},Jn=Tt("species"),Xn=b.Array,Qn=function(t,e){return new(function(t){var e;return Yn(t)&&(e=t.constructor,(bn(e)&&(e===Xn||Yn(e.prototype))||U(e)&&null===(e=e[Jn]))&&(e=void 0)),void 0===e?Xn:e}(t))(0===e?0:e)},Zn=Tt("species"),to=function(t){return Q>=51||!m((function(){var e=[];return(e.constructor={})[Zn]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},eo=Tt("isConcatSpreadable"),io=9007199254740991,no="Maximum allowed index exceeded",oo=b.TypeError,ao=Q>=51||!m((function(){var t=[];return t[eo]=!1,t.concat()[0]!==t})),ro=to("concat"),so=function(t){if(!U(t))return!1;var e=t[eo];return void 0!==e?!!e:Yn(t)};Ke({target:"Array",proto:!0,forced:!ao||!ro},{concat:function(t){var e,i,n,o,a,r=gt(this),s=Qn(r,0),l=0;for(e=-1,n=arguments.length;e<n;e++)if(so(a=-1===e?r:arguments[e])){if(l+(o=Pe(a))>io)throw oo(no);for(i=0;i<o;i++,l++)i in a&&Pn(s,l,a[i])}else{if(l>=io)throw oo(no);Pn(s,l++,a)}return s.length=l,s}});var lo=E(E.bind),co=E([].push),ho=function(t){var e=1==t,i=2==t,n=3==t,o=4==t,a=6==t,r=7==t,s=5==t||a;return function(l,c,h,u){for(var d,f,p=gt(l),g=V(p),v=function(t,e){return rt(t),void 0===e?t:w?lo(t,e):function(){return t.apply(e,arguments)}}(c,h),b=Pe(g),m=0,y=u||Qn,S=e?y(l,b):i||r?y(l,0):void 0;b>m;m++)if((s||m in g)&&(f=v(d=g[m],m,p),t))if(e)S[m]=f;else if(f)switch(t){case 3:return!0;case 5:return d;case 6:return m;case 2:co(S,d)}else switch(t){case 4:return!1;case 7:co(S,d)}return a?-1:n||o?o:S}},uo={forEach:ho(0),map:ho(1),filter:ho(2),some:ho(3),every:ho(4),find:ho(5),findIndex:ho(6),filterReject:ho(7)},fo=uo.find,po="find",go=!0;po in[]&&Array(1).find((function(){go=!1})),Ke({target:"Array",proto:!0,forced:go},{find:function(t){return fo(this,t,arguments.length>1?arguments[1]:void 0)}}),Gn(po);var vo=ii?{}.toString:function(){return"[object "+ri(this)+"]"};ii||me(Object.prototype,"toString",vo,{unsafe:!0});var bo=b.TypeError,mo=function(t){if(ln(t))throw bo("The method doesn't accept regular expressions");return t},yo=Tt("match"),wo=function(t){var e=/./;try{"/./"[t](e)}catch(i){try{return e[yo]=!1,"/./"[t](e)}catch(t){}}return!1},So=E("".indexOf);Ke({target:"String",proto:!0,forced:!wo("includes")},{includes:function(t){return!!~So(li(L(this)),li(mo(t)),arguments.length>1?arguments[1]:void 0)}});var xo={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},ko=jt("span").classList,Oo=ko&&ko.constructor&&ko.constructor.prototype,Co=Oo===Object.prototype?void 0:Oo,To=uo.forEach,Po=mi("forEach")?[].forEach:function(t){return To(this,t,arguments.length>1?arguments[1]:void 0)},Io=function(t){if(t&&t.forEach!==Po)try{Kt(t,"forEach",Po)}catch(e){t.forEach=Po}};for(var Ao in xo)xo[Ao]&&Io(b[Ao]&&b[Ao].prototype);Io(Co);var $o=gi.trim,Ro=E("".charAt),Eo=b.parseFloat,jo=b.Symbol,Fo=jo&&jo.iterator,_o=1/Eo(ci+"-0")!=-1/0||Fo&&!m((function(){Eo(Object(Fo))}))?function(t){var e=$o(li(t)),i=Eo(e);return 0===i&&"-"==Ro(e,0)?-0:i}:Eo;Ke({global:!0,forced:parseFloat!=_o},{parseFloat:_o});var No=Ae.indexOf,Do=E([].indexOf),Vo=!!Do&&1/Do([1],1,-0)<0,Bo=mi("indexOf");Ke({target:"Array",proto:!0,forced:Vo||!Bo},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return Vo?Do(this,t,e)||0:No(this,t,e)}});var Lo=Math.floor,Ho=function(t,e){var i=t.length,n=Lo(i/2);return i<8?Mo(t,e):Uo(t,Ho($n(t,0,n),e),Ho($n(t,n),e),e)},Mo=function(t,e){for(var i,n,o=t.length,a=1;a<o;){for(n=a,i=t[a];n&&e(t[n-1],i)>0;)t[n]=t[--n];n!==a++&&(t[n]=i)}return t},Uo=function(t,e,i,n){for(var o=e.length,a=i.length,r=0,s=0;r<o||s<a;)t[r+s]=r<o&&s<a?n(e[r],i[s])<=0?e[r++]:i[s++]:r<o?e[r++]:i[s++];return t},zo=Ho,qo=G.match(/firefox\/(\d+)/i),Wo=!!qo&&+qo[1],Go=/MSIE|Trident/.test(G),Ko=G.match(/AppleWebKit\/(\d+)\./),Yo=!!Ko&&+Ko[1],Jo=[],Xo=E(Jo.sort),Qo=E(Jo.push),Zo=m((function(){Jo.sort(void 0)})),ta=m((function(){Jo.sort(null)})),ea=mi("sort"),ia=!m((function(){if(Q)return Q<70;if(!(Wo&&Wo>3)){if(Go)return!0;if(Yo)return Yo<603;var t,e,i,n,o="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:i=3;break;case 68:case 71:i=4;break;default:i=2}for(n=0;n<47;n++)Jo.push({k:e+n,v:i})}for(Jo.sort((function(t,e){return e.v-t.v})),n=0;n<Jo.length;n++)e=Jo[n].k.charAt(0),o.charAt(o.length-1)!==e&&(o+=e);return"DGBEFHACIJK"!==o}}));Ke({target:"Array",proto:!0,forced:Zo||!ta||!ea||!ia},{sort:function(t){void 0!==t&&rt(t);var e=gt(this);if(ia)return void 0===t?Xo(e):Xo(e,t);var i,n,o=[],a=Pe(e);for(n=0;n<a;n++)n in e&&Qo(o,e[n]);for(zo(o,function(t){return function(e,i){return void 0===i?-1:void 0===e?1:void 0!==t?+t(e,i)||0:li(e)>li(i)?1:-1}}(t)),i=o.length,n=0;n<i;)e[n]=o[n++];for(;n<a;)delete e[n++];return e}});var na=Math.floor,oa=E("".charAt),aa=E("".replace),ra=E("".slice),sa=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,la=/\$([$&'`]|\d{1,2})/g,ca=function(t,e,i,n,o,a){var r=i+t.length,s=n.length,l=la;return void 0!==o&&(o=gt(o),l=sa),aa(a,l,(function(a,l){var c;switch(oa(l,0)){case"$":return"$";case"&":return t;case"`":return ra(e,0,i);case"'":return ra(e,r);case"<":c=o[ra(l,1,-1)];break;default:var h=+l;if(0===h)return a;if(h>s){var u=na(h/10);return 0===u?a:u<=s?void 0===n[u-1]?oa(l,1):n[u-1]+oa(l,1):a}c=n[h-1]}return void 0===c?"":c}))},ha=Tt("replace"),ua=Math.max,da=Math.min,fa=E([].concat),pa=E([].push),ga=E("".indexOf),va=E("".slice),ba="$0"==="a".replace(/./,"$0"),ma=!!/./[ha]&&""===/./[ha]("a","$0");rn("replace",(function(t,e,i){var n=ma?"$":"$0";return[function(t,i){var n=L(this),o=null==t?void 0:st(t,ha);return o?x(o,t,n,i):x(e,li(n),t,i)},function(t,o){var a=Lt(this),r=li(t);if("string"==typeof o&&-1===ga(o,n)&&-1===ga(o,"$<")){var s=i(e,a,r,o);if(s.done)return s.value}var l=M(o);l||(o=li(o));var c=a.global;if(c){var h=a.unicode;a.lastIndex=0}for(var u=[];;){var d=En(a,r);if(null===d)break;if(pa(u,d),!c)break;""===li(d[0])&&(a.lastIndex=Tn(r,Te(a.lastIndex),h))}for(var f,p="",g=0,v=0;v<u.length;v++){for(var b=li((d=u[v])[0]),m=ua(da(Se(d.index),r.length),0),y=[],w=1;w<d.length;w++)pa(y,void 0===(f=d[w])?f:String(f));var S=d.groups;if(l){var x=fa([b],y,m,r);void 0!==S&&pa(x,S);var k=li(nn(o,void 0,x))}else k=ca(b,r,m,y,S,o);m>=g&&(p+=va(r,g,m)+k,g=m+b.length)}return p+va(r,g)}]}),!!m((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!ba||ma);var ya=uo.filter,wa=to("filter");Ke({target:"Array",proto:!0,forced:!wa},{filter:function(t){return ya(this,t,arguments.length>1?arguments[1]:void 0)}});var Sa=Object.is||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e};rn("search",(function(t,e,i){return[function(e){var i=L(this),n=null==e?void 0:st(e,t);return n?x(n,e,i):new RegExp(e)[t](li(i))},function(t){var n=Lt(this),o=li(t),a=i(e,n,o);if(a.done)return a.value;var r=n.lastIndex;Sa(r,0)||(n.lastIndex=0);var s=En(n,o);return Sa(n.lastIndex,r)||(n.lastIndex=r),null===s?-1:s.index}]}));var xa=gi.trim,ka=b.parseInt,Oa=b.Symbol,Ca=Oa&&Oa.iterator,Ta=/^[+-]?0x/i,Pa=E(Ta.exec),Ia=8!==ka(ci+"08")||22!==ka(ci+"0x16")||Ca&&!m((function(){ka(Object(Ca))}))?function(t,e){var i=xa(li(t));return ka(i,e>>>0||(Pa(Ta,i)?16:10))}:ka;Ke({global:!0,forced:parseInt!=Ia},{parseInt:Ia});var Aa=uo.map,$a=to("map");Ke({target:"Array",proto:!0,forced:!$a},{map:function(t){return Aa(this,t,arguments.length>1?arguments[1]:void 0)}});var Ra=uo.findIndex,Ea="findIndex",ja=!0;Ea in[]&&Array(1).findIndex((function(){ja=!1})),Ke({target:"Array",proto:!0,forced:ja},{findIndex:function(t){return Ra(this,t,arguments.length>1?arguments[1]:void 0)}}),Gn(Ea);var Fa=b.String,_a=b.TypeError,Na=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,i={};try{(t=E(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(i,[]),e=i instanceof Array}catch(t){}return function(i,n){return Lt(i),function(t){if("object"==typeof t||M(t))return t;throw _a("Can't set "+Fa(t)+" as a prototype")}(n),e?t(i,n):i.__proto__=n,i}}():void 0),Da=function(t,e,i){var n,o;return Na&&M(n=e.constructor)&&n!==i&&U(o=n.prototype)&&o!==i.prototype&&Na(t,o),t},Va=Tt("species"),Ba=Gt.f,La=_e.f,Ha=fe.enforce,Ma=Tt("match"),Ua=b.RegExp,za=Ua.prototype,qa=b.SyntaxError,Wa=E(ki),Ga=E(za.exec),Ka=E("".charAt),Ya=E("".replace),Ja=E("".indexOf),Xa=E("".slice),Qa=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,Za=/a/g,tr=/a/g,er=new Ua(Za)!==Za,ir=Pi.MISSED_STICKY,nr=Pi.UNSUPPORTED_Y,or=y&&(!er||ir||Vi||Li||m((function(){return tr[Ma]=!1,Ua(Za)!=Za||Ua(tr)==tr||"/a/i"!=Ua(Za,"i")})));if(We("RegExp",or)){for(var ar=function(t,e){var i,n,o,a,r,s,l=W(za,this),c=ln(t),h=void 0===e,u=[],d=t;if(!l&&c&&h&&t.constructor===ar)return t;if((c||W(za,t))&&(t=t.source,h&&(e="flags"in d?d.flags:Wa(d))),t=void 0===t?"":li(t),e=void 0===e?"":li(e),d=t,Vi&&"dotAll"in Za&&(n=!!e&&Ja(e,"s")>-1)&&(e=Ya(e,/s/g,"")),i=e,ir&&"sticky"in Za&&(o=!!e&&Ja(e,"y")>-1)&&nr&&(e=Ya(e,/y/g,"")),Li&&(a=function(t){for(var e,i=t.length,n=0,o="",a=[],r={},s=!1,l=!1,c=0,h="";n<=i;n++){if("\\"===(e=Ka(t,n)))e+=Ka(t,++n);else if("]"===e)s=!1;else if(!s)switch(!0){case"["===e:s=!0;break;case"("===e:Ga(Qa,Xa(t,n+1))&&(n+=2,l=!0),o+=e,c++;continue;case">"===e&&l:if(""===h||bt(r,h))throw new qa("Invalid capture group name");r[h]=!0,a[a.length]=[h,c],l=!1,h="";continue}l?h+=e:o+=e}return[o,a]}(t),t=a[0],u=a[1]),r=Da(Ua(t,e),l?this:za,ar),(n||o||u.length)&&(s=Ha(r),n&&(s.dotAll=!0,s.raw=ar(function(t){for(var e,i=t.length,n=0,o="",a=!1;n<=i;n++)"\\"!==(e=Ka(t,n))?a||"."!==e?("["===e?a=!0:"]"===e&&(a=!1),o+=e):o+="[\\s\\S]":o+=e+Ka(t,++n);return o}(t),i)),o&&(s.sticky=!0),u.length&&(s.groups=u)),t!==d)try{Kt(r,"source",""===d?"(?:)":d)}catch(t){}return r},rr=function(t){t in ar||Ba(ar,t,{configurable:!0,get:function(){return Ua[t]},set:function(e){Ua[t]=e}})},sr=La(Ua),lr=0;sr.length>lr;)rr(sr[lr++]);za.constructor=ar,ar.prototype=za,me(b,"RegExp",ar)}!function(t){var e=q(t),i=Gt.f;y&&e&&!e[Va]&&i(e,Va,{configurable:!0,get:function(){return this}})}("RegExp");var cr=be.PROPER,hr="toString",ur=RegExp.prototype,dr=ur.toString,fr=E(ki),pr=m((function(){return"/a/b"!=dr.call({source:"a",flags:"b"})})),gr=cr&&dr.name!=hr;(pr||gr)&&me(RegExp.prototype,hr,(function(){var t=Lt(this),e=li(t.source),i=t.flags;return"/"+e+"/"+li(void 0===i&&W(ur,t)&&!("flags"in ur)?fr(t):i)}),{unsafe:!0});var vr=E([].slice),br=to("slice"),mr=Tt("species"),yr=b.Array,wr=Math.max;Ke({target:"Array",proto:!0,forced:!br},{slice:function(t,e){var i,n,o,a=H(this),r=Pe(a),s=Oe(t,r),l=Oe(void 0===e?r:e,r);if(Yn(a)&&(i=a.constructor,(bn(i)&&(i===yr||Yn(i.prototype))||U(i)&&null===(i=i[mr]))&&(i=void 0),i===yr||void 0===i))return vr(a,s,l);for(n=new(void 0===i?yr:i)(wr(l-s,0)),o=0;s<l;s++,o++)s in a&&Pn(n,o,a[s]);return n.length=o,n}});var Sr,xr,kr,Or={},Cr=!m((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),Tr=ne("IE_PROTO"),Pr=b.Object,Ir=Pr.prototype,Ar=Cr?Pr.getPrototypeOf:function(t){var e=gt(t);if(bt(e,Tr))return e[Tr];var i=e.constructor;return M(i)&&e instanceof i?i.prototype:e instanceof Pr?Ir:null},$r=Tt("iterator"),Rr=!1;[].keys&&("next"in(kr=[].keys())?(xr=Ar(Ar(kr)))!==Object.prototype&&(Sr=xr):Rr=!0);var Er=null==Sr||m((function(){var t={};return Sr[$r].call(t)!==t}));Er&&(Sr={}),M(Sr[$r])||me(Sr,$r,(function(){return this}));var jr={IteratorPrototype:Sr,BUGGY_SAFARI_ITERATORS:Rr},Fr=Gt.f,_r=Tt("toStringTag"),Nr=function(t,e,i){t&&!i&&(t=t.prototype),t&&!bt(t,_r)&&Fr(t,_r,{configurable:!0,value:e})},Dr=jr.IteratorPrototype,Vr=function(){return this},Br=be.PROPER,Lr=be.CONFIGURABLE,Hr=jr.IteratorPrototype,Mr=jr.BUGGY_SAFARI_ITERATORS,Ur=Tt("iterator"),zr="keys",qr="values",Wr="entries",Gr=function(){return this},Kr=Gt.f,Yr="Array Iterator",Jr=fe.set,Xr=fe.getterFor(Yr),Qr=function(t,e,i,n,o,a,r){!function(t,e,i,n){var o=e+" Iterator";t.prototype=Ni(Dr,{next:P(+!n,i)}),Nr(t,o,!1),Or[o]=Vr}(i,e,n);var s,l,c,h=function(t){if(t===o&&g)return g;if(!Mr&&t in f)return f[t];switch(t){case zr:case qr:case Wr:return function(){return new i(this,t)}}return function(){return new i(this)}},u=e+" Iterator",d=!1,f=t.prototype,p=f[Ur]||f["@@iterator"]||o&&f[o],g=!Mr&&p||h(o),v="Array"==e&&f.entries||p;if(v&&(s=Ar(v.call(new t)))!==Object.prototype&&s.next&&(Ar(s)!==Hr&&(Na?Na(s,Hr):M(s[Ur])||me(s,Ur,Gr)),Nr(s,u,!0)),Br&&o==qr&&p&&p.name!==qr&&(Lr?Kt(f,"name",qr):(d=!0,g=function(){return x(p,this)})),o)if(l={values:h(qr),keys:a?g:h(zr),entries:h(Wr)},r)for(c in l)(Mr||d||!(c in f))&&me(f,c,l[c]);else Ke({target:e,proto:!0,forced:Mr||d},l);return f[Ur]!==g&&me(f,Ur,g,{name:o}),Or[e]=g,l}(Array,"Array",(function(t,e){Jr(this,{type:Yr,target:H(t),index:0,kind:e})}),(function(){var t=Xr(this),e=t.target,i=t.kind,n=t.index++;return!e||n>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==i?{value:n,done:!1}:"values"==i?{value:e[n],done:!1}:{value:[n,e[n]],done:!1}}),"values"),Zr=Or.Arguments=Or.Array;if(Gn("keys"),Gn("values"),Gn("entries"),y&&"values"!==Zr.name)try{Kr(Zr,"name",{value:"values"})}catch(t){}var ts=Tt("iterator"),es=Tt("toStringTag"),is=Qr.values,ns=function(t,e){if(t){if(t[ts]!==is)try{Kt(t,ts,is)}catch(e){t[ts]=is}if(t[es]||Kt(t,es,e),xo[e])for(var i in Qr)if(t[i]!==Qr[i])try{Kt(t,i,Qr[i])}catch(e){t[i]=Qr[i]}}};for(var os in xo)ns(b[os]&&b[os].prototype,os);ns(Co,"DOMTokenList");var as=to("splice"),rs=b.TypeError,ss=Math.max,ls=Math.min,cs=9007199254740991,hs="Maximum allowed length exceeded";Ke({target:"Array",proto:!0,forced:!as},{splice:function(t,e){var i,n,o,a,r,s,l=gt(this),c=Pe(l),h=Oe(t,c),u=arguments.length;if(0===u?i=n=0:1===u?(i=0,n=c-h):(i=u-2,n=ls(ss(Se(e),0),c-h)),c+i-n>cs)throw rs(hs);for(o=Qn(l,n),a=0;a<n;a++)(r=h+a)in l&&Pn(o,a,l[r]);if(o.length=n,i<n){for(a=h;a<c-n;a++)s=a+i,(r=a+n)in l?l[s]=l[r]:delete l[s];for(a=c;a>c-n+i;a--)delete l[a-1]}else if(i>n)for(a=c-n;a>h;a--)s=a+i-1,(r=a+n-1)in l?l[s]=l[r]:delete l[s];for(a=0;a<i;a++)l[a+h]=arguments[a+2];return l.length=c-n+i,o}});var us=E(1..valueOf),ds=_e.f,fs=Nt.f,ps=Gt.f,gs=gi.trim,vs="Number",bs=b.Number,ms=bs.prototype,ys=b.TypeError,ws=E("".slice),Ss=E("".charCodeAt),xs=function(t){var e=At(t,"number");return"bigint"==typeof e?e:ks(e)},ks=function(t){var e,i,n,o,a,r,s,l,c=At(t,"number");if(it(c))throw ys("Cannot convert a Symbol value to a number");if("string"==typeof c&&c.length>2)if(c=gs(c),43===(e=Ss(c,0))||45===e){if(88===(i=Ss(c,2))||120===i)return NaN}else if(48===e){switch(Ss(c,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+c}for(r=(a=ws(c,2)).length,s=0;s<r;s++)if((l=Ss(a,s))<48||l>o)return NaN;return parseInt(a,n)}return+c};if(We(vs,!bs(" 0o1")||!bs("0b1")||bs("+0x1"))){for(var Os,Cs=function(t){var e=arguments.length<1?0:bs(xs(t)),i=this;return W(ms,i)&&m((function(){us(i)}))?Da(Object(e),i,Cs):e},Ts=y?ds(bs):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),Ps=0;Ts.length>Ps;Ps++)bt(bs,Os=Ts[Ps])&&!bt(Cs,Os)&&ps(Cs,Os,fs(bs,Os));Cs.prototype=ms,ms.constructor=Cs,me(b,vs,Cs)}var Is=E([].reverse),As=[1,2];Ke({target:"Array",proto:!0,forced:String(As)===String(As.reverse())},{reverse:function(){return Yn(this)&&(this.length=this.length),Is(this)}});var $s=m((function(){Ye(1)}));Ke({target:"Object",stat:!0,forced:$s},{keys:function(t){return Ye(gt(t))}}),rn("match",(function(t,e,i){return[function(e){var i=L(this),n=null==e?void 0:st(e,t);return n?x(n,e,i):new RegExp(e)[t](li(i))},function(t){var n=Lt(this),o=li(t),a=i(e,n,o);if(a.done)return a.value;if(!n.global)return En(n,o);var r=n.unicode;n.lastIndex=0;for(var s,l=[],c=0;null!==(s=En(n,o));){var h=li(s[0]);l[c]=h,""===h&&(n.lastIndex=Tn(o,Te(n.lastIndex),r)),c++}return 0===c?null:l}]}));var Rs,Es=Nt.f,js=E("".startsWith),Fs=E("".slice),_s=Math.min,Ns=wo("startsWith"),Ds=!(Ns||(Rs=Es(String.prototype,"startsWith"),!Rs||Rs.writable));Ke({target:"String",proto:!0,forced:!Ds&&!Ns},{startsWith:function(t){var e=li(L(this));mo(t);var i=Te(_s(arguments.length>1?arguments[1]:void 0,e.length)),n=li(t);return js?js(e,n,i):Fs(e,i,i+n.length)===n}});var Vs=Nt.f,Bs=E("".endsWith),Ls=E("".slice),Hs=Math.min,Ms=wo("endsWith"),Us=!Ms&&!!function(){var t=Vs(String.prototype,"endsWith");return t&&!t.writable}();Ke({target:"String",proto:!0,forced:!Us&&!Ms},{endsWith:function(t){var e=li(L(this));mo(t);var i=arguments.length>1?arguments[1]:void 0,n=e.length,o=void 0===i?n:Hs(Te(i),n),a=li(t);return Bs?Bs(e,a,o):Ls(e,o-a.length,o)===a}});var zs={getBootstrapVersion:function(){var t=5;try{var e=i.default.fn.dropdown.Constructor.VERSION;void 0!==e&&(t=parseInt(e,10))}catch(t){}try{var n=bootstrap.Tooltip.VERSION;void 0!==n&&(t=parseInt(n,10))}catch(t){}return t},getIconsPrefix:function(t){return{bootstrap3:"glyphicon",bootstrap4:"fa",bootstrap5:"bi","bootstrap-table":"icon",bulma:"fa",foundation:"fa",materialize:"material-icons",semantic:"fa"}[t]||"fa"},getIcons:function(t){return{glyphicon:{paginationSwitchDown:"glyphicon-collapse-down icon-chevron-down",paginationSwitchUp:"glyphicon-collapse-up icon-chevron-up",refresh:"glyphicon-refresh icon-refresh",toggleOff:"glyphicon-list-alt icon-list-alt",toggleOn:"glyphicon-list-alt icon-list-alt",columns:"glyphicon-th icon-th",detailOpen:"glyphicon-plus icon-plus",detailClose:"glyphicon-minus icon-minus",fullscreen:"glyphicon-fullscreen",search:"glyphicon-search",clearSearch:"glyphicon-trash"},fa:{paginationSwitchDown:"fa-caret-square-down",paginationSwitchUp:"fa-caret-square-up",refresh:"fa-sync",toggleOff:"fa-toggle-off",toggleOn:"fa-toggle-on",columns:"fa-th-list",detailOpen:"fa-plus",detailClose:"fa-minus",fullscreen:"fa-arrows-alt",search:"fa-search",clearSearch:"fa-trash"},bi:{paginationSwitchDown:"bi-caret-down-square",paginationSwitchUp:"bi-caret-up-square",refresh:"bi-arrow-clockwise",toggleOff:"bi-toggle-off",toggleOn:"bi-toggle-on",columns:"bi-list-ul",detailOpen:"bi-plus",detailClose:"bi-dash",fullscreen:"bi-arrows-move",search:"bi-search",clearSearch:"bi-trash"},icon:{paginationSwitchDown:"icon-arrow-up-circle",paginationSwitchUp:"icon-arrow-down-circle",refresh:"icon-refresh-cw",toggleOff:"icon-toggle-right",toggleOn:"icon-toggle-right",columns:"icon-list",detailOpen:"icon-plus",detailClose:"icon-minus",fullscreen:"icon-maximize",search:"icon-search",clearSearch:"icon-trash-2"},"material-icons":{paginationSwitchDown:"grid_on",paginationSwitchUp:"grid_off",refresh:"refresh",toggleOff:"tablet",toggleOn:"tablet_android",columns:"view_list",detailOpen:"add",detailClose:"remove",fullscreen:"fullscreen",sort:"sort",search:"search",clearSearch:"delete"}}[t]},getSearchInput:function(t){return"string"==typeof t.options.searchSelector?i.default(t.options.searchSelector):t.$toolbar.find(".search input")},sprintf:function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];var o=!0,a=0,r=t.replace(/%s/g,(function(){var t=i[a++];return void 0===t?(o=!1,""):t}));return o?r:""},isObject:function(t){return t instanceof Object&&!Array.isArray(t)},isEmptyObject:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return 0===Object.entries(t).length&&t.constructor===Object},isNumeric:function(t){return!isNaN(parseFloat(t))&&isFinite(t)},getFieldTitle:function(t,e){var i,n=u(t);try{for(n.s();!(i=n.n()).done;){var o=i.value;if(o.field===e)return o.title}}catch(t){n.e(t)}finally{n.f()}return""},setFieldIndex:function(t){var e,i=0,n=[],o=u(t[0]);try{for(o.s();!(e=o.n()).done;){i+=e.value.colspan||1}}catch(t){o.e(t)}finally{o.f()}for(var a=0;a<t.length;a++){n[a]=[];for(var r=0;r<i;r++)n[a][r]=!1}for(var s=0;s<t.length;s++){var l,c=u(t[s]);try{for(c.s();!(l=c.n()).done;){var h=l.value,d=h.rowspan||1,f=h.colspan||1,p=n[s].indexOf(!1);h.colspanIndex=p,1===f?(h.fieldIndex=p,void 0===h.field&&(h.field=p)):h.colspanGroup=h.colspan;for(var g=0;g<d;g++)for(var v=0;v<f;v++)n[s+g][p+v]=!0}}catch(t){c.e(t)}finally{c.f()}}},normalizeAccent:function(t){return"string"!=typeof t?t:t.normalize("NFD").replace(/[\u0300-\u036f]/g,"")},updateFieldGroup:function(t){var e,i,n=(e=[]).concat.apply(e,l(t)),o=u(t);try{for(o.s();!(i=o.n()).done;){var a,r=u(i.value);try{for(r.s();!(a=r.n()).done;){var s=a.value;if(s.colspanGroup>1){for(var c=0,h=function(t){n.find((function(e){return e.fieldIndex===t})).visible&&c++},d=s.colspanIndex;d<s.colspanIndex+s.colspanGroup;d++)h(d);s.colspan=c,s.visible=c>0}}}catch(t){r.e(t)}finally{r.f()}}}catch(t){o.e(t)}finally{o.f()}},getScrollBarWidth:function(){if(void 0===this.cachedWidth){var t=i.default("<div/>").addClass("fixed-table-scroll-inner"),e=i.default("<div/>").addClass("fixed-table-scroll-outer");e.append(t),i.default("body").append(e);var n=t[0].offsetWidth;e.css("overflow","scroll");var o=t[0].offsetWidth;n===o&&(o=e[0].clientWidth),e.remove(),this.cachedWidth=n-o}return this.cachedWidth},calculateObjectValue:function(t,e,i,o){var a=e;if("string"==typeof e){var r=e.split(".");if(r.length>1){a=window;var s,c=u(r);try{for(c.s();!(s=c.n()).done;){a=a[s.value]}}catch(t){c.e(t)}finally{c.f()}}else a=window[e]}return null!==a&&"object"===n(a)?a:"function"==typeof a?a.apply(t,i||[]):!a&&"string"==typeof e&&i&&this.sprintf.apply(this,[e].concat(l(i)))?this.sprintf.apply(this,[e].concat(l(i))):o},compareObjects:function(t,e,i){var n=Object.keys(t),o=Object.keys(e);if(i&&n.length!==o.length)return!1;for(var a=0,r=n;a<r.length;a++){var s=r[a];if(o.includes(s)&&t[s]!==e[s])return!1}return!0},regexCompare:function(t,e){try{var i=e.match(/^\/(.*?)\/([gim]*)$/);if(-1!==t.toString().search(i?new RegExp(i[1],i[2]):new RegExp(e,"gim")))return!0}catch(t){return!1}},escapeHTML:function(t){return t?t.toString().replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;"):t},unescapeHTML:function(t){return"string"==typeof t&&t?t.toString().replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#39;/g,"'"):t},removeHTML:function(t){return t?t.toString().replace(/(<([^>]+)>)/gi,"").replace(/&[#A-Za-z0-9]+;/gi,"").trim():t},getRealDataAttr:function(t){for(var e=0,i=Object.entries(t);e<i.length;e++){var n=s(i[e],2),o=n[0],a=n[1],r=o.split(/(?=[A-Z])/).join("-").toLowerCase();r!==o&&(t[r]=a,delete t[o])}return t},getItemField:function(t,e,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0,o=t;if(void 0!==n&&(i=n),"string"!=typeof e||t.hasOwnProperty(e))return i?this.escapeHTML(t[e]):t[e];var a,r=e.split("."),s=u(r);try{for(s.s();!(a=s.n()).done;){var l=a.value;o=o&&o[l]}}catch(t){s.e(t)}finally{s.f()}return i?this.escapeHTML(o):o},isIEBrowser:function(){return navigator.userAgent.includes("MSIE ")||/Trident.*rv:11\./.test(navigator.userAgent)},findIndex:function(t,e){var i,n=u(t);try{for(n.s();!(i=n.n()).done;){var o=i.value;if(JSON.stringify(o)===JSON.stringify(e))return t.indexOf(o)}}catch(t){n.e(t)}finally{n.f()}return-1},trToData:function(t,e){var n=this,o=[],a=[];return e.each((function(e,r){var s=i.default(r),l={};l._id=s.attr("id"),l._class=s.attr("class"),l._data=n.getRealDataAttr(s.data()),l._style=s.attr("style"),s.find(">td,>th").each((function(o,r){for(var s=i.default(r),c=+s.attr("colspan")||1,h=+s.attr("rowspan")||1,u=o;a[e]&&a[e][u];u++);for(var d=u;d<u+c;d++)for(var f=e;f<e+h;f++)a[f]||(a[f]=[]),a[f][d]=!0;var p=t[u].field;l[p]=s.html().trim(),l["_".concat(p,"_id")]=s.attr("id"),l["_".concat(p,"_class")]=s.attr("class"),l["_".concat(p,"_rowspan")]=s.attr("rowspan"),l["_".concat(p,"_colspan")]=s.attr("colspan"),l["_".concat(p,"_title")]=s.attr("title"),l["_".concat(p,"_data")]=n.getRealDataAttr(s.data()),l["_".concat(p,"_style")]=s.attr("style")})),o.push(l)})),o},sort:function(t,e,i,n,o,a){return null==t&&(t=""),null==e&&(e=""),n&&t===e&&(t=o,e=a),this.isNumeric(t)&&this.isNumeric(e)?(t=parseFloat(t))<(e=parseFloat(e))?-1*i:t>e?i:0:t===e?0:("string"!=typeof t&&(t=t.toString()),-1===t.localeCompare(e)?-1*i:i)},getEventName:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e=e||"".concat(+new Date).concat(~~(1e6*Math.random())),"".concat(t,"-").concat(e)},hasDetailViewIcon:function(t){return t.detailView&&t.detailViewIcon&&!t.cardView},getDetailViewIndexOffset:function(t){return this.hasDetailViewIcon(t)&&"right"!==t.detailViewAlign?1:0},checkAutoMergeCells:function(t){var e,i=u(t);try{for(i.s();!(e=i.n()).done;)for(var n=e.value,o=0,a=Object.keys(n);o<a.length;o++){var r=a[o];if(r.startsWith("_")&&(r.endsWith("_rowspan")||r.endsWith("_colspan")))return!0}}catch(t){i.e(t)}finally{i.f()}return!1},deepCopy:function(t){return void 0===t?t:i.default.extend(!0,Array.isArray(t)?[]:{},t)},debounce:function(t,e,i){var n;return function(){var o=this,a=arguments,r=function(){n=null,i||t.apply(o,a)},s=i&&!n;clearTimeout(n),n=setTimeout(r,e),s&&t.apply(o,a)}}},qs=zs.getBootstrapVersion(),Ws={3:{classes:{buttonsPrefix:"btn",buttons:"default",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"pull",inputGroup:"input-group",inputPrefix:"input-",input:"form-control",select:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<ul class="dropdown-menu" role="menu">',"</ul>"],toolbarDropdownItem:'<li class="dropdown-item-marker" role="menuitem"><label>%s</label></li>',toolbarDropdownSeparator:'<li class="divider"></li>',pageDropdown:['<ul class="dropdown-menu" role="menu">',"</ul>"],pageDropdownItem:'<li role="menuitem" class="%s"><a href="#">%s</a></li>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<span class="input-group-btn">%s</span></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}},4:{classes:{buttonsPrefix:"btn",buttons:"secondary",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"float",inputGroup:"btn-group",inputPrefix:"form-control-",input:"form-control",select:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<div class="dropdown-menu dropdown-menu-right">',"</div>"],toolbarDropdownItem:'<label class="dropdown-item dropdown-item-marker">%s</label>',pageDropdown:['<div class="dropdown-menu">',"</div>"],pageDropdownItem:'<a class="dropdown-item %s" href="#">%s</a>',toolbarDropdownSeparator:'<div class="dropdown-divider"></div>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<div class="input-group-append">%s</div></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}},5:{classes:{buttonsPrefix:"btn",buttons:"secondary",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"float",inputGroup:"btn-group",inputPrefix:"form-control-",input:"form-control",select:"form-select",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{dataToggle:"data-bs-toggle",toolbarDropdown:['<div class="dropdown-menu dropdown-menu-right">',"</div>"],toolbarDropdownItem:'<label class="dropdown-item dropdown-item-marker">%s</label>',pageDropdown:['<div class="dropdown-menu">',"</div>"],pageDropdownItem:'<a class="dropdown-item %s" href="#">%s</a>',toolbarDropdownSeparator:'<div class="dropdown-divider"></div>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s%s</div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}}}[qs],Gs={height:void 0,classes:"table table-bordered table-hover",buttons:{},theadClasses:"",headerStyle:function(t){return{}},rowStyle:function(t,e){return{}},rowAttributes:function(t,e){return{}},undefinedText:"-",locale:void 0,virtualScroll:!1,virtualScrollItemHeight:void 0,sortable:!0,sortClass:void 0,silentSort:!0,sortName:void 0,sortOrder:void 0,sortReset:!1,sortStable:!1,rememberOrder:!1,serverSort:!0,customSort:void 0,columns:[[]],data:[],url:void 0,method:"get",cache:!0,contentType:"application/json",dataType:"json",ajax:void 0,ajaxOptions:{},queryParams:function(t){return t},queryParamsType:"limit",responseHandler:function(t){return t},totalField:"total",totalNotFilteredField:"totalNotFiltered",dataField:"rows",footerField:"footer",pagination:!1,paginationParts:["pageInfo","pageSize","pageList"],showExtendedPagination:!1,paginationLoop:!0,sidePagination:"client",totalRows:0,totalNotFiltered:0,pageNumber:1,pageSize:10,pageList:[10,25,50,100],paginationHAlign:"right",paginationVAlign:"bottom",paginationDetailHAlign:"left",paginationPreText:"&lsaquo;",paginationNextText:"&rsaquo;",paginationSuccessivelySize:5,paginationPagesBySide:1,paginationUseIntermediate:!1,search:!1,searchHighlight:!1,searchOnEnterKey:!1,strictSearch:!1,regexSearch:!1,searchSelector:!1,visibleSearch:!1,showButtonIcons:!0,showButtonText:!1,showSearchButton:!1,showSearchClearButton:!1,trimOnSearch:!0,searchAlign:"right",searchTimeOut:500,searchText:"",customSearch:void 0,showHeader:!0,showFooter:!1,footerStyle:function(t){return{}},searchAccentNeutralise:!1,showColumns:!1,showColumnsToggleAll:!1,showColumnsSearch:!1,minimumCountColumns:1,showPaginationSwitch:!1,showRefresh:!1,showToggle:!1,showFullscreen:!1,smartDisplay:!0,escape:!1,filterOptions:{filterAlgorithm:"and"},idField:void 0,selectItemName:"btSelectItem",clickToSelect:!1,ignoreClickToSelectOn:function(t){var e=t.tagName;return["A","BUTTON"].includes(e)},singleSelect:!1,checkboxHeader:!0,maintainMetaData:!1,multipleSelectRow:!1,uniqueId:void 0,cardView:!1,detailView:!1,detailViewIcon:!0,detailViewByClick:!1,detailViewAlign:"left",detailFormatter:function(t,e){return""},detailFilter:function(t,e){return!0},toolbar:void 0,toolbarAlign:"left",buttonsToolbar:void 0,buttonsAlign:"right",buttonsOrder:["paginationSwitch","refresh","toggle","fullscreen","columns"],buttonsPrefix:Ws.classes.buttonsPrefix,buttonsClass:Ws.classes.buttons,iconsPrefix:void 0,icons:{},iconSize:void 0,loadingFontSize:"auto",loadingTemplate:function(t){return'<span class="loading-wrap">\n      <span class="loading-text">'.concat(t,'</span>\n      <span class="animation-wrap"><span class="animation-dot"></span></span>\n      </span>\n    ')},onAll:function(t,e){return!1},onClickCell:function(t,e,i,n){return!1},onDblClickCell:function(t,e,i,n){return!1},onClickRow:function(t,e){return!1},onDblClickRow:function(t,e){return!1},onSort:function(t,e){return!1},onCheck:function(t){return!1},onUncheck:function(t){return!1},onCheckAll:function(t){return!1},onUncheckAll:function(t){return!1},onCheckSome:function(t){return!1},onUncheckSome:function(t){return!1},onLoadSuccess:function(t){return!1},onLoadError:function(t){return!1},onColumnSwitch:function(t,e){return!1},onColumnSwitchAll:function(t){return!1},onPageChange:function(t,e){return!1},onSearch:function(t){return!1},onToggle:function(t){return!1},onPreBody:function(t){return!1},onPostBody:function(){return!1},onPostHeader:function(){return!1},onPostFooter:function(){return!1},onExpandRow:function(t,e,i){return!1},onCollapseRow:function(t,e){return!1},onRefreshOptions:function(t){return!1},onRefresh:function(t){return!1},onResetView:function(){return!1},onScrollBody:function(){return!1},onTogglePagination:function(t){return!1},onVirtualScroll:function(t,e){return!1}},Ks={formatLoadingMessage:function(){return"Loading, please wait"},formatRecordsPerPage:function(t){return"".concat(t," rows per page")},formatShowingRows:function(t,e,i,n){return void 0!==n&&n>0&&n>i?"Showing ".concat(t," to ").concat(e," of ").concat(i," rows (filtered from ").concat(n," total rows)"):"Showing ".concat(t," to ").concat(e," of ").concat(i," rows")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatSearch:function(){return"Search"},formatClearSearch:function(){return"Clear Search"},formatNoMatches:function(){return"No matching records found"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Refresh"},formatToggle:function(){return"Toggle"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Columns"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"}},Ys={field:void 0,title:void 0,titleTooltip:void 0,class:void 0,width:void 0,widthUnit:"px",rowspan:void 0,colspan:void 0,align:void 0,halign:void 0,falign:void 0,valign:void 0,cellStyle:void 0,radio:!1,checkbox:!1,checkboxEnabled:!0,clickToSelect:!0,showSelectTitle:!1,sortable:!1,sortName:void 0,order:"asc",sorter:void 0,visible:!0,switchable:!0,cardVisible:!0,searchable:!0,formatter:void 0,footerFormatter:void 0,detailFormatter:void 0,searchFormatter:!0,searchHighlightFormatter:!1,escape:void 0,events:void 0};Object.assign(Gs,Ks);var Js={VERSION:"1.20.0",THEME:"bootstrap".concat(qs),CONSTANTS:Ws,DEFAULTS:Gs,COLUMN_DEFAULTS:Ys,METHODS:["getOptions","refreshOptions","getData","getSelections","load","append","prepend","remove","removeAll","insertRow","updateRow","getRowByUniqueId","updateByUniqueId","removeByUniqueId","updateCell","updateCellByUniqueId","showRow","hideRow","getHiddenRows","showColumn","hideColumn","getVisibleColumns","getHiddenColumns","showAllColumns","hideAllColumns","mergeCells","checkAll","uncheckAll","checkInvert","check","uncheck","checkBy","uncheckBy","refresh","destroy","resetView","showLoading","hideLoading","togglePagination","toggleFullscreen","toggleView","resetSearch","filterBy","scrollTo","getScrollPosition","selectPage","prevPage","nextPage","toggleDetailView","expandRow","collapseRow","expandRowByUniqueId","collapseRowByUniqueId","expandAllRows","collapseAllRows","updateColumnTitle","updateFormatText"],EVENTS:{"all.bs.table":"onAll","click-row.bs.table":"onClickRow","dbl-click-row.bs.table":"onDblClickRow","click-cell.bs.table":"onClickCell","dbl-click-cell.bs.table":"onDblClickCell","sort.bs.table":"onSort","check.bs.table":"onCheck","uncheck.bs.table":"onUncheck","check-all.bs.table":"onCheckAll","uncheck-all.bs.table":"onUncheckAll","check-some.bs.table":"onCheckSome","uncheck-some.bs.table":"onUncheckSome","load-success.bs.table":"onLoadSuccess","load-error.bs.table":"onLoadError","column-switch.bs.table":"onColumnSwitch","column-switch-all.bs.table":"onColumnSwitchAll","page-change.bs.table":"onPageChange","search.bs.table":"onSearch","toggle.bs.table":"onToggle","pre-body.bs.table":"onPreBody","post-body.bs.table":"onPostBody","post-header.bs.table":"onPostHeader","post-footer.bs.table":"onPostFooter","expand-row.bs.table":"onExpandRow","collapse-row.bs.table":"onCollapseRow","refresh-options.bs.table":"onRefreshOptions","reset-view.bs.table":"onResetView","refresh.bs.table":"onRefresh","scroll-body.bs.table":"onScrollBody","toggle-pagination.bs.table":"onTogglePagination","virtual-scroll.bs.table":"onVirtualScroll"},LOCALES:{en:Ks,"en-US":Ks}},Xs=function(){function t(e){var i=this;o(this,t),this.rows=e.rows,this.scrollEl=e.scrollEl,this.contentEl=e.contentEl,this.callback=e.callback,this.itemHeight=e.itemHeight,this.cache={},this.scrollTop=this.scrollEl.scrollTop,this.initDOM(this.rows,e.fixedScroll),this.scrollEl.scrollTop=this.scrollTop,this.lastCluster=0;var n=function(){i.lastCluster!==(i.lastCluster=i.getNum())&&(i.initDOM(i.rows),i.callback(i.startIndex,i.endIndex))};this.scrollEl.addEventListener("scroll",n,!1),this.destroy=function(){i.contentEl.innerHtml="",i.scrollEl.removeEventListener("scroll",n,!1)}}return r(t,[{key:"initDOM",value:function(t,e){void 0===this.clusterHeight&&(this.cache.scrollTop=this.scrollEl.scrollTop,this.cache.data=this.contentEl.innerHTML=t[0]+t[0]+t[0],this.getRowsHeight(t));var i=this.initData(t,this.getNum(e)),n=i.rows.join(""),o=this.checkChanges("data",n),a=this.checkChanges("top",i.topOffset),r=this.checkChanges("bottom",i.bottomOffset),s=[];o&&a?(i.topOffset&&s.push(this.getExtra("top",i.topOffset)),s.push(n),i.bottomOffset&&s.push(this.getExtra("bottom",i.bottomOffset)),this.startIndex=i.start,this.endIndex=i.end,this.contentEl.innerHTML=s.join(""),e&&(this.contentEl.scrollTop=this.cache.scrollTop)):r&&(this.contentEl.lastChild.style.height="".concat(i.bottomOffset,"px"))}},{key:"getRowsHeight",value:function(){if(void 0===this.itemHeight){var t=this.contentEl.children,e=t[Math.floor(t.length/2)];this.itemHeight=e.offsetHeight}this.blockHeight=50*this.itemHeight,this.clusterRows=200,this.clusterHeight=4*this.blockHeight}},{key:"getNum",value:function(t){return this.scrollTop=t?this.cache.scrollTop:this.scrollEl.scrollTop,Math.floor(this.scrollTop/(this.clusterHeight-this.blockHeight))||0}},{key:"initData",value:function(t,e){if(t.length<50)return{topOffset:0,bottomOffset:0,rowsAbove:0,rows:t};var i=Math.max((this.clusterRows-50)*e,0),n=i+this.clusterRows,o=Math.max(i*this.itemHeight,0),a=Math.max((t.length-n)*this.itemHeight,0),r=[],s=i;o<1&&s++;for(var l=i;l<n;l++)t[l]&&r.push(t[l]);return{start:i,end:n,topOffset:o,bottomOffset:a,rowsAbove:s,rows:r}}},{key:"checkChanges",value:function(t,e){var i=e!==this.cache[t];return this.cache[t]=e,i}},{key:"getExtra",value:function(t,e){var i=document.createElement("tr");return i.className="virtual-scroll-".concat(t),e&&(i.style.height="".concat(e,"px")),i.outerHTML}}]),t}(),Qs=function(){function t(e,n){o(this,t),this.options=n,this.$el=i.default(e),this.$el_=this.$el.clone(),this.timeoutId_=0,this.timeoutFooter_=0}return r(t,[{key:"init",value:function(){this.initConstants(),this.initLocale(),this.initContainer(),this.initTable(),this.initHeader(),this.initData(),this.initHiddenRows(),this.initToolbar(),this.initPagination(),this.initBody(),this.initSearchText(),this.initServer()}},{key:"initConstants",value:function(){var t=this.options;this.constants=Js.CONSTANTS,this.constants.theme=i.default.fn.bootstrapTable.theme,this.constants.dataToggle=this.constants.html.dataToggle||"data-toggle";var e=zs.getIconsPrefix(i.default.fn.bootstrapTable.theme),o=zs.getIcons(e);t.iconsPrefix=t.iconsPrefix||i.default.fn.bootstrapTable.defaults.iconsPrefix||e,t.icons=Object.assign(o,i.default.fn.bootstrapTable.defaults.icons,t.icons);var a=t.buttonsPrefix?"".concat(t.buttonsPrefix,"-"):"";this.constants.buttonsClass=[t.buttonsPrefix,a+t.buttonsClass,zs.sprintf("".concat(a,"%s"),t.iconSize)].join(" ").trim(),this.buttons=zs.calculateObjectValue(this,t.buttons,[],{}),"object"!==n(this.buttons)&&(this.buttons={}),"string"==typeof t.icons&&(t.icons=zs.calculateObjectValue(null,t.icons))}},{key:"initLocale",value:function(){if(this.options.locale){var e=i.default.fn.bootstrapTable.locales,n=this.options.locale.split(/-|_/);n[0]=n[0].toLowerCase(),n[1]&&(n[1]=n[1].toUpperCase());var o={};e[this.options.locale]?o=e[this.options.locale]:e[n.join("-")]?o=e[n.join("-")]:e[n[0]]&&(o=e[n[0]]);for(var a=0,r=Object.entries(o);a<r.length;a++){var l=s(r[a],2),c=l[0],h=l[1];this.options[c]===t.DEFAULTS[c]&&(this.options[c]=h)}}}},{key:"initContainer",value:function(){var t=["top","both"].includes(this.options.paginationVAlign)?'<div class="fixed-table-pagination clearfix"></div>':"",e=["bottom","both"].includes(this.options.paginationVAlign)?'<div class="fixed-table-pagination"></div>':"",n=zs.calculateObjectValue(this.options,this.options.loadingTemplate,[this.options.formatLoadingMessage()]);this.$container=i.default('\n      <div class="bootstrap-table '.concat(this.constants.theme,'">\n      <div class="fixed-table-toolbar"></div>\n      ').concat(t,'\n      <div class="fixed-table-container">\n      <div class="fixed-table-header"><table></table></div>\n      <div class="fixed-table-body">\n      <div class="fixed-table-loading">\n      ').concat(n,'\n      </div>\n      </div>\n      <div class="fixed-table-footer"></div>\n      </div>\n      ').concat(e,"\n      </div>\n    ")),this.$container.insertAfter(this.$el),this.$tableContainer=this.$container.find(".fixed-table-container"),this.$tableHeader=this.$container.find(".fixed-table-header"),this.$tableBody=this.$container.find(".fixed-table-body"),this.$tableLoading=this.$container.find(".fixed-table-loading"),this.$tableFooter=this.$el.find("tfoot"),this.options.buttonsToolbar?this.$toolbar=i.default("body").find(this.options.buttonsToolbar):this.$toolbar=this.$container.find(".fixed-table-toolbar"),this.$pagination=this.$container.find(".fixed-table-pagination"),this.$tableBody.append(this.$el),this.$container.after('<div class="clearfix"></div>'),this.$el.addClass(this.options.classes),this.$tableLoading.addClass(this.options.classes),this.options.height&&(this.$tableContainer.addClass("fixed-height"),this.options.showFooter&&this.$tableContainer.addClass("has-footer"),this.options.classes.split(" ").includes("table-bordered")&&(this.$tableBody.append('<div class="fixed-table-border"></div>'),this.$tableBorder=this.$tableBody.find(".fixed-table-border"),this.$tableLoading.addClass("fixed-table-border")),this.$tableFooter=this.$container.find(".fixed-table-footer"))}},{key:"initTable",value:function(){var e=this,n=[];if(this.$header=this.$el.find(">thead"),this.$header.length?this.options.theadClasses&&this.$header.addClass(this.options.theadClasses):this.$header=i.default('<thead class="'.concat(this.options.theadClasses,'"></thead>')).appendTo(this.$el),this._headerTrClasses=[],this._headerTrStyles=[],this.$header.find("tr").each((function(t,o){var a=i.default(o),r=[];a.find("th").each((function(t,e){var n=i.default(e);void 0!==n.data("field")&&n.data("field","".concat(n.data("field"))),r.push(i.default.extend({},{title:n.html(),class:n.attr("class"),titleTooltip:n.attr("title"),rowspan:n.attr("rowspan")?+n.attr("rowspan"):void 0,colspan:n.attr("colspan")?+n.attr("colspan"):void 0},n.data()))})),n.push(r),a.attr("class")&&e._headerTrClasses.push(a.attr("class")),a.attr("style")&&e._headerTrStyles.push(a.attr("style"))})),Array.isArray(this.options.columns[0])||(this.options.columns=[this.options.columns]),this.options.columns=i.default.extend(!0,[],n,this.options.columns),this.columns=[],this.fieldsColumnsIndex=[],zs.setFieldIndex(this.options.columns),this.options.columns.forEach((function(n,o){n.forEach((function(n,a){var r=i.default.extend({},t.COLUMN_DEFAULTS,n);void 0!==r.fieldIndex&&(e.columns[r.fieldIndex]=r,e.fieldsColumnsIndex[r.field]=r.fieldIndex),e.options.columns[o][a]=r}))})),!this.options.data.length){var o=zs.trToData(this.columns,this.$el.find(">tbody>tr"));o.length&&(this.options.data=o,this.fromHtml=!0)}this.options.pagination&&"server"!==this.options.sidePagination||(this.footerData=zs.trToData(this.columns,this.$el.find(">tfoot>tr"))),this.footerData&&this.$el.find("tfoot").html("<tr></tr>"),!this.options.showFooter||this.options.cardView?this.$tableFooter.hide():this.$tableFooter.show()}},{key:"initHeader",value:function(){var t=this,e={},n=[];this.header={fields:[],styles:[],classes:[],formatters:[],detailFormatters:[],events:[],sorters:[],sortNames:[],cellStyles:[],searchables:[]},zs.updateFieldGroup(this.options.columns),this.options.columns.forEach((function(i,o){var a=[];a.push("<tr".concat(zs.sprintf(' class="%s"',t._headerTrClasses[o])," ").concat(zs.sprintf(' style="%s"',t._headerTrStyles[o]),">"));var r="";if(0===o&&zs.hasDetailViewIcon(t.options)){var l=t.options.columns.length>1?' rowspan="'.concat(t.options.columns.length,'"'):"";r='<th class="detail"'.concat(l,'>\n          <div class="fht-cell"></div>\n          </th>')}r&&"right"!==t.options.detailViewAlign&&a.push(r),i.forEach((function(i,n){var r=zs.sprintf(' class="%s"',i.class),l=i.widthUnit,c=parseFloat(i.width),h=zs.sprintf("text-align: %s; ",i.halign?i.halign:i.align),u=zs.sprintf("text-align: %s; ",i.align),d=zs.sprintf("vertical-align: %s; ",i.valign);if(d+=zs.sprintf("width: %s; ",!i.checkbox&&!i.radio||c?c?c+l:void 0:i.showSelectTitle?void 0:"36px"),void 0!==i.fieldIndex||i.visible){var f=zs.calculateObjectValue(null,t.options.headerStyle,[i]),p=[],g="";if(f&&f.css)for(var v=0,b=Object.entries(f.css);v<b.length;v++){var m=s(b[v],2),y=m[0],w=m[1];p.push("".concat(y,": ").concat(w))}if(f&&f.classes&&(g=zs.sprintf(' class="%s"',i.class?[i.class,f.classes].join(" "):f.classes)),void 0!==i.fieldIndex){if(t.header.fields[i.fieldIndex]=i.field,t.header.styles[i.fieldIndex]=u+d,t.header.classes[i.fieldIndex]=r,t.header.formatters[i.fieldIndex]=i.formatter,t.header.detailFormatters[i.fieldIndex]=i.detailFormatter,t.header.events[i.fieldIndex]=i.events,t.header.sorters[i.fieldIndex]=i.sorter,t.header.sortNames[i.fieldIndex]=i.sortName,t.header.cellStyles[i.fieldIndex]=i.cellStyle,t.header.searchables[i.fieldIndex]=i.searchable,!i.visible)return;if(t.options.cardView&&!i.cardVisible)return;e[i.field]=i}a.push("<th".concat(zs.sprintf(' title="%s"',i.titleTooltip)),i.checkbox||i.radio?zs.sprintf(' class="bs-checkbox %s"',i.class||""):g||r,zs.sprintf(' style="%s"',h+d+p.join("; ")),zs.sprintf(' rowspan="%s"',i.rowspan),zs.sprintf(' colspan="%s"',i.colspan),zs.sprintf(' data-field="%s"',i.field),0===n&&o>0?" data-not-first-th":"",">"),a.push(zs.sprintf('<div class="th-inner %s">',t.options.sortable&&i.sortable?"sortable both":""));var S=t.options.escape?zs.escapeHTML(i.title):i.title,x=S;i.checkbox&&(S="",!t.options.singleSelect&&t.options.checkboxHeader&&(S='<label><input name="btSelectAll" type="checkbox" /><span></span></label>'),t.header.stateField=i.field),i.radio&&(S="",t.header.stateField=i.field),!S&&i.showSelectTitle&&(S+=x),a.push(S),a.push("</div>"),a.push('<div class="fht-cell"></div>'),a.push("</div>"),a.push("</th>")}})),r&&"right"===t.options.detailViewAlign&&a.push(r),a.push("</tr>"),a.length>3&&n.push(a.join(""))})),this.$header.html(n.join("")),this.$header.find("th[data-field]").each((function(t,n){i.default(n).data(e[i.default(n).data("field")])})),this.$container.off("click",".th-inner").on("click",".th-inner",(function(e){var n=i.default(e.currentTarget);if(t.options.detailView&&!n.parent().hasClass("bs-checkbox")&&n.closest(".bootstrap-table")[0]!==t.$container[0])return!1;t.options.sortable&&n.parent().data().sortable&&t.onSort(e)}));var o=zs.getEventName("resize.bootstrap-table",this.$el.attr("id"));i.default(window).off(o),!this.options.showHeader||this.options.cardView?(this.$header.hide(),this.$tableHeader.hide(),this.$tableLoading.css("top",0)):(this.$header.show(),this.$tableHeader.show(),this.$tableLoading.css("top",this.$header.outerHeight()+1),this.getCaret(),i.default(window).on(o,(function(){return t.resetView()}))),this.$selectAll=this.$header.find('[name="btSelectAll"]'),this.$selectAll.off("click").on("click",(function(e){e.stopPropagation();var n=i.default(e.currentTarget).prop("checked");t[n?"checkAll":"uncheckAll"](),t.updateSelected()}))}},{key:"initData",value:function(t,e){"append"===e?this.options.data=this.options.data.concat(t):"prepend"===e?this.options.data=[].concat(t).concat(this.options.data):(t=t||zs.deepCopy(this.options.data),this.options.data=Array.isArray(t)?t:t[this.options.dataField]),this.data=l(this.options.data),this.options.sortReset&&(this.unsortedData=l(this.data)),"server"!==this.options.sidePagination&&this.initSort()}},{key:"initSort",value:function(){var t=this,e=this.options.sortName,i="desc"===this.options.sortOrder?-1:1,n=this.header.fields.indexOf(this.options.sortName),o=0;-1!==n?(this.options.sortStable&&this.data.forEach((function(t,e){t.hasOwnProperty("_position")||(t._position=e)})),this.options.customSort?zs.calculateObjectValue(this.options,this.options.customSort,[this.options.sortName,this.options.sortOrder,this.data]):this.data.sort((function(o,a){t.header.sortNames[n]&&(e=t.header.sortNames[n]);var r=zs.getItemField(o,e,t.options.escape),s=zs.getItemField(a,e,t.options.escape),l=zs.calculateObjectValue(t.header,t.header.sorters[n],[r,s,o,a]);return void 0!==l?t.options.sortStable&&0===l?i*(o._position-a._position):i*l:zs.sort(r,s,i,t.options.sortStable,o._position,a._position)})),void 0!==this.options.sortClass&&(clearTimeout(o),o=setTimeout((function(){t.$el.removeClass(t.options.sortClass);var e=t.$header.find('[data-field="'.concat(t.options.sortName,'"]')).index();t.$el.find("tr td:nth-child(".concat(e+1,")")).addClass(t.options.sortClass)}),250))):this.options.sortReset&&(this.data=l(this.unsortedData))}},{key:"onSort",value:function(t){var e=t.type,n=t.currentTarget,o="keypress"===e?i.default(n):i.default(n).parent(),a=this.$header.find("th").eq(o.index());if(this.$header.add(this.$header_).find("span.order").remove(),this.options.sortName===o.data("field")){var r=this.options.sortOrder;void 0===r?this.options.sortOrder="asc":"asc"===r?this.options.sortOrder="desc":"desc"===this.options.sortOrder&&(this.options.sortOrder=this.options.sortReset?void 0:"asc"),void 0===this.options.sortOrder&&(this.options.sortName=void 0)}else this.options.sortName=o.data("field"),this.options.rememberOrder?this.options.sortOrder="asc"===o.data("order")?"desc":"asc":this.options.sortOrder=this.columns[this.fieldsColumnsIndex[o.data("field")]].sortOrder||this.columns[this.fieldsColumnsIndex[o.data("field")]].order;if(this.trigger("sort",this.options.sortName,this.options.sortOrder),o.add(a).data("order",this.options.sortOrder),this.getCaret(),"server"===this.options.sidePagination&&this.options.serverSort)return this.options.pageNumber=1,void this.initServer(this.options.silentSort);this.initSort(),this.initBody()}},{key:"initToolbar",value:function(){var t,e=this,o=this.options,a=[],r=0,l=0;this.$toolbar.find(".bs-bars").children().length&&i.default("body").append(i.default(o.toolbar)),this.$toolbar.html(""),"string"!=typeof o.toolbar&&"object"!==n(o.toolbar)||i.default(zs.sprintf('<div class="bs-bars %s-%s"></div>',this.constants.classes.pull,o.toolbarAlign)).appendTo(this.$toolbar).append(i.default(o.toolbar)),a=['<div class="'.concat(["columns","columns-".concat(o.buttonsAlign),this.constants.classes.buttonsGroup,"".concat(this.constants.classes.pull,"-").concat(o.buttonsAlign)].join(" "),'">')],"string"==typeof o.buttonsOrder&&(o.buttonsOrder=o.buttonsOrder.replace(/\[|\]| |'/g,"").split(",")),this.buttons=Object.assign(this.buttons,{paginationSwitch:{text:o.pagination?o.formatPaginationSwitchUp():o.formatPaginationSwitchDown(),icon:o.pagination?o.icons.paginationSwitchDown:o.icons.paginationSwitchUp,render:!1,event:this.togglePagination,attributes:{"aria-label":o.formatPaginationSwitch(),title:o.formatPaginationSwitch()}},refresh:{text:o.formatRefresh(),icon:o.icons.refresh,render:!1,event:this.refresh,attributes:{"aria-label":o.formatRefresh(),title:o.formatRefresh()}},toggle:{text:o.formatToggle(),icon:o.icons.toggleOff,render:!1,event:this.toggleView,attributes:{"aria-label":o.formatToggleOn(),title:o.formatToggleOn()}},fullscreen:{text:o.formatFullscreen(),icon:o.icons.fullscreen,render:!1,event:this.toggleFullscreen,attributes:{"aria-label":o.formatFullscreen(),title:o.formatFullscreen()}},columns:{render:!1,html:function(){var t=[];if(t.push('<div class="keep-open '.concat(e.constants.classes.buttonsDropdown,'" title="').concat(o.formatColumns(),'">\n            <button class="').concat(e.constants.buttonsClass,' dropdown-toggle" type="button" ').concat(e.constants.dataToggle,'="dropdown"\n            aria-label="Columns" title="').concat(o.formatColumns(),'">\n            ').concat(o.showButtonIcons?zs.sprintf(e.constants.html.icon,o.iconsPrefix,o.icons.columns):"","\n            ").concat(o.showButtonText?o.formatColumns():"","\n            ").concat(e.constants.html.dropdownCaret,"\n            </button>\n            ").concat(e.constants.html.toolbarDropdown[0])),o.showColumnsSearch&&(t.push(zs.sprintf(e.constants.html.toolbarDropdownItem,zs.sprintf('<input type="text" class="%s" name="columnsSearch" placeholder="%s" autocomplete="off">',e.constants.classes.input,o.formatSearch()))),t.push(e.constants.html.toolbarDropdownSeparator)),o.showColumnsToggleAll){var i=e.getVisibleColumns().length===e.columns.filter((function(t){return!e.isSelectionColumn(t)})).length;t.push(zs.sprintf(e.constants.html.toolbarDropdownItem,zs.sprintf('<input type="checkbox" class="toggle-all" %s> <span>%s</span>',i?'checked="checked"':"",o.formatColumnsToggleAll()))),t.push(e.constants.html.toolbarDropdownSeparator)}var n=0;return e.columns.forEach((function(t){t.visible&&n++})),e.columns.forEach((function(i,a){if(!e.isSelectionColumn(i)&&(!o.cardView||i.cardVisible)){var r=i.visible?' checked="checked"':"",s=n<=o.minimumCountColumns&&r?' disabled="disabled"':"";i.switchable&&(t.push(zs.sprintf(e.constants.html.toolbarDropdownItem,zs.sprintf('<input type="checkbox" data-field="%s" value="%s"%s%s> <span>%s</span>',i.field,a,r,s,i.title))),l++)}})),t.push(e.constants.html.toolbarDropdown[1],"</div>"),t.join("")}}});for(var c={},h=0,d=Object.entries(this.buttons);h<d.length;h++){var f=s(d[h],2),p=f[0],g=f[1],v=void 0;if(g.hasOwnProperty("html"))"function"==typeof g.html?v=g.html():"string"==typeof g.html&&(v=g.html);else{if(v='<button class="'.concat(this.constants.buttonsClass,'" type="button" name="').concat(p,'"'),g.hasOwnProperty("attributes"))for(var b=0,m=Object.entries(g.attributes);b<m.length;b++){var y=s(m[b],2),w=y[0],S=y[1];v+=" ".concat(w,'="').concat(S,'"')}v+=">",o.showButtonIcons&&g.hasOwnProperty("icon")&&(v+="".concat(zs.sprintf(this.constants.html.icon,o.iconsPrefix,g.icon)," ")),o.showButtonText&&g.hasOwnProperty("text")&&(v+=g.text),v+="</button>"}c[p]=v;var x="show".concat(p.charAt(0).toUpperCase()).concat(p.substring(1)),k=o[x];!(!g.hasOwnProperty("render")||g.hasOwnProperty("render")&&g.render)||void 0!==k&&!0!==k||(o[x]=!0),o.buttonsOrder.includes(p)||o.buttonsOrder.push(p)}var O,C=u(o.buttonsOrder);try{for(C.s();!(O=C.n()).done;){var T=O.value;o["show".concat(T.charAt(0).toUpperCase()).concat(T.substring(1))]&&a.push(c[T])}}catch(t){C.e(t)}finally{C.f()}a.push("</div>"),(this.showToolbar||a.length>2)&&this.$toolbar.append(a.join(""));for(var P=0,I=Object.entries(this.buttons);P<I.length;P++){var A=s(I[P],2),$=A[0],R=A[1];if(R.hasOwnProperty("event")){if("function"==typeof R.event||"string"==typeof R.event)if("continue"===function(){var t="string"==typeof R.event?window[R.event]:R.event;return e.$toolbar.find('button[name="'.concat($,'"]')).off("click").on("click",(function(){return t.call(e)})),"continue"}())continue;for(var E=function(){var t=s(F[j],2),i=t[0],n=t[1],o="string"==typeof n?window[n]:n;e.$toolbar.find('button[name="'.concat($,'"]')).off(i).on(i,(function(){return o.call(e)}))},j=0,F=Object.entries(R.event);j<F.length;j++)E()}}if(o.showColumns){var _=(t=this.$toolbar.find(".keep-open")).find('input[type="checkbox"]:not(".toggle-all")'),N=t.find('input[type="checkbox"].toggle-all');if(l<=o.minimumCountColumns&&t.find("input").prop("disabled",!0),t.find("li, label").off("click").on("click",(function(t){t.stopImmediatePropagation()})),_.off("click").on("click",(function(t){var n=t.currentTarget,o=i.default(n);e._toggleColumn(o.val(),o.prop("checked"),!1),e.trigger("column-switch",o.data("field"),o.prop("checked")),N.prop("checked",_.filter(":checked").length===e.columns.filter((function(t){return!e.isSelectionColumn(t)})).length)})),N.off("click").on("click",(function(t){var n=t.currentTarget;e._toggleAllColumns(i.default(n).prop("checked")),e.trigger("column-switch-all",i.default(n).prop("checked"))})),o.showColumnsSearch){var D=t.find('[name="columnsSearch"]'),V=t.find(".dropdown-item-marker");D.on("keyup paste change",(function(t){var e=t.currentTarget,n=i.default(e).val().toLowerCase();V.show(),_.each((function(t,e){var o=i.default(e).parents(".dropdown-item-marker");o.text().toLowerCase().includes(n)||o.hide()}))}))}}var B=function(t){var i="keyup drop blur mouseup";t.off(i).on(i,(function(t){o.searchOnEnterKey&&13!==t.keyCode||[37,38,39,40].includes(t.keyCode)||(clearTimeout(r),r=setTimeout((function(){e.onSearch({currentTarget:t.currentTarget})}),o.searchTimeOut))}))};if((o.search||this.showSearchClearButton)&&"string"!=typeof o.searchSelector){a=[];var L=zs.sprintf(this.constants.html.searchButton,this.constants.buttonsClass,o.formatSearch(),o.showButtonIcons?zs.sprintf(this.constants.html.icon,o.iconsPrefix,o.icons.search):"",o.showButtonText?o.formatSearch():""),H=zs.sprintf(this.constants.html.searchClearButton,this.constants.buttonsClass,o.formatClearSearch(),o.showButtonIcons?zs.sprintf(this.constants.html.icon,o.iconsPrefix,o.icons.clearSearch):"",o.showButtonText?o.formatClearSearch():""),M='<input class="'.concat(this.constants.classes.input,"\n        ").concat(zs.sprintf(" %s%s",this.constants.classes.inputPrefix,o.iconSize),'\n        search-input" type="search" placeholder="').concat(o.formatSearch(),'" autocomplete="off">'),U=M;if(o.showSearchButton||o.showSearchClearButton){var z=(o.showSearchButton?L:"")+(o.showSearchClearButton?H:"");U=o.search?zs.sprintf(this.constants.html.inputGroup,M,z):z}a.push(zs.sprintf('\n        <div class="'.concat(this.constants.classes.pull,"-").concat(o.searchAlign," search ").concat(this.constants.classes.inputGroup,'">\n          %s\n        </div>\n      '),U)),this.$toolbar.append(a.join(""));var q=zs.getSearchInput(this);o.showSearchButton?(this.$toolbar.find(".search button[name=search]").off("click").on("click",(function(){clearTimeout(r),r=setTimeout((function(){e.onSearch({currentTarget:q})}),o.searchTimeOut)})),o.searchOnEnterKey&&B(q)):B(q),o.showSearchClearButton&&this.$toolbar.find(".search button[name=clearSearch]").click((function(){e.resetSearch()}))}else if("string"==typeof o.searchSelector){B(zs.getSearchInput(this))}}},{key:"onSearch",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.currentTarget,n=t.firedByInitSearchText,o=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(void 0!==e&&i.default(e).length&&o){var a=i.default(e).val().trim();if(this.options.trimOnSearch&&i.default(e).val()!==a&&i.default(e).val(a),this.searchText===a)return;var r=zs.getSearchInput(this),s=e instanceof jQuery?e:i.default(e);(s.is(r)||s.hasClass("search-input"))&&(this.searchText=a,this.options.searchText=a)}n||this.options.cookie||(this.options.pageNumber=1),this.initSearch(),n?"client"===this.options.sidePagination&&this.updatePagination():this.updatePagination(),this.trigger("search",this.searchText)}},{key:"initSearch",value:function(){var t=this;if(this.filterOptions=this.filterOptions||this.options.filterOptions,"server"!==this.options.sidePagination){if(this.options.customSearch)return this.data=zs.calculateObjectValue(this.options,this.options.customSearch,[this.options.data,this.searchText,this.filterColumns]),void(this.options.sortReset&&(this.unsortedData=l(this.data)));var e=this.searchText&&(this.fromHtml?zs.escapeHTML(this.searchText):this.searchText),i=e?e.toLowerCase():"",n=zs.isEmptyObject(this.filterColumns)?null:this.filterColumns;this.options.searchAccentNeutralise&&(i=zs.normalizeAccent(i)),"function"==typeof this.filterOptions.filterAlgorithm?this.data=this.options.data.filter((function(e){return t.filterOptions.filterAlgorithm.apply(null,[e,n])})):"string"==typeof this.filterOptions.filterAlgorithm&&(this.data=n?this.options.data.filter((function(e){var i=t.filterOptions.filterAlgorithm;if("and"===i){for(var o in n)if(Array.isArray(n[o])&&!n[o].includes(e[o])||!Array.isArray(n[o])&&e[o]!==n[o])return!1}else if("or"===i){var a=!1;for(var r in n)(Array.isArray(n[r])&&n[r].includes(e[r])||!Array.isArray(n[r])&&e[r]===n[r])&&(a=!0);return a}return!0})):l(this.options.data));var o=this.getVisibleFields();this.data=i?this.data.filter((function(n,a){for(var r=0;r<t.header.fields.length;r++)if(t.header.searchables[r]&&(!t.options.visibleSearch||-1!==o.indexOf(t.header.fields[r]))){var s=zs.isNumeric(t.header.fields[r])?parseInt(t.header.fields[r],10):t.header.fields[r],l=t.columns[t.fieldsColumnsIndex[s]],c=void 0;if("string"==typeof s){c=n;for(var h=s.split("."),u=0;u<h.length;u++)null!==c[h[u]]&&(c=c[h[u]])}else c=n[s];if(t.options.searchAccentNeutralise&&(c=zs.normalizeAccent(c)),l&&l.searchFormatter&&(c=zs.calculateObjectValue(l,t.header.formatters[r],[c,n,a,l.field],c)),"string"==typeof c||"number"==typeof c){if(t.options.strictSearch&&"".concat(c).toLowerCase()===i||t.options.regexSearch&&zs.regexCompare(c,e))return!0;var d=/(?:(<=|=>|=<|>=|>|<)(?:\s+)?(-?\d+)?|(-?\d+)?(\s+)?(<=|=>|=<|>=|>|<))/gm.exec(t.searchText),f=!1;if(d){var p=d[1]||"".concat(d[5],"l"),g=d[2]||d[3],v=parseInt(c,10),b=parseInt(g,10);switch(p){case">":case"<l":f=v>b;break;case"<":case">l":f=v<b;break;case"<=":case"=<":case">=l":case"=>l":f=v<=b;break;case">=":case"=>":case"<=l":case"=<l":f=v>=b}}if(f||"".concat(c).toLowerCase().includes(i))return!0}}return!1})):this.data,this.options.sortReset&&(this.unsortedData=l(this.data)),this.initSort()}}},{key:"initPagination",value:function(){var t=this,e=this.options;if(e.pagination){this.$pagination.show();var i,n,o,a,r,s,l,c=[],h=!1,u=this.getData({includeHiddenRows:!1}),d=e.pageList;if("string"==typeof d&&(d=d.replace(/\[|\]| /g,"").toLowerCase().split(",")),d=d.map((function(t){return"string"==typeof t?t.toLowerCase()===e.formatAllRows().toLowerCase()||["all","unlimited"].includes(t.toLowerCase())?e.formatAllRows():+t:t})),this.paginationParts=e.paginationParts,"string"==typeof this.paginationParts&&(this.paginationParts=this.paginationParts.replace(/\[|\]| |'/g,"").split(",")),"server"!==e.sidePagination&&(e.totalRows=u.length),this.totalPages=0,e.totalRows&&(e.pageSize===e.formatAllRows()&&(e.pageSize=e.totalRows,h=!0),this.totalPages=1+~~((e.totalRows-1)/e.pageSize),e.totalPages=this.totalPages),this.totalPages>0&&e.pageNumber>this.totalPages&&(e.pageNumber=this.totalPages),this.pageFrom=(e.pageNumber-1)*e.pageSize+1,this.pageTo=e.pageNumber*e.pageSize,this.pageTo>e.totalRows&&(this.pageTo=e.totalRows),this.options.pagination&&"server"!==this.options.sidePagination&&(this.options.totalNotFiltered=this.options.data.length),this.options.showExtendedPagination||(this.options.totalNotFiltered=void 0),(this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")||this.paginationParts.includes("pageSize"))&&c.push('<div class="'.concat(this.constants.classes.pull,"-").concat(e.paginationDetailHAlign,' pagination-detail">')),this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")){var f=this.paginationParts.includes("pageInfoShort")?e.formatDetailPagination(e.totalRows):e.formatShowingRows(this.pageFrom,this.pageTo,e.totalRows,e.totalNotFiltered);c.push('<span class="pagination-info">\n      '.concat(f,"\n      </span>"))}if(this.paginationParts.includes("pageSize")){c.push('<div class="page-list">');var p=['<div class="'.concat(this.constants.classes.paginationDropdown,'">\n        <button class="').concat(this.constants.buttonsClass,' dropdown-toggle" type="button" ').concat(this.constants.dataToggle,'="dropdown">\n        <span class="page-size">\n        ').concat(h?e.formatAllRows():e.pageSize,"\n        </span>\n        ").concat(this.constants.html.dropdownCaret,"\n        </button>\n        ").concat(this.constants.html.pageDropdown[0])];d.forEach((function(i,n){var o;(!e.smartDisplay||0===n||d[n-1]<e.totalRows||i===e.formatAllRows())&&(o=h?i===e.formatAllRows()?t.constants.classes.dropdownActive:"":i===e.pageSize?t.constants.classes.dropdownActive:"",p.push(zs.sprintf(t.constants.html.pageDropdownItem,o,i)))})),p.push("".concat(this.constants.html.pageDropdown[1],"</div>")),c.push(e.formatRecordsPerPage(p.join("")))}if((this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")||this.paginationParts.includes("pageSize"))&&c.push("</div></div>"),this.paginationParts.includes("pageList")){c.push('<div class="'.concat(this.constants.classes.pull,"-").concat(e.paginationHAlign,' pagination">'),zs.sprintf(this.constants.html.pagination[0],zs.sprintf(" pagination-%s",e.iconSize)),zs.sprintf(this.constants.html.paginationItem," page-pre",e.formatSRPaginationPreText(),e.paginationPreText)),this.totalPages<e.paginationSuccessivelySize?(n=1,o=this.totalPages):o=(n=e.pageNumber-e.paginationPagesBySide)+2*e.paginationPagesBySide,e.pageNumber<e.paginationSuccessivelySize-1&&(o=e.paginationSuccessivelySize),e.paginationSuccessivelySize>this.totalPages-n&&(n=n-(e.paginationSuccessivelySize-(this.totalPages-n))+1),n<1&&(n=1),o>this.totalPages&&(o=this.totalPages);var g=Math.round(e.paginationPagesBySide/2),v=function(i){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return zs.sprintf(t.constants.html.paginationItem,n+(i===e.pageNumber?" ".concat(t.constants.classes.paginationActive):""),e.formatSRPaginationPageText(i),i)};if(n>1){var b=e.paginationPagesBySide;for(b>=n&&(b=n-1),i=1;i<=b;i++)c.push(v(i));n-1===b+1?(i=n-1,c.push(v(i))):n-1>b&&(n-2*e.paginationPagesBySide>e.paginationPagesBySide&&e.paginationUseIntermediate?(i=Math.round((n-g)/2+g),c.push(v(i," page-intermediate"))):c.push(zs.sprintf(this.constants.html.paginationItem," page-first-separator disabled","","...")))}for(i=n;i<=o;i++)c.push(v(i));if(this.totalPages>o){var m=this.totalPages-(e.paginationPagesBySide-1);for(o>=m&&(m=o+1),o+1===m-1?(i=o+1,c.push(v(i))):m>o+1&&(this.totalPages-o>2*e.paginationPagesBySide&&e.paginationUseIntermediate?(i=Math.round((this.totalPages-g-o)/2+o),c.push(v(i," page-intermediate"))):c.push(zs.sprintf(this.constants.html.paginationItem," page-last-separator disabled","","..."))),i=m;i<=this.totalPages;i++)c.push(v(i))}c.push(zs.sprintf(this.constants.html.paginationItem," page-next",e.formatSRPaginationNextText(),e.paginationNextText)),c.push(this.constants.html.pagination[1],"</div>")}this.$pagination.html(c.join(""));var y=["bottom","both"].includes(e.paginationVAlign)?" ".concat(this.constants.classes.dropup):"";this.$pagination.last().find(".page-list > div").addClass(y),e.onlyInfoPagination||(a=this.$pagination.find(".page-list a"),r=this.$pagination.find(".page-pre"),s=this.$pagination.find(".page-next"),l=this.$pagination.find(".page-item").not(".page-next, .page-pre, .page-last-separator, .page-first-separator"),this.totalPages<=1&&this.$pagination.find("div.pagination").hide(),e.smartDisplay&&(d.length<2||e.totalRows<=d[0])&&this.$pagination.find("div.page-list").hide(),this.$pagination[this.getData().length?"show":"hide"](),e.paginationLoop||(1===e.pageNumber&&r.addClass("disabled"),e.pageNumber===this.totalPages&&s.addClass("disabled")),h&&(e.pageSize=e.formatAllRows()),a.off("click").on("click",(function(e){return t.onPageListChange(e)})),r.off("click").on("click",(function(e){return t.onPagePre(e)})),s.off("click").on("click",(function(e){return t.onPageNext(e)})),l.off("click").on("click",(function(e){return t.onPageNumber(e)})))}else this.$pagination.hide()}},{key:"updatePagination",value:function(t){t&&i.default(t.currentTarget).hasClass("disabled")||(this.options.maintainMetaData||this.resetRows(),this.initPagination(),this.trigger("page-change",this.options.pageNumber,this.options.pageSize),"server"===this.options.sidePagination?this.initServer():this.initBody())}},{key:"onPageListChange",value:function(t){t.preventDefault();var e=i.default(t.currentTarget);return e.parent().addClass(this.constants.classes.dropdownActive).siblings().removeClass(this.constants.classes.dropdownActive),this.options.pageSize=e.text().toUpperCase()===this.options.formatAllRows().toUpperCase()?this.options.formatAllRows():+e.text(),this.$toolbar.find(".page-size").text(this.options.pageSize),this.updatePagination(t),!1}},{key:"onPagePre",value:function(t){if(!i.default(t.target).hasClass("disabled"))return t.preventDefault(),this.options.pageNumber-1==0?this.options.pageNumber=this.options.totalPages:this.options.pageNumber--,this.updatePagination(t),!1}},{key:"onPageNext",value:function(t){if(!i.default(t.target).hasClass("disabled"))return t.preventDefault(),this.options.pageNumber+1>this.options.totalPages?this.options.pageNumber=1:this.options.pageNumber++,this.updatePagination(t),!1}},{key:"onPageNumber",value:function(t){if(t.preventDefault(),this.options.pageNumber!==+i.default(t.currentTarget).text())return this.options.pageNumber=+i.default(t.currentTarget).text(),this.updatePagination(t),!1}},{key:"initRow",value:function(t,e,i,o){var a=this,r=[],l={},c=[],h="",u={},d=[];if(!(zs.findIndex(this.hiddenRows,t)>-1)){if((l=zs.calculateObjectValue(this.options,this.options.rowStyle,[t,e],l))&&l.css)for(var f=0,p=Object.entries(l.css);f<p.length;f++){var g=s(p[f],2),v=g[0],b=g[1];c.push("".concat(v,": ").concat(b))}if(u=zs.calculateObjectValue(this.options,this.options.rowAttributes,[t,e],u))for(var m=0,y=Object.entries(u);m<y.length;m++){var w=s(y[m],2),S=w[0],x=w[1];d.push("".concat(S,'="').concat(zs.escapeHTML(x),'"'))}if(t._data&&!zs.isEmptyObject(t._data))for(var k=0,O=Object.entries(t._data);k<O.length;k++){var C=s(O[k],2),T=C[0],P=C[1];if("index"===T)return;h+=" data-".concat(T,"='").concat("object"===n(P)?JSON.stringify(P):P,"'")}r.push("<tr",zs.sprintf(" %s",d.length?d.join(" "):void 0),zs.sprintf(' id="%s"',Array.isArray(t)?void 0:t._id),zs.sprintf(' class="%s"',l.classes||(Array.isArray(t)?void 0:t._class)),zs.sprintf(' style="%s"',Array.isArray(t)?void 0:t._style),' data-index="'.concat(e,'"'),zs.sprintf(' data-uniqueid="%s"',zs.getItemField(t,this.options.uniqueId,!1)),zs.sprintf(' data-has-detail-view="%s"',this.options.detailView&&zs.calculateObjectValue(null,this.options.detailFilter,[e,t])?"true":void 0),zs.sprintf("%s",h),">"),this.options.cardView&&r.push('<td colspan="'.concat(this.header.fields.length,'"><div class="card-views">'));var I="";return zs.hasDetailViewIcon(this.options)&&(I="<td>",zs.calculateObjectValue(null,this.options.detailFilter,[e,t])&&(I+='\n          <a class="detail-icon" href="#">\n          '.concat(zs.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailOpen),"\n          </a>\n        ")),I+="</td>"),I&&"right"!==this.options.detailViewAlign&&r.push(I),this.header.fields.forEach((function(i,n){var o=a.columns[n],l="",h=zs.getItemField(t,i,a.options.escape,o.escape),u="",d="",f={},p="",g=a.header.classes[n],v="",b="",m="",y="",w="",S="";if((!a.fromHtml&&!a.autoMergeCells||void 0!==h||o.checkbox||o.radio)&&o.visible&&(!a.options.cardView||o.cardVisible)){if(o.escape&&(h=zs.escapeHTML(h)),c.concat([a.header.styles[n]]).length&&(b+="".concat(c.concat([a.header.styles[n]]).join("; "))),t["_".concat(i,"_style")]&&(b+="".concat(t["_".concat(i,"_style")])),b&&(v=' style="'.concat(b,'"')),t["_".concat(i,"_id")]&&(p=zs.sprintf(' id="%s"',t["_".concat(i,"_id")])),t["_".concat(i,"_class")]&&(g=zs.sprintf(' class="%s"',t["_".concat(i,"_class")])),t["_".concat(i,"_rowspan")]&&(y=zs.sprintf(' rowspan="%s"',t["_".concat(i,"_rowspan")])),t["_".concat(i,"_colspan")]&&(w=zs.sprintf(' colspan="%s"',t["_".concat(i,"_colspan")])),t["_".concat(i,"_title")]&&(S=zs.sprintf(' title="%s"',t["_".concat(i,"_title")])),(f=zs.calculateObjectValue(a.header,a.header.cellStyles[n],[h,t,e,i],f)).classes&&(g=' class="'.concat(f.classes,'"')),f.css){for(var x=[],k=0,O=Object.entries(f.css);k<O.length;k++){var C=s(O[k],2),T=C[0],P=C[1];x.push("".concat(T,": ").concat(P))}v=' style="'.concat(x.concat(a.header.styles[n]).join("; "),'"')}if(u=zs.calculateObjectValue(o,a.header.formatters[n],[h,t,e,i],h),o.checkbox||o.radio||(u=null==u?a.options.undefinedText:u),o.searchable&&a.searchText&&a.options.searchHighlight&&!o.checkbox&&!o.radio){var I="",A=new RegExp("(".concat(a.searchText.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),")"),"gim"),$="<mark>$1</mark>";if(u&&/<(?=.*? .*?\/ ?>|br|hr|input|!--|wbr)[a-z]+.*?>|<([a-z]+).*?<\/\1>/i.test(u)){var R=(new DOMParser).parseFromString(u.toString(),"text/html").documentElement.textContent,E=R.replace(A,$);R=R.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),I=u.replace(new RegExp("(>\\s*)(".concat(R,")(\\s*)"),"gm"),"$1".concat(E,"$3"))}else I=u.toString().replace(A,$);u=zs.calculateObjectValue(o,o.searchHighlightFormatter,[u,a.searchText],I)}if(t["_".concat(i,"_data")]&&!zs.isEmptyObject(t["_".concat(i,"_data")]))for(var j=0,F=Object.entries(t["_".concat(i,"_data")]);j<F.length;j++){var _=s(F[j],2),N=_[0],D=_[1];if("index"===N)return;m+=" data-".concat(N,'="').concat(D,'"')}if(o.checkbox||o.radio){d=o.checkbox?"checkbox":d,d=o.radio?"radio":d;var V=o.class||"",B=zs.isObject(u)&&u.hasOwnProperty("checked")?u.checked:(!0===u||h)&&!1!==u,L=!o.checkboxEnabled||u&&u.disabled;l=[a.options.cardView?'<div class="card-view '.concat(V,'">'):'<td class="bs-checkbox '.concat(V,'"').concat(g).concat(v,">"),'<label>\n            <input\n            data-index="'.concat(e,'"\n            name="').concat(a.options.selectItemName,'"\n            type="').concat(d,'"\n            ').concat(zs.sprintf('value="%s"',t[a.options.idField]),"\n            ").concat(zs.sprintf('checked="%s"',B?"checked":void 0),"\n            ").concat(zs.sprintf('disabled="%s"',L?"disabled":void 0)," />\n            <span></span>\n            </label>"),a.header.formatters[n]&&"string"==typeof u?u:"",a.options.cardView?"</div>":"</td>"].join(""),t[a.header.stateField]=!0===u||!!h||u&&u.checked}else if(a.options.cardView){var H=a.options.showHeader?'<span class="card-view-title '.concat(f.classes||"",'"').concat(v,">").concat(zs.getFieldTitle(a.columns,i),"</span>"):"";l='<div class="card-view">'.concat(H,'<span class="card-view-value ').concat(f.classes||"",'"').concat(v,">").concat(u,"</span></div>"),a.options.smartDisplay&&""===u&&(l='<div class="card-view"></div>')}else l="<td".concat(p).concat(g).concat(v).concat(m).concat(y).concat(w).concat(S,">").concat(u,"</td>");r.push(l)}})),I&&"right"===this.options.detailViewAlign&&r.push(I),this.options.cardView&&r.push("</div></td>"),r.push("</tr>"),r.join("")}}},{key:"initBody",value:function(t,e){var n=this,o=this.getData();this.trigger("pre-body",o),this.$body=this.$el.find(">tbody"),this.$body.length||(this.$body=i.default("<tbody></tbody>").appendTo(this.$el)),this.options.pagination&&"server"!==this.options.sidePagination||(this.pageFrom=1,this.pageTo=o.length);var a=[],r=i.default(document.createDocumentFragment()),s=!1,l=[];this.autoMergeCells=zs.checkAutoMergeCells(o.slice(this.pageFrom-1,this.pageTo));for(var c=this.pageFrom-1;c<this.pageTo;c++){var h=o[c],u=this.initRow(h,c,o,r);if(s=s||!!u,u&&"string"==typeof u){var d=this.options.uniqueId;if(d&&h.hasOwnProperty(d)){var f=h[d],p=this.$body.find(zs.sprintf('> tr[data-uniqueid="%s"][data-has-detail-view]',f)).next();p.is("tr.detail-view")&&(l.push(c),e&&f===e||(u+=p[0].outerHTML))}this.options.virtualScroll?a.push(u):r.append(u)}}s?this.options.virtualScroll?(this.virtualScroll&&this.virtualScroll.destroy(),this.virtualScroll=new Xs({rows:a,fixedScroll:t,scrollEl:this.$tableBody[0],contentEl:this.$body[0],itemHeight:this.options.virtualScrollItemHeight,callback:function(t,e){n.fitHeader(),n.initBodyEvent(),n.trigger("virtual-scroll",t,e)}})):this.$body.html(r):this.$body.html('<tr class="no-records-found">'.concat(zs.sprintf('<td colspan="%s">%s</td>',this.getVisibleFields().length+zs.getDetailViewIndexOffset(this.options),this.options.formatNoMatches()),"</tr>")),l.forEach((function(t){n.expandRow(t)})),t||this.scrollTo(0),this.initBodyEvent(),this.initFooter(),this.resetView(),this.updateSelected(),"server"!==this.options.sidePagination&&(this.options.totalRows=o.length),this.trigger("post-body",o)}},{key:"initBodyEvent",value:function(){var t=this;this.$body.find("> tr[data-index] > td").off("click dblclick").on("click dblclick",(function(e){var n=i.default(e.currentTarget),o=n.parent(),a=i.default(e.target).parents(".card-views").children(),r=i.default(e.target).parents(".card-view"),s=o.data("index"),l=t.data[s],c=t.options.cardView?a.index(r):n[0].cellIndex,h=t.getVisibleFields()[c-zs.getDetailViewIndexOffset(t.options)],u=t.columns[t.fieldsColumnsIndex[h]],d=zs.getItemField(l,h,t.options.escape,u.escape);if(!n.find(".detail-icon").length){if(t.trigger("click"===e.type?"click-cell":"dbl-click-cell",h,d,l,n),t.trigger("click"===e.type?"click-row":"dbl-click-row",l,o,h),"click"===e.type&&t.options.clickToSelect&&u.clickToSelect&&!zs.calculateObjectValue(t.options,t.options.ignoreClickToSelectOn,[e.target])){var f=o.find(zs.sprintf('[name="%s"]',t.options.selectItemName));f.length&&f[0].click()}"click"===e.type&&t.options.detailViewByClick&&t.toggleDetailView(s,t.header.detailFormatters[t.fieldsColumnsIndex[h]])}})).off("mousedown").on("mousedown",(function(e){t.multipleSelectRowCtrlKey=e.ctrlKey||e.metaKey,t.multipleSelectRowShiftKey=e.shiftKey})),this.$body.find("> tr[data-index] > td > .detail-icon").off("click").on("click",(function(e){return e.preventDefault(),t.toggleDetailView(i.default(e.currentTarget).parent().parent().data("index")),!1})),this.$selectItem=this.$body.find(zs.sprintf('[name="%s"]',this.options.selectItemName)),this.$selectItem.off("click").on("click",(function(e){e.stopImmediatePropagation();var n=i.default(e.currentTarget);t._toggleCheck(n.prop("checked"),n.data("index"))})),this.header.events.forEach((function(e,n){var o=e;if(o){if("string"==typeof o&&(o=zs.calculateObjectValue(null,o)),!o)throw new Error("Unknown event in the scope: ".concat(e));var a=t.header.fields[n],r=t.getVisibleFields().indexOf(a);if(-1!==r){r+=zs.getDetailViewIndexOffset(t.options);var s=function(e){if(!o.hasOwnProperty(e))return"continue";var n=o[e];t.$body.find(">tr:not(.no-records-found)").each((function(o,s){var l=i.default(s),c=l.find(t.options.cardView?".card-views>.card-view":">td").eq(r),h=e.indexOf(" "),u=e.substring(0,h),d=e.substring(h+1);c.find(d).off(u).on(u,(function(e){var i=l.data("index"),o=t.data[i],r=o[a];n.apply(t,[e,r,o,i])}))}))};for(var l in o)s(l)}}}))}},{key:"initServer",value:function(t,e,n){var o=this,a={},r=this.header.fields.indexOf(this.options.sortName),s={searchText:this.searchText,sortName:this.options.sortName,sortOrder:this.options.sortOrder};if(this.header.sortNames[r]&&(s.sortName=this.header.sortNames[r]),this.options.pagination&&"server"===this.options.sidePagination&&(s.pageSize=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,s.pageNumber=this.options.pageNumber),n||this.options.url||this.options.ajax){if("limit"===this.options.queryParamsType&&(s={search:s.searchText,sort:s.sortName,order:s.sortOrder},this.options.pagination&&"server"===this.options.sidePagination&&(s.offset=this.options.pageSize===this.options.formatAllRows()?0:this.options.pageSize*(this.options.pageNumber-1),s.limit=this.options.pageSize,0!==s.limit&&this.options.pageSize!==this.options.formatAllRows()||delete s.limit)),this.options.search&&"server"===this.options.sidePagination&&this.columns.filter((function(t){return!t.searchable})).length){s.searchable=[];var l,c=u(this.columns);try{for(c.s();!(l=c.n()).done;){var h=l.value;!h.checkbox&&h.searchable&&(this.options.visibleSearch&&h.visible||!this.options.visibleSearch)&&s.searchable.push(h.field)}}catch(t){c.e(t)}finally{c.f()}}if(zs.isEmptyObject(this.filterColumnsPartial)||(s.filter=JSON.stringify(this.filterColumnsPartial,null)),i.default.extend(s,e||{}),!1!==(a=zs.calculateObjectValue(this.options,this.options.queryParams,[s],a))){t||this.showLoading();var d=i.default.extend({},zs.calculateObjectValue(null,this.options.ajaxOptions),{type:this.options.method,url:n||this.options.url,data:"application/json"===this.options.contentType&&"post"===this.options.method?JSON.stringify(a):a,cache:this.options.cache,contentType:this.options.contentType,dataType:this.options.dataType,success:function(e,i,n){var a=zs.calculateObjectValue(o.options,o.options.responseHandler,[e,n],e);o.load(a),o.trigger("load-success",a,n&&n.status,n),t||o.hideLoading(),"server"===o.options.sidePagination&&o.options.pageNumber>1&&a[o.options.totalField]>0&&!a[o.options.dataField].length&&o.updatePagination()},error:function(e){if(e&&0===e.status&&o._xhrAbort)o._xhrAbort=!1;else{var i=[];"server"===o.options.sidePagination&&((i={})[o.options.totalField]=0,i[o.options.dataField]=[]),o.load(i),o.trigger("load-error",e&&e.status,e),t||o.hideLoading()}}});return this.options.ajax?zs.calculateObjectValue(this,this.options.ajax,[d],null):(this._xhr&&4!==this._xhr.readyState&&(this._xhrAbort=!0,this._xhr.abort()),this._xhr=i.default.ajax(d)),a}}}},{key:"initSearchText",value:function(){if(this.options.search&&(this.searchText="",""!==this.options.searchText)){var t=zs.getSearchInput(this);t.val(this.options.searchText),this.onSearch({currentTarget:t,firedByInitSearchText:!0})}}},{key:"getCaret",value:function(){var t=this;this.$header.find("th").each((function(e,n){i.default(n).find(".sortable").removeClass("desc asc").addClass(i.default(n).data("field")===t.options.sortName?t.options.sortOrder:"both")}))}},{key:"updateSelected",value:function(){var t=this.$selectItem.filter(":enabled").length&&this.$selectItem.filter(":enabled").length===this.$selectItem.filter(":enabled").filter(":checked").length;this.$selectAll.add(this.$selectAll_).prop("checked",t),this.$selectItem.each((function(t,e){i.default(e).closest("tr")[i.default(e).prop("checked")?"addClass":"removeClass"]("selected")}))}},{key:"updateRows",value:function(){var t=this;this.$selectItem.each((function(e,n){t.data[i.default(n).data("index")][t.header.stateField]=i.default(n).prop("checked")}))}},{key:"resetRows",value:function(){var t,e=u(this.data);try{for(e.s();!(t=e.n()).done;){var i=t.value;this.$selectAll.prop("checked",!1),this.$selectItem.prop("checked",!1),this.header.stateField&&(i[this.header.stateField]=!1)}}catch(t){e.e(t)}finally{e.f()}this.initHiddenRows()}},{key:"trigger",value:function(e){for(var n,o,a="".concat(e,".bs.table"),r=arguments.length,s=new Array(r>1?r-1:0),l=1;l<r;l++)s[l-1]=arguments[l];(n=this.options)[t.EVENTS[a]].apply(n,[].concat(s,[this])),this.$el.trigger(i.default.Event(a,{sender:this}),s),(o=this.options).onAll.apply(o,[a].concat([].concat(s,[this]))),this.$el.trigger(i.default.Event("all.bs.table",{sender:this}),[a,s])}},{key:"resetHeader",value:function(){var t=this;clearTimeout(this.timeoutId_),this.timeoutId_=setTimeout((function(){return t.fitHeader()}),this.$el.is(":hidden")?100:0)}},{key:"fitHeader",value:function(){var t=this;if(this.$el.is(":hidden"))this.timeoutId_=setTimeout((function(){return t.fitHeader()}),100);else{var e=this.$tableBody.get(0),n=this.hasScrollBar&&e.scrollHeight>e.clientHeight+this.$header.outerHeight()?zs.getScrollBarWidth():0;this.$el.css("margin-top",-this.$header.outerHeight());var o=i.default(":focus");if(o.length>0){var a=o.parents("th");if(a.length>0){var r=a.attr("data-field");if(void 0!==r){var s=this.$header.find("[data-field='".concat(r,"']"));s.length>0&&s.find(":input").addClass("focus-temp")}}}this.$header_=this.$header.clone(!0,!0),this.$selectAll_=this.$header_.find('[name="btSelectAll"]'),this.$tableHeader.css("margin-right",n).find("table").css("width",this.$el.outerWidth()).html("").attr("class",this.$el.attr("class")).append(this.$header_),this.$tableLoading.css("width",this.$el.outerWidth());var l=i.default(".focus-temp:visible:eq(0)");l.length>0&&(l.focus(),this.$header.find(".focus-temp").removeClass("focus-temp")),this.$header.find("th[data-field]").each((function(e,n){t.$header_.find(zs.sprintf('th[data-field="%s"]',i.default(n).data("field"))).data(i.default(n).data())}));for(var c=this.getVisibleFields(),h=this.$header_.find("th"),u=this.$body.find(">tr:not(.no-records-found,.virtual-scroll-top)").eq(0);u.length&&u.find('>td[colspan]:not([colspan="1"])').length;)u=u.next();var d=u.find("> *").length;u.find("> *").each((function(e,n){var o=i.default(n);if(zs.hasDetailViewIcon(t.options)&&(0===e&&"right"!==t.options.detailViewAlign||e===d-1&&"right"===t.options.detailViewAlign)){var a=h.filter(".detail"),r=a.innerWidth()-a.find(".fht-cell").width();a.find(".fht-cell").width(o.innerWidth()-r)}else{var s=e-zs.getDetailViewIndexOffset(t.options),l=t.$header_.find(zs.sprintf('th[data-field="%s"]',c[s]));l.length>1&&(l=i.default(h[o[0].cellIndex]));var u=l.innerWidth()-l.find(".fht-cell").width();l.find(".fht-cell").width(o.innerWidth()-u)}})),this.horizontalScroll(),this.trigger("post-header")}}},{key:"initFooter",value:function(){if(this.options.showFooter&&!this.options.cardView){var t=this.getData(),e=[],i="";zs.hasDetailViewIcon(this.options)&&(i='<th class="detail"><div class="th-inner"></div><div class="fht-cell"></div></th>'),i&&"right"!==this.options.detailViewAlign&&e.push(i);var n,o=u(this.columns);try{for(o.s();!(n=o.n()).done;){var a,r,l=n.value,c=[],h={},d=zs.sprintf(' class="%s"',l.class);if(!(!l.visible||this.footerData&&this.footerData.length>0&&!(l.field in this.footerData[0]))){if(this.options.cardView&&!l.cardVisible)return;if(a=zs.sprintf("text-align: %s; ",l.falign?l.falign:l.align),r=zs.sprintf("vertical-align: %s; ",l.valign),(h=zs.calculateObjectValue(null,this.options.footerStyle,[l]))&&h.css)for(var f=0,p=Object.entries(h.css);f<p.length;f++){var g=s(p[f],2),v=g[0],b=g[1];c.push("".concat(v,": ").concat(b))}h&&h.classes&&(d=zs.sprintf(' class="%s"',l.class?[l.class,h.classes].join(" "):h.classes)),e.push("<th",d,zs.sprintf(' style="%s"',a+r+c.concat().join("; ")));var m=0;this.footerData&&this.footerData.length>0&&(m=this.footerData[0]["_".concat(l.field,"_colspan")]||0),m&&e.push(' colspan="'.concat(m,'" ')),e.push(">"),e.push('<div class="th-inner">');var y="";this.footerData&&this.footerData.length>0&&(y=this.footerData[0][l.field]||""),e.push(zs.calculateObjectValue(l,l.footerFormatter,[t,y],y)),e.push("</div>"),e.push('<div class="fht-cell"></div>'),e.push("</div>"),e.push("</th>")}}}catch(t){o.e(t)}finally{o.f()}i&&"right"===this.options.detailViewAlign&&e.push(i),this.options.height||this.$tableFooter.length||(this.$el.append("<tfoot><tr></tr></tfoot>"),this.$tableFooter=this.$el.find("tfoot")),this.$tableFooter.find("tr").length||this.$tableFooter.html("<table><thead><tr></tr></thead></table>"),this.$tableFooter.find("tr").html(e.join("")),this.trigger("post-footer",this.$tableFooter)}}},{key:"fitFooter",value:function(){var t=this;if(this.$el.is(":hidden"))setTimeout((function(){return t.fitFooter()}),100);else{var e=this.$tableBody.get(0),n=this.hasScrollBar&&e.scrollHeight>e.clientHeight+this.$header.outerHeight()?zs.getScrollBarWidth():0;this.$tableFooter.css("margin-right",n).find("table").css("width",this.$el.outerWidth()).attr("class",this.$el.attr("class"));var o=this.$tableFooter.find("th"),a=this.$body.find(">tr:first-child:not(.no-records-found)");for(o.find(".fht-cell").width("auto");a.length&&a.find('>td[colspan]:not([colspan="1"])').length;)a=a.next();var r=a.find("> *").length;a.find("> *").each((function(e,n){var a=i.default(n);if(zs.hasDetailViewIcon(t.options)&&(0===e&&"left"===t.options.detailViewAlign||e===r-1&&"right"===t.options.detailViewAlign)){var s=o.filter(".detail"),l=s.innerWidth()-s.find(".fht-cell").width();s.find(".fht-cell").width(a.innerWidth()-l)}else{var c=o.eq(e),h=c.innerWidth()-c.find(".fht-cell").width();c.find(".fht-cell").width(a.innerWidth()-h)}})),this.horizontalScroll()}}},{key:"horizontalScroll",value:function(){var t=this;this.$tableBody.off("scroll").on("scroll",(function(){var e=t.$tableBody.scrollLeft();t.options.showHeader&&t.options.height&&t.$tableHeader.scrollLeft(e),t.options.showFooter&&!t.options.cardView&&t.$tableFooter.scrollLeft(e),t.trigger("scroll-body",t.$tableBody)}))}},{key:"getVisibleFields",value:function(){var t,e=[],i=u(this.header.fields);try{for(i.s();!(t=i.n()).done;){var n=t.value,o=this.columns[this.fieldsColumnsIndex[n]];o&&o.visible&&(!this.options.cardView||o.cardVisible)&&e.push(n)}}catch(t){i.e(t)}finally{i.f()}return e}},{key:"initHiddenRows",value:function(){this.hiddenRows=[]}},{key:"getOptions",value:function(){var t=i.default.extend({},this.options);return delete t.data,i.default.extend(!0,{},t)}},{key:"refreshOptions",value:function(t){zs.compareObjects(this.options,t,!0)||(this.options=i.default.extend(this.options,t),this.trigger("refresh-options",this.options),this.destroy(),this.init())}},{key:"getData",value:function(t){var e=this,i=this.options.data;if(!(this.searchText||this.options.customSearch||void 0!==this.options.sortName||this.enableCustomSort)&&zs.isEmptyObject(this.filterColumns)&&zs.isEmptyObject(this.filterColumnsPartial)||t&&t.unfiltered||(i=this.data),t&&t.useCurrentPage&&(i=i.slice(this.pageFrom-1,this.pageTo)),t&&!t.includeHiddenRows){var n=this.getHiddenRows();i=i.filter((function(t){return-1===zs.findIndex(n,t)}))}return t&&t.formatted&&i.forEach((function(t){for(var i=0,n=Object.entries(t);i<n.length;i++){var o=s(n[i],2),a=o[0],r=o[1],l=e.columns[e.fieldsColumnsIndex[a]];if(!l)return;t[a]=zs.calculateObjectValue(l,e.header.formatters[l.fieldIndex],[r,t,t.index,l.field],r)}})),i}},{key:"getSelections",value:function(){var t=this;return(this.options.maintainMetaData?this.options.data:this.data).filter((function(e){return!0===e[t.header.stateField]}))}},{key:"load",value:function(t){var e,i=t;this.options.pagination&&"server"===this.options.sidePagination&&(this.options.totalRows=i[this.options.totalField],this.options.totalNotFiltered=i[this.options.totalNotFilteredField],this.footerData=i[this.options.footerField]?[i[this.options.footerField]]:void 0),e=i.fixedScroll,i=Array.isArray(i)?i:i[this.options.dataField],this.initData(i),this.initSearch(),this.initPagination(),this.initBody(e)}},{key:"append",value:function(t){this.initData(t,"append"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"prepend",value:function(t){this.initData(t,"prepend"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"remove",value:function(t){for(var e=0,i=this.options.data.length-1;i>=0;i--){var n=this.options.data[i];(n.hasOwnProperty(t.field)||"$index"===t.field)&&((!n.hasOwnProperty(t.field)&&"$index"===t.field&&t.values.includes(i)||t.values.includes(n[t.field]))&&(e++,this.options.data.splice(i,1)))}e&&("server"===this.options.sidePagination&&(this.options.totalRows-=e,this.data=l(this.options.data)),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},{key:"removeAll",value:function(){this.options.data.length>0&&(this.options.data.splice(0,this.options.data.length),this.initSearch(),this.initPagination(),this.initBody(!0))}},{key:"insertRow",value:function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("row")&&(this.options.data.splice(t.index,0,t.row),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},{key:"updateRow",value:function(t){var e,n=u(Array.isArray(t)?t:[t]);try{for(n.s();!(e=n.n()).done;){var o=e.value;o.hasOwnProperty("index")&&o.hasOwnProperty("row")&&(o.hasOwnProperty("replace")&&o.replace?this.options.data[o.index]=o.row:i.default.extend(this.options.data[o.index],o.row))}}catch(t){n.e(t)}finally{n.f()}this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"getRowByUniqueId",value:function(t){var e,i,n,o=this.options.uniqueId,a=t,r=null;for(e=this.options.data.length-1;e>=0;e--){if((i=this.options.data[e]).hasOwnProperty(o))n=i[o];else{if(!i._data||!i._data.hasOwnProperty(o))continue;n=i._data[o]}if("string"==typeof n?a=a.toString():"number"==typeof n&&(Number(n)===n&&n%1==0?a=parseInt(a,10):n===Number(n)&&0!==n&&(a=parseFloat(a))),n===a){r=i;break}}return r}},{key:"updateByUniqueId",value:function(t){var e,n=null,o=u(Array.isArray(t)?t:[t]);try{for(o.s();!(e=o.n()).done;){var a=e.value;if(a.hasOwnProperty("id")&&a.hasOwnProperty("row")){var r=this.options.data.indexOf(this.getRowByUniqueId(a.id));-1!==r&&(a.hasOwnProperty("replace")&&a.replace?this.options.data[r]=a.row:i.default.extend(this.options.data[r],a.row),n=a.id)}}}catch(t){o.e(t)}finally{o.f()}this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0,n)}},{key:"removeByUniqueId",value:function(t){var e=this.options.data.length,i=this.getRowByUniqueId(t);i&&this.options.data.splice(this.options.data.indexOf(i),1),e!==this.options.data.length&&("server"===this.options.sidePagination&&(this.options.totalRows-=1,this.data=l(this.options.data)),this.initSearch(),this.initPagination(),this.initBody(!0))}},{key:"updateCell",value:function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("field")&&t.hasOwnProperty("value")&&(this.data[t.index][t.field]=t.value,!1!==t.reinit&&(this.initSort(),this.initBody(!0)))}},{key:"updateCellByUniqueId",value:function(t){var e=this;(Array.isArray(t)?t:[t]).forEach((function(t){var i=t.id,n=t.field,o=t.value,a=e.options.data.indexOf(e.getRowByUniqueId(i));-1!==a&&(e.options.data[a][n]=o)})),!1!==t.reinit&&(this.initSort(),this.initBody(!0))}},{key:"showRow",value:function(t){this._toggleRow(t,!0)}},{key:"hideRow",value:function(t){this._toggleRow(t,!1)}},{key:"_toggleRow",value:function(t,e){var i;if(t.hasOwnProperty("index")?i=this.getData()[t.index]:t.hasOwnProperty("uniqueId")&&(i=this.getRowByUniqueId(t.uniqueId)),i){var n=zs.findIndex(this.hiddenRows,i);e||-1!==n?e&&n>-1&&this.hiddenRows.splice(n,1):this.hiddenRows.push(i),this.initBody(!0),this.initPagination()}}},{key:"getHiddenRows",value:function(t){if(t)return this.initHiddenRows(),this.initBody(!0),void this.initPagination();var e,i=[],n=u(this.getData());try{for(n.s();!(e=n.n()).done;){var o=e.value;this.hiddenRows.includes(o)&&i.push(o)}}catch(t){n.e(t)}finally{n.f()}return this.hiddenRows=i,i}},{key:"showColumn",value:function(t){var e=this;(Array.isArray(t)?t:[t]).forEach((function(t){e._toggleColumn(e.fieldsColumnsIndex[t],!0,!0)}))}},{key:"hideColumn",value:function(t){var e=this;(Array.isArray(t)?t:[t]).forEach((function(t){e._toggleColumn(e.fieldsColumnsIndex[t],!1,!0)}))}},{key:"_toggleColumn",value:function(t,e,i){if(-1!==t&&this.columns[t].visible!==e&&(this.columns[t].visible=e,this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns)){var n=this.$toolbar.find('.keep-open input:not(".toggle-all")').prop("disabled",!1);i&&n.filter(zs.sprintf('[value="%s"]',t)).prop("checked",e),n.filter(":checked").length<=this.options.minimumCountColumns&&n.filter(":checked").prop("disabled",!0)}}},{key:"getVisibleColumns",value:function(){var t=this;return this.columns.filter((function(e){return e.visible&&!t.isSelectionColumn(e)}))}},{key:"getHiddenColumns",value:function(){return this.columns.filter((function(t){return!t.visible}))}},{key:"isSelectionColumn",value:function(t){return t.radio||t.checkbox}},{key:"showAllColumns",value:function(){this._toggleAllColumns(!0)}},{key:"hideAllColumns",value:function(){this._toggleAllColumns(!1)}},{key:"_toggleAllColumns",value:function(t){var e,n=this,o=u(this.columns.slice().reverse());try{for(o.s();!(e=o.n()).done;){var a=e.value;if(a.switchable){if(!t&&this.options.showColumns&&this.getVisibleColumns().filter((function(t){return t.switchable})).length===this.options.minimumCountColumns)continue;a.visible=t}}}catch(t){o.e(t)}finally{o.f()}if(this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns){var r=this.$toolbar.find('.keep-open input[type="checkbox"]:not(".toggle-all")').prop("disabled",!1);t?r.prop("checked",t):r.get().reverse().forEach((function(e){r.filter(":checked").length>n.options.minimumCountColumns&&i.default(e).prop("checked",t)})),r.filter(":checked").length<=this.options.minimumCountColumns&&r.filter(":checked").prop("disabled",!0)}}},{key:"mergeCells",value:function(t){var e,i,n=t.index,o=this.getVisibleFields().indexOf(t.field),a=t.rowspan||1,r=t.colspan||1,s=this.$body.find(">tr[data-index]");o+=zs.getDetailViewIndexOffset(this.options);var l=s.eq(n).find(">td").eq(o);if(!(n<0||o<0||n>=this.data.length)){for(e=n;e<n+a;e++)for(i=o;i<o+r;i++)s.eq(e).find(">td").eq(i).hide();l.attr("rowspan",a).attr("colspan",r).show()}}},{key:"checkAll",value:function(){this._toggleCheckAll(!0)}},{key:"uncheckAll",value:function(){this._toggleCheckAll(!1)}},{key:"_toggleCheckAll",value:function(t){var e=this.getSelections();this.$selectAll.add(this.$selectAll_).prop("checked",t),this.$selectItem.filter(":enabled").prop("checked",t),this.updateRows(),this.updateSelected();var i=this.getSelections();t?this.trigger("check-all",i,e):this.trigger("uncheck-all",i,e)}},{key:"checkInvert",value:function(){var t=this.$selectItem.filter(":enabled"),e=t.filter(":checked");t.each((function(t,e){i.default(e).prop("checked",!i.default(e).prop("checked"))})),this.updateRows(),this.updateSelected(),this.trigger("uncheck-some",e),e=this.getSelections(),this.trigger("check-some",e)}},{key:"check",value:function(t){this._toggleCheck(!0,t)}},{key:"uncheck",value:function(t){this._toggleCheck(!1,t)}},{key:"_toggleCheck",value:function(t,e){var i=this.$selectItem.filter('[data-index="'.concat(e,'"]')),n=this.data[e];if(i.is(":radio")||this.options.singleSelect||this.options.multipleSelectRow&&!this.multipleSelectRowCtrlKey&&!this.multipleSelectRowShiftKey){var o,a=u(this.options.data);try{for(a.s();!(o=a.n()).done;){o.value[this.header.stateField]=!1}}catch(t){a.e(t)}finally{a.f()}this.$selectItem.filter(":checked").not(i).prop("checked",!1)}if(n[this.header.stateField]=t,this.options.multipleSelectRow){if(this.multipleSelectRowShiftKey&&this.multipleSelectRowLastSelectedIndex>=0)for(var r=s(this.multipleSelectRowLastSelectedIndex<e?[this.multipleSelectRowLastSelectedIndex,e]:[e,this.multipleSelectRowLastSelectedIndex],2),l=r[0],c=r[1],h=l+1;h<c;h++)this.data[h][this.header.stateField]=!0,this.$selectItem.filter('[data-index="'.concat(h,'"]')).prop("checked",!0);this.multipleSelectRowCtrlKey=!1,this.multipleSelectRowShiftKey=!1,this.multipleSelectRowLastSelectedIndex=t?e:-1}i.prop("checked",t),this.updateSelected(),this.trigger(t?"check":"uncheck",this.data[e],i)}},{key:"checkBy",value:function(t){this._toggleCheckBy(!0,t)}},{key:"uncheckBy",value:function(t){this._toggleCheckBy(!1,t)}},{key:"_toggleCheckBy",value:function(t,e){var i=this;if(e.hasOwnProperty("field")&&e.hasOwnProperty("values")){var n=[];this.data.forEach((function(o,a){if(!o.hasOwnProperty(e.field))return!1;if(e.values.includes(o[e.field])){var r=i.$selectItem.filter(":enabled").filter(zs.sprintf('[data-index="%s"]',a)),s=!!e.hasOwnProperty("onlyCurrentPage")&&e.onlyCurrentPage;if(!(r=t?r.not(":checked"):r.filter(":checked")).length&&s)return;r.prop("checked",t),o[i.header.stateField]=t,n.push(o),i.trigger(t?"check":"uncheck",o,r)}})),this.updateSelected(),this.trigger(t?"check-some":"uncheck-some",n)}}},{key:"refresh",value:function(t){t&&t.url&&(this.options.url=t.url),t&&t.pageNumber&&(this.options.pageNumber=t.pageNumber),t&&t.pageSize&&(this.options.pageSize=t.pageSize),this.trigger("refresh",this.initServer(t&&t.silent,t&&t.query,t&&t.url))}},{key:"destroy",value:function(){this.$el.insertBefore(this.$container),i.default(this.options.toolbar).insertBefore(this.$el),this.$container.next().remove(),this.$container.remove(),this.$el.html(this.$el_.html()).css("margin-top","0").attr("class",this.$el_.attr("class")||"")}},{key:"resetView",value:function(t){var e=0;if(t&&t.height&&(this.options.height=t.height),this.$tableContainer.toggleClass("has-card-view",this.options.cardView),this.options.height){var i=this.$tableBody.get(0);this.hasScrollBar=i.scrollWidth>i.clientWidth}if(!this.options.cardView&&this.options.showHeader&&this.options.height?(this.$tableHeader.show(),this.resetHeader(),e+=this.$header.outerHeight(!0)+1):(this.$tableHeader.hide(),this.trigger("post-header")),!this.options.cardView&&this.options.showFooter&&(this.$tableFooter.show(),this.fitFooter(),this.options.height&&(e+=this.$tableFooter.outerHeight(!0))),this.$container.hasClass("fullscreen"))this.$tableContainer.css("height",""),this.$tableContainer.css("width","");else if(this.options.height){this.$tableBorder&&(this.$tableBorder.css("width",""),this.$tableBorder.css("height",""));var n=this.$toolbar.outerHeight(!0),o=this.$pagination.outerHeight(!0),a=this.options.height-n-o,r=this.$tableBody.find(">table"),s=r.outerHeight();if(this.$tableContainer.css("height","".concat(a,"px")),this.$tableBorder&&r.is(":visible")){var l=a-s-2;this.hasScrollBar&&(l-=zs.getScrollBarWidth()),this.$tableBorder.css("width","".concat(r.outerWidth(),"px")),this.$tableBorder.css("height","".concat(l,"px"))}}this.options.cardView?(this.$el.css("margin-top","0"),this.$tableContainer.css("padding-bottom","0"),this.$tableFooter.hide()):(this.getCaret(),this.$tableContainer.css("padding-bottom","".concat(e,"px"))),this.trigger("reset-view")}},{key:"showLoading",value:function(){this.$tableLoading.toggleClass("open",!0);var t=this.options.loadingFontSize;"auto"===this.options.loadingFontSize&&(t=.04*this.$tableLoading.width(),t=Math.max(12,t),t=Math.min(32,t),t="".concat(t,"px")),this.$tableLoading.find(".loading-text").css("font-size",t)}},{key:"hideLoading",value:function(){this.$tableLoading.toggleClass("open",!1)}},{key:"togglePagination",value:function(){this.options.pagination=!this.options.pagination;var t=this.options.showButtonIcons?this.options.pagination?this.options.icons.paginationSwitchDown:this.options.icons.paginationSwitchUp:"",e=this.options.showButtonText?this.options.pagination?this.options.formatPaginationSwitchUp():this.options.formatPaginationSwitchDown():"";this.$toolbar.find('button[name="paginationSwitch"]').html("".concat(zs.sprintf(this.constants.html.icon,this.options.iconsPrefix,t)," ").concat(e)),this.updatePagination(),this.trigger("toggle-pagination",this.options.pagination)}},{key:"toggleFullscreen",value:function(){this.$el.closest(".bootstrap-table").toggleClass("fullscreen"),this.resetView()}},{key:"toggleView",value:function(){this.options.cardView=!this.options.cardView,this.initHeader();var t=this.options.showButtonIcons?this.options.cardView?this.options.icons.toggleOn:this.options.icons.toggleOff:"",e=this.options.showButtonText?this.options.cardView?this.options.formatToggleOff():this.options.formatToggleOn():"";this.$toolbar.find('button[name="toggle"]').html("".concat(zs.sprintf(this.constants.html.icon,this.options.iconsPrefix,t)," ").concat(e)),this.initBody(),this.trigger("toggle",this.options.cardView)}},{key:"resetSearch",value:function(t){var e=zs.getSearchInput(this),i=t||"";e.val(i),this.searchText=i,this.onSearch({currentTarget:e},!1)}},{key:"filterBy",value:function(t,e){this.filterOptions=zs.isEmptyObject(e)?this.options.filterOptions:i.default.extend(this.options.filterOptions,e),this.filterColumns=zs.isEmptyObject(t)?{}:t,this.options.pageNumber=1,this.initSearch(),this.updatePagination()}},{key:"scrollTo",value:function(t){var e={unit:"px",value:0};"object"===n(t)?e=Object.assign(e,t):"string"==typeof t&&"bottom"===t?e.value=this.$tableBody[0].scrollHeight:"string"!=typeof t&&"number"!=typeof t||(e.value=t);var o=e.value;"rows"===e.unit&&(o=0,this.$body.find("> tr:lt(".concat(e.value,")")).each((function(t,e){o+=i.default(e).outerHeight(!0)}))),this.$tableBody.scrollTop(o)}},{key:"getScrollPosition",value:function(){return this.$tableBody.scrollTop()}},{key:"selectPage",value:function(t){t>0&&t<=this.options.totalPages&&(this.options.pageNumber=t,this.updatePagination())}},{key:"prevPage",value:function(){this.options.pageNumber>1&&(this.options.pageNumber--,this.updatePagination())}},{key:"nextPage",value:function(){this.options.pageNumber<this.options.totalPages&&(this.options.pageNumber++,this.updatePagination())}},{key:"toggleDetailView",value:function(t,e){this.$body.find(zs.sprintf('> tr[data-index="%s"]',t)).next().is("tr.detail-view")?this.collapseRow(t):this.expandRow(t,e),this.resetView()}},{key:"expandRow",value:function(t,e){var i=this.data[t],n=this.$body.find(zs.sprintf('> tr[data-index="%s"][data-has-detail-view]',t));if(this.options.detailViewIcon&&n.find("a.detail-icon").html(zs.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailClose)),!n.next().is("tr.detail-view")){n.after(zs.sprintf('<tr class="detail-view"><td colspan="%s"></td></tr>',n.children("td").length));var o=n.next().find("td"),a=e||this.options.detailFormatter,r=zs.calculateObjectValue(this.options,a,[t,i,o],"");1===o.length&&o.append(r),this.trigger("expand-row",t,i,o)}}},{key:"expandRowByUniqueId",value:function(t){var e=this.getRowByUniqueId(t);e&&this.expandRow(this.data.indexOf(e))}},{key:"collapseRow",value:function(t){var e=this.data[t],i=this.$body.find(zs.sprintf('> tr[data-index="%s"][data-has-detail-view]',t));i.next().is("tr.detail-view")&&(this.options.detailViewIcon&&i.find("a.detail-icon").html(zs.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailOpen)),this.trigger("collapse-row",t,e,i.next()),i.next().remove())}},{key:"collapseRowByUniqueId",value:function(t){var e=this.getRowByUniqueId(t);e&&this.collapseRow(this.data.indexOf(e))}},{key:"expandAllRows",value:function(){for(var t=this.$body.find("> tr[data-index][data-has-detail-view]"),e=0;e<t.length;e++)this.expandRow(i.default(t[e]).data("index"))}},{key:"collapseAllRows",value:function(){for(var t=this.$body.find("> tr[data-index][data-has-detail-view]"),e=0;e<t.length;e++)this.collapseRow(i.default(t[e]).data("index"))}},{key:"updateColumnTitle",value:function(t){t.hasOwnProperty("field")&&t.hasOwnProperty("title")&&(this.columns[this.fieldsColumnsIndex[t.field]].title=this.options.escape?zs.escapeHTML(t.title):t.title,this.columns[this.fieldsColumnsIndex[t.field]].visible&&(this.$header.find("th[data-field]").each((function(e,n){if(i.default(n).data("field")===t.field)return i.default(i.default(n).find(".th-inner")[0]).text(t.title),!1})),this.resetView()))}},{key:"updateFormatText",value:function(t,e){/^format/.test(t)&&this.options[t]&&("string"==typeof e?this.options[t]=function(){return e}:"function"==typeof e&&(this.options[t]=e),this.initToolbar(),this.initPagination(),this.initBody())}}]),t}();return Qs.VERSION=Js.VERSION,Qs.DEFAULTS=Js.DEFAULTS,Qs.LOCALES=Js.LOCALES,Qs.COLUMN_DEFAULTS=Js.COLUMN_DEFAULTS,Qs.METHODS=Js.METHODS,Qs.EVENTS=Js.EVENTS,i.default.BootstrapTable=Qs,i.default.fn.bootstrapTable=function(t){for(var e=arguments.length,o=new Array(e>1?e-1:0),a=1;a<e;a++)o[a-1]=arguments[a];var r;return this.each((function(e,a){var s=i.default(a).data("bootstrap.table"),l=i.default.extend({},Qs.DEFAULTS,i.default(a).data(),"object"===n(t)&&t);if("string"==typeof t){var c;if(!Js.METHODS.includes(t))throw new Error("Unknown method: ".concat(t));if(!s)return;r=(c=s)[t].apply(c,o),"destroy"===t&&i.default(a).removeData("bootstrap.table")}s||(s=new i.default.BootstrapTable(a,l),i.default(a).data("bootstrap.table",s),s.init())})),void 0===r?this:r},i.default.fn.bootstrapTable.Constructor=Qs,i.default.fn.bootstrapTable.theme=Js.THEME,i.default.fn.bootstrapTable.VERSION=Js.VERSION,i.default.fn.bootstrapTable.defaults=Qs.DEFAULTS,i.default.fn.bootstrapTable.columnDefaults=Qs.COLUMN_DEFAULTS,i.default.fn.bootstrapTable.events=Qs.EVENTS,i.default.fn.bootstrapTable.locales=Qs.LOCALES,i.default.fn.bootstrapTable.methods=Qs.METHODS,i.default.fn.bootstrapTable.utils=zs,i.default((function(){i.default('[data-toggle="table"]').bootstrapTable()})),Qs}));
