/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.20.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var n=e(t);function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,e){return u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},u(t,e)}function c(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function a(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=i(t);if(e){var o=i(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return c(this,n)}}function f(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=i(t)););return t}function s(){return s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var r=f(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},s.apply(this,arguments)}var l="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function p(t,e){return t(e={exports:{}},e.exports),e.exports}var h,b,y=function(t){return t&&t.Math==Math&&t},d=y("object"==typeof globalThis&&globalThis)||y("object"==typeof window&&window)||y("object"==typeof self&&self)||y("object"==typeof l&&l)||function(){return this}()||Function("return this")(),v=function(t){try{return!!t()}catch(t){return!0}},g=!v((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),m=!v((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),w=Function.prototype.call,O=m?w.bind(w):function(){return w.apply(w,arguments)},j={}.propertyIsEnumerable,S=Object.getOwnPropertyDescriptor,R={f:S&&!j.call({1:2},1)?function(t){var e=S(this,t);return!!e&&e.enumerable}:j},P=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},T=Function.prototype,A=T.bind,E=T.call,x=m&&A.bind(E,E),I=m?function(t){return t&&x(t)}:function(t){return t&&function(){return E.apply(t,arguments)}},k=I({}.toString),F=I("".slice),_=function(t){return F(k(t),8,-1)},C=d.Object,M=I("".split),z=v((function(){return!C("z").propertyIsEnumerable(0)}))?function(t){return"String"==_(t)?M(t,""):C(t)}:C,B=d.TypeError,D=function(t){if(null==t)throw B("Can't call method on "+t);return t},L=function(t){return z(D(t))},N=function(t){return"function"==typeof t},G=function(t){return"object"==typeof t?null!==t:N(t)},q=function(t){return N(t)?t:void 0},W=function(t,e){return arguments.length<2?q(d[t]):d[t]&&d[t][e]},U=I({}.isPrototypeOf),$=W("navigator","userAgent")||"",X=d.process,K=d.Deno,Q=X&&X.versions||K&&K.version,V=Q&&Q.v8;V&&(b=(h=V.split("."))[0]>0&&h[0]<4?1:+(h[0]+h[1])),!b&&$&&(!(h=$.match(/Edge\/(\d+)/))||h[1]>=74)&&(h=$.match(/Chrome\/(\d+)/))&&(b=+h[1]);var Y=b,H=!!Object.getOwnPropertySymbols&&!v((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&Y&&Y<41})),J=H&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Z=d.Object,tt=J?function(t){return"symbol"==typeof t}:function(t){var e=W("Symbol");return N(e)&&U(e.prototype,Z(t))},et=d.String,nt=d.TypeError,rt=function(t){if(N(t))return t;throw nt(function(t){try{return et(t)}catch(t){return"Object"}}(t)+" is not a function")},ot=d.TypeError,it=Object.defineProperty,ut=function(t,e){try{it(d,t,{value:e,configurable:!0,writable:!0})}catch(n){d[t]=e}return e},ct="__core-js_shared__",at=d[ct]||ut(ct,{}),ft=p((function(t){(t.exports=function(t,e){return at[t]||(at[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.21.1",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})})),st=d.Object,lt=function(t){return st(D(t))},pt=I({}.hasOwnProperty),ht=Object.hasOwn||function(t,e){return pt(lt(t),e)},bt=0,yt=Math.random(),dt=I(1..toString),vt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+dt(++bt+yt,36)},gt=ft("wks"),mt=d.Symbol,wt=mt&&mt.for,Ot=J?mt:mt&&mt.withoutSetter||vt,jt=function(t){if(!ht(gt,t)||!H&&"string"!=typeof gt[t]){var e="Symbol."+t;H&&ht(mt,t)?gt[t]=mt[t]:gt[t]=J&&wt?wt(e):Ot(e)}return gt[t]},St=d.TypeError,Rt=jt("toPrimitive"),Pt=function(t,e){if(!G(t)||tt(t))return t;var n,r,o=null==(n=t[Rt])?void 0:rt(n);if(o){if(void 0===e&&(e="default"),r=O(o,t,e),!G(r)||tt(r))return r;throw St("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var n,r;if("string"===e&&N(n=t.toString)&&!G(r=O(n,t)))return r;if(N(n=t.valueOf)&&!G(r=O(n,t)))return r;if("string"!==e&&N(n=t.toString)&&!G(r=O(n,t)))return r;throw ot("Can't convert object to primitive value")}(t,e)},Tt=function(t){var e=Pt(t,"string");return tt(e)?e:e+""},At=d.document,Et=G(At)&&G(At.createElement),xt=function(t){return Et?At.createElement(t):{}},It=!g&&!v((function(){return 7!=Object.defineProperty(xt("div"),"a",{get:function(){return 7}}).a})),kt=Object.getOwnPropertyDescriptor,Ft={f:g?kt:function(t,e){if(t=L(t),e=Tt(e),It)try{return kt(t,e)}catch(t){}if(ht(t,e))return P(!O(R.f,t,e),t[e])}},_t=g&&v((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Ct=d.String,Mt=d.TypeError,zt=function(t){if(G(t))return t;throw Mt(Ct(t)+" is not an object")},Bt=d.TypeError,Dt=Object.defineProperty,Lt=Object.getOwnPropertyDescriptor,Nt="enumerable",Gt="configurable",qt="writable",Wt={f:g?_t?function(t,e,n){if(zt(t),e=Tt(e),zt(n),"function"==typeof t&&"prototype"===e&&"value"in n&&qt in n&&!n.writable){var r=Lt(t,e);r&&r.writable&&(t[e]=n.value,n={configurable:Gt in n?n.configurable:r.configurable,enumerable:Nt in n?n.enumerable:r.enumerable,writable:!1})}return Dt(t,e,n)}:Dt:function(t,e,n){if(zt(t),e=Tt(e),zt(n),It)try{return Dt(t,e,n)}catch(t){}if("get"in n||"set"in n)throw Bt("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},Ut=g?function(t,e,n){return Wt.f(t,e,P(1,n))}:function(t,e,n){return t[e]=n,t},$t=I(Function.toString);N(at.inspectSource)||(at.inspectSource=function(t){return $t(t)});var Xt,Kt,Qt,Vt=at.inspectSource,Yt=d.WeakMap,Ht=N(Yt)&&/native code/.test(Vt(Yt)),Jt=ft("keys"),Zt=function(t){return Jt[t]||(Jt[t]=vt(t))},te={},ee="Object already initialized",ne=d.TypeError,re=d.WeakMap;if(Ht||at.state){var oe=at.state||(at.state=new re),ie=I(oe.get),ue=I(oe.has),ce=I(oe.set);Xt=function(t,e){if(ue(oe,t))throw new ne(ee);return e.facade=t,ce(oe,t,e),e},Kt=function(t){return ie(oe,t)||{}},Qt=function(t){return ue(oe,t)}}else{var ae=Zt("state");te[ae]=!0,Xt=function(t,e){if(ht(t,ae))throw new ne(ee);return e.facade=t,Ut(t,ae,e),e},Kt=function(t){return ht(t,ae)?t[ae]:{}},Qt=function(t){return ht(t,ae)}}var fe={set:Xt,get:Kt,has:Qt,enforce:function(t){return Qt(t)?Kt(t):Xt(t,{})},getterFor:function(t){return function(e){var n;if(!G(e)||(n=Kt(e)).type!==t)throw ne("Incompatible receiver, "+t+" required");return n}}},se=Function.prototype,le=g&&Object.getOwnPropertyDescriptor,pe=ht(se,"name"),he={EXISTS:pe,PROPER:pe&&"something"===function(){}.name,CONFIGURABLE:pe&&(!g||g&&le(se,"name").configurable)},be=p((function(t){var e=he.CONFIGURABLE,n=fe.get,r=fe.enforce,o=String(String).split("String");(t.exports=function(t,n,i,u){var c,a=!!u&&!!u.unsafe,f=!!u&&!!u.enumerable,s=!!u&&!!u.noTargetGet,l=u&&void 0!==u.name?u.name:n;N(i)&&("Symbol("===String(l).slice(0,7)&&(l="["+String(l).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!ht(i,"name")||e&&i.name!==l)&&Ut(i,"name",l),(c=r(i)).source||(c.source=o.join("string"==typeof l?l:""))),t!==d?(a?!s&&t[n]&&(f=!0):delete t[n],f?t[n]=i:Ut(t,n,i)):f?t[n]=i:ut(n,i)})(Function.prototype,"toString",(function(){return N(this)&&n(this).source||Vt(this)}))})),ye=Math.ceil,de=Math.floor,ve=function(t){var e=+t;return e!=e||0===e?0:(e>0?de:ye)(e)},ge=Math.max,me=Math.min,we=Math.min,Oe=function(t){return(e=t.length)>0?we(ve(e),9007199254740991):0;var e},je=function(t){return function(e,n,r){var o,i=L(e),u=Oe(i),c=function(t,e){var n=ve(t);return n<0?ge(n+e,0):me(n,e)}(r,u);if(t&&n!=n){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===n)return t||c||0;return!t&&-1}},Se={includes:je(!0),indexOf:je(!1)}.indexOf,Re=I([].push),Pe=function(t,e){var n,r=L(t),o=0,i=[];for(n in r)!ht(te,n)&&ht(r,n)&&Re(i,n);for(;e.length>o;)ht(r,n=e[o++])&&(~Se(i,n)||Re(i,n));return i},Te=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Ae=Te.concat("length","prototype"),Ee={f:Object.getOwnPropertyNames||function(t){return Pe(t,Ae)}},xe={f:Object.getOwnPropertySymbols},Ie=I([].concat),ke=W("Reflect","ownKeys")||function(t){var e=Ee.f(zt(t)),n=xe.f;return n?Ie(e,n(t)):e},Fe=function(t,e,n){for(var r=ke(e),o=Wt.f,i=Ft.f,u=0;u<r.length;u++){var c=r[u];ht(t,c)||n&&ht(n,c)||o(t,c,i(e,c))}},_e=/#|\.prototype\./,Ce=function(t,e){var n=ze[Me(t)];return n==De||n!=Be&&(N(e)?v(e):!!e)},Me=Ce.normalize=function(t){return String(t).replace(_e,".").toLowerCase()},ze=Ce.data={},Be=Ce.NATIVE="N",De=Ce.POLYFILL="P",Le=Ce,Ne=Ft.f,Ge=function(t,e){var n,r,o,i,u,c=t.target,a=t.global,f=t.stat;if(n=a?d:f?d[c]||ut(c,{}):(d[c]||{}).prototype)for(r in e){if(i=e[r],o=t.noTargetGet?(u=Ne(n,r))&&u.value:n[r],!Le(a?r:c+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Fe(i,o)}(t.sham||o&&o.sham)&&Ut(i,"sham",!0),be(n,r,i,t)}},qe=Array.isArray||function(t){return"Array"==_(t)},We=function(t,e,n){var r=Tt(e);r in t?Wt.f(t,r,P(0,n)):t[r]=n},Ue={};Ue[jt("toStringTag")]="z";var $e="[object z]"===String(Ue),Xe=jt("toStringTag"),Ke=d.Object,Qe="Arguments"==_(function(){return arguments}()),Ve=$e?_:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=Ke(t),Xe))?n:Qe?_(e):"Object"==(r=_(e))&&N(e.callee)?"Arguments":r},Ye=function(){},He=[],Je=W("Reflect","construct"),Ze=/^\s*(?:class|function)\b/,tn=I(Ze.exec),en=!Ze.exec(Ye),nn=function(t){if(!N(t))return!1;try{return Je(Ye,He,t),!0}catch(t){return!1}},rn=function(t){if(!N(t))return!1;switch(Ve(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return en||!!tn(Ze,Vt(t))}catch(t){return!0}};rn.sham=!0;var on,un=!Je||v((function(){var t;return nn(nn.call)||!nn(Object)||!nn((function(){t=!0}))||t}))?rn:nn,cn=jt("species"),an=d.Array,fn=function(t,e){return new(function(t){var e;return qe(t)&&(e=t.constructor,(un(e)&&(e===an||qe(e.prototype))||G(e)&&null===(e=e[cn]))&&(e=void 0)),void 0===e?an:e}(t))(0===e?0:e)},sn=jt("species"),ln=jt("isConcatSpreadable"),pn=9007199254740991,hn="Maximum allowed index exceeded",bn=d.TypeError,yn=Y>=51||!v((function(){var t=[];return t[ln]=!1,t.concat()[0]!==t})),dn=(on="concat",Y>=51||!v((function(){var t=[];return(t.constructor={})[sn]=function(){return{foo:1}},1!==t[on](Boolean).foo}))),vn=function(t){if(!G(t))return!1;var e=t[ln];return void 0!==e?!!e:qe(t)};Ge({target:"Array",proto:!0,forced:!yn||!dn},{concat:function(t){var e,n,r,o,i,u=lt(this),c=fn(u,0),a=0;for(e=-1,r=arguments.length;e<r;e++)if(vn(i=-1===e?u:arguments[e])){if(a+(o=Oe(i))>pn)throw bn(hn);for(n=0;n<o;n++,a++)n in i&&We(c,a,i[n])}else{if(a>=pn)throw bn(hn);We(c,a++,i)}return c.length=a,c}});var gn=Object.keys||function(t){return Pe(t,Te)},mn=Object.assign,wn=Object.defineProperty,On=I([].concat),jn=!mn||v((function(){if(g&&1!==mn({b:1},mn(wn({},"a",{enumerable:!0,get:function(){wn(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},n=Symbol(),r="abcdefghijklmnopqrst";return t[n]=7,r.split("").forEach((function(t){e[t]=t})),7!=mn({},t)[n]||gn(mn({},e)).join("")!=r}))?function(t,e){for(var n=lt(t),r=arguments.length,o=1,i=xe.f,u=R.f;r>o;)for(var c,a=z(arguments[o++]),f=i?On(gn(a),i(a)):gn(a),s=f.length,l=0;s>l;)c=f[l++],g&&!O(u,a,c)||(n[c]=a[c]);return n}:mn;Ge({target:"Object",stat:!0,forced:Object.assign!==jn},{assign:jn});var Sn,Rn=I(I.bind),Pn=I([].push),Tn=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,u=7==t,c=5==t||i;return function(a,f,s,l){for(var p,h,b=lt(a),y=z(b),d=function(t,e){return rt(t),void 0===e?t:m?Rn(t,e):function(){return t.apply(e,arguments)}}(f,s),v=Oe(y),g=0,w=l||fn,O=e?w(a,v):n||u?w(a,0):void 0;v>g;g++)if((c||g in y)&&(h=d(p=y[g],g,b),t))if(e)O[g]=h;else if(h)switch(t){case 3:return!0;case 5:return p;case 6:return g;case 2:Pn(O,p)}else switch(t){case 4:return!1;case 7:Pn(O,p)}return i?-1:r||o?o:O}},An={forEach:Tn(0),map:Tn(1),filter:Tn(2),some:Tn(3),every:Tn(4),find:Tn(5),findIndex:Tn(6),filterReject:Tn(7)},En=g&&!_t?Object.defineProperties:function(t,e){zt(t);for(var n,r=L(e),o=gn(e),i=o.length,u=0;i>u;)Wt.f(t,n=o[u++],r[n]);return t},xn={f:En},In=W("document","documentElement"),kn=Zt("IE_PROTO"),Fn=function(){},_n=function(t){return"<script>"+t+"</"+"script>"},Cn=function(t){t.write(_n("")),t.close();var e=t.parentWindow.Object;return t=null,e},Mn=function(){try{Sn=new ActiveXObject("htmlfile")}catch(t){}var t,e;Mn="undefined"!=typeof document?document.domain&&Sn?Cn(Sn):((e=xt("iframe")).style.display="none",In.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(_n("document.F=Object")),t.close(),t.F):Cn(Sn);for(var n=Te.length;n--;)delete Mn.prototype[Te[n]];return Mn()};te[kn]=!0;var zn=Object.create||function(t,e){var n;return null!==t?(Fn.prototype=zt(t),n=new Fn,Fn.prototype=null,n[kn]=t):n=Mn(),void 0===e?n:xn.f(n,e)},Bn=jt("unscopables"),Dn=Array.prototype;null==Dn[Bn]&&Wt.f(Dn,Bn,{configurable:!0,value:zn(null)});var Ln,Nn=An.find,Gn="find",qn=!0;Gn in[]&&Array(1).find((function(){qn=!1})),Ge({target:"Array",proto:!0,forced:qn},{find:function(t){return Nn(this,t,arguments.length>1?arguments[1]:void 0)}}),Ln=Gn,Dn[Bn][Ln]=!0;var Wn=$e?{}.toString:function(){return"[object "+Ve(this)+"]"};$e||be(Object.prototype,"toString",Wn,{unsafe:!0});var Un=n.default.fn.bootstrapTable.utils;n.default.extend(n.default.fn.bootstrapTable.defaults,{autoRefresh:!1,showAutoRefresh:!0,autoRefreshInterval:60,autoRefreshSilent:!0,autoRefreshStatus:!0,autoRefreshFunction:null}),n.default.extend(n.default.fn.bootstrapTable.defaults.icons,{autoRefresh:{bootstrap3:"glyphicon-time icon-time",bootstrap5:"bi-clock",materialize:"access_time","bootstrap-table":"icon-clock"}[n.default.fn.bootstrapTable.theme]||"fa-clock"}),n.default.extend(n.default.fn.bootstrapTable.locales,{formatAutoRefresh:function(){return"Auto Refresh"}}),n.default.extend(n.default.fn.bootstrapTable.defaults,n.default.fn.bootstrapTable.locales),n.default.BootstrapTable=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&u(t,e)}(l,t);var e,n,c,f=a(l);function l(){return r(this,l),f.apply(this,arguments)}return e=l,n=[{key:"init",value:function(){for(var t,e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];(t=s(i(l.prototype),"init",this)).call.apply(t,[this].concat(n)),this.options.autoRefresh&&this.options.autoRefreshStatus&&this.setupRefreshInterval()}},{key:"initToolbar",value:function(){var t;this.options.autoRefresh&&(this.buttons=Object.assign(this.buttons,{autoRefresh:{html:'\n            <button class="auto-refresh '.concat(this.constants.buttonsClass,"\n              ").concat(this.options.autoRefreshStatus?" ".concat(this.constants.classes.buttonActive):"",'"\n              type="button" name="autoRefresh" title="').concat(this.options.formatAutoRefresh(),'">\n              ').concat(this.options.showButtonIcons?Un.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.autoRefresh):"","\n              ").concat(this.options.showButtonText?this.options.formatAutoRefresh():"","\n            </button>\n           "),event:this.toggleAutoRefresh}}));for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];(t=s(i(l.prototype),"initToolbar",this)).call.apply(t,[this].concat(n))}},{key:"toggleAutoRefresh",value:function(){this.options.autoRefresh&&(this.options.autoRefreshStatus?(clearInterval(this.options.autoRefreshFunction),this.$toolbar.find(">.columns .auto-refresh").removeClass(this.constants.classes.buttonActive)):(this.setupRefreshInterval(),this.$toolbar.find(">.columns .auto-refresh").addClass(this.constants.classes.buttonActive)),this.options.autoRefreshStatus=!this.options.autoRefreshStatus)}},{key:"destroy",value:function(){this.options.autoRefresh&&this.options.autoRefreshStatus&&clearInterval(this.options.autoRefreshFunction),s(i(l.prototype),"destroy",this).call(this)}},{key:"setupRefreshInterval",value:function(){var t=this;this.options.autoRefreshFunction=setInterval((function(){t.options.autoRefresh&&t.options.autoRefreshStatus&&t.refresh({silent:t.options.autoRefreshSilent})}),1e3*this.options.autoRefreshInterval)}}],n&&o(e.prototype,n),c&&o(e,c),Object.defineProperty(e,"prototype",{writable:!1}),l}(n.default.BootstrapTable)}));
