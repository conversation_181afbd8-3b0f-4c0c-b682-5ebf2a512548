/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.20.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var n=e(t);function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,e){return u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},u(t,e)}function c(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function a(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=i(t);if(e){var o=i(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return c(this,n)}}function f(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=i(t)););return t}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var r=f(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},l.apply(this,arguments)}var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function p(t,e){return t(e={exports:{}},e.exports),e.exports}var y,d,b=function(t){return t&&t.Math==Math&&t},h=b("object"==typeof globalThis&&globalThis)||b("object"==typeof window&&window)||b("object"==typeof self&&self)||b("object"==typeof s&&s)||function(){return this}()||Function("return this")(),v=function(t){try{return!!t()}catch(t){return!0}},g=!v((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),m=!v((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),w=Function.prototype.call,O=m?w.bind(w):function(){return w.apply(w,arguments)},j={}.propertyIsEnumerable,S=Object.getOwnPropertyDescriptor,T={f:S&&!j.call({1:2},1)?function(t){var e=S(this,t);return!!e&&e.enumerable}:j},P=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},C=Function.prototype,E=C.bind,R=C.call,x=m&&E.bind(R,R),A=m?function(t){return t&&x(t)}:function(t){return t&&function(){return R.apply(t,arguments)}},F=A({}.toString),_=A("".slice),k=function(t){return _(F(t),8,-1)},B=h.Object,I=A("".split),D=v((function(){return!B("z").propertyIsEnumerable(0)}))?function(t){return"String"==k(t)?I(t,""):B(t)}:B,M=h.TypeError,z=function(t){if(null==t)throw M("Can't call method on "+t);return t},N=function(t){return D(z(t))},L=function(t){return"function"==typeof t},W=function(t){return"object"==typeof t?null!==t:L(t)},G=function(t){return L(t)?t:void 0},q=function(t,e){return arguments.length<2?G(h[t]):h[t]&&h[t][e]},$=A({}.isPrototypeOf),H=q("navigator","userAgent")||"",U=h.process,V=h.Deno,X=U&&U.versions||V&&V.version,K=X&&X.v8;K&&(d=(y=K.split("."))[0]>0&&y[0]<4?1:+(y[0]+y[1])),!d&&H&&(!(y=H.match(/Edge\/(\d+)/))||y[1]>=74)&&(y=H.match(/Chrome\/(\d+)/))&&(d=+y[1]);var Q=d,Y=!!Object.getOwnPropertySymbols&&!v((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&Q&&Q<41})),J=Y&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Z=h.Object,tt=J?function(t){return"symbol"==typeof t}:function(t){var e=q("Symbol");return L(e)&&$(e.prototype,Z(t))},et=h.String,nt=h.TypeError,rt=function(t){if(L(t))return t;throw nt(function(t){try{return et(t)}catch(t){return"Object"}}(t)+" is not a function")},ot=h.TypeError,it=Object.defineProperty,ut=function(t,e){try{it(h,t,{value:e,configurable:!0,writable:!0})}catch(n){h[t]=e}return e},ct="__core-js_shared__",at=h[ct]||ut(ct,{}),ft=p((function(t){(t.exports=function(t,e){return at[t]||(at[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.21.1",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})})),lt=h.Object,st=function(t){return lt(z(t))},pt=A({}.hasOwnProperty),yt=Object.hasOwn||function(t,e){return pt(st(t),e)},dt=0,bt=Math.random(),ht=A(1..toString),vt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+ht(++dt+bt,36)},gt=ft("wks"),mt=h.Symbol,wt=mt&&mt.for,Ot=J?mt:mt&&mt.withoutSetter||vt,jt=function(t){if(!yt(gt,t)||!Y&&"string"!=typeof gt[t]){var e="Symbol."+t;Y&&yt(mt,t)?gt[t]=mt[t]:gt[t]=J&&wt?wt(e):Ot(e)}return gt[t]},St=h.TypeError,Tt=jt("toPrimitive"),Pt=function(t,e){if(!W(t)||tt(t))return t;var n,r,o=null==(n=t[Tt])?void 0:rt(n);if(o){if(void 0===e&&(e="default"),r=O(o,t,e),!W(r)||tt(r))return r;throw St("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var n,r;if("string"===e&&L(n=t.toString)&&!W(r=O(n,t)))return r;if(L(n=t.valueOf)&&!W(r=O(n,t)))return r;if("string"!==e&&L(n=t.toString)&&!W(r=O(n,t)))return r;throw ot("Can't convert object to primitive value")}(t,e)},Ct=function(t){var e=Pt(t,"string");return tt(e)?e:e+""},Et=h.document,Rt=W(Et)&&W(Et.createElement),xt=function(t){return Rt?Et.createElement(t):{}},At=!g&&!v((function(){return 7!=Object.defineProperty(xt("div"),"a",{get:function(){return 7}}).a})),Ft=Object.getOwnPropertyDescriptor,_t={f:g?Ft:function(t,e){if(t=N(t),e=Ct(e),At)try{return Ft(t,e)}catch(t){}if(yt(t,e))return P(!O(T.f,t,e),t[e])}},kt=g&&v((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Bt=h.String,It=h.TypeError,Dt=function(t){if(W(t))return t;throw It(Bt(t)+" is not an object")},Mt=h.TypeError,zt=Object.defineProperty,Nt=Object.getOwnPropertyDescriptor,Lt="enumerable",Wt="configurable",Gt="writable",qt={f:g?kt?function(t,e,n){if(Dt(t),e=Ct(e),Dt(n),"function"==typeof t&&"prototype"===e&&"value"in n&&Gt in n&&!n.writable){var r=Nt(t,e);r&&r.writable&&(t[e]=n.value,n={configurable:Wt in n?n.configurable:r.configurable,enumerable:Lt in n?n.enumerable:r.enumerable,writable:!1})}return zt(t,e,n)}:zt:function(t,e,n){if(Dt(t),e=Ct(e),Dt(n),At)try{return zt(t,e,n)}catch(t){}if("get"in n||"set"in n)throw Mt("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},$t=g?function(t,e,n){return qt.f(t,e,P(1,n))}:function(t,e,n){return t[e]=n,t},Ht=A(Function.toString);L(at.inspectSource)||(at.inspectSource=function(t){return Ht(t)});var Ut,Vt,Xt,Kt=at.inspectSource,Qt=h.WeakMap,Yt=L(Qt)&&/native code/.test(Kt(Qt)),Jt=ft("keys"),Zt=function(t){return Jt[t]||(Jt[t]=vt(t))},te={},ee="Object already initialized",ne=h.TypeError,re=h.WeakMap;if(Yt||at.state){var oe=at.state||(at.state=new re),ie=A(oe.get),ue=A(oe.has),ce=A(oe.set);Ut=function(t,e){if(ue(oe,t))throw new ne(ee);return e.facade=t,ce(oe,t,e),e},Vt=function(t){return ie(oe,t)||{}},Xt=function(t){return ue(oe,t)}}else{var ae=Zt("state");te[ae]=!0,Ut=function(t,e){if(yt(t,ae))throw new ne(ee);return e.facade=t,$t(t,ae,e),e},Vt=function(t){return yt(t,ae)?t[ae]:{}},Xt=function(t){return yt(t,ae)}}var fe={set:Ut,get:Vt,has:Xt,enforce:function(t){return Xt(t)?Vt(t):Ut(t,{})},getterFor:function(t){return function(e){var n;if(!W(e)||(n=Vt(e)).type!==t)throw ne("Incompatible receiver, "+t+" required");return n}}},le=Function.prototype,se=g&&Object.getOwnPropertyDescriptor,pe=yt(le,"name"),ye={EXISTS:pe,PROPER:pe&&"something"===function(){}.name,CONFIGURABLE:pe&&(!g||g&&se(le,"name").configurable)},de=p((function(t){var e=ye.CONFIGURABLE,n=fe.get,r=fe.enforce,o=String(String).split("String");(t.exports=function(t,n,i,u){var c,a=!!u&&!!u.unsafe,f=!!u&&!!u.enumerable,l=!!u&&!!u.noTargetGet,s=u&&void 0!==u.name?u.name:n;L(i)&&("Symbol("===String(s).slice(0,7)&&(s="["+String(s).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!yt(i,"name")||e&&i.name!==s)&&$t(i,"name",s),(c=r(i)).source||(c.source=o.join("string"==typeof s?s:""))),t!==h?(a?!l&&t[n]&&(f=!0):delete t[n],f?t[n]=i:$t(t,n,i)):f?t[n]=i:ut(n,i)})(Function.prototype,"toString",(function(){return L(this)&&n(this).source||Kt(this)}))})),be=Math.ceil,he=Math.floor,ve=function(t){var e=+t;return e!=e||0===e?0:(e>0?he:be)(e)},ge=Math.max,me=Math.min,we=Math.min,Oe=function(t){return(e=t.length)>0?we(ve(e),9007199254740991):0;var e},je=function(t){return function(e,n,r){var o,i=N(e),u=Oe(i),c=function(t,e){var n=ve(t);return n<0?ge(n+e,0):me(n,e)}(r,u);if(t&&n!=n){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===n)return t||c||0;return!t&&-1}},Se={includes:je(!0),indexOf:je(!1)}.indexOf,Te=A([].push),Pe=function(t,e){var n,r=N(t),o=0,i=[];for(n in r)!yt(te,n)&&yt(r,n)&&Te(i,n);for(;e.length>o;)yt(r,n=e[o++])&&(~Se(i,n)||Te(i,n));return i},Ce=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Ee=Ce.concat("length","prototype"),Re={f:Object.getOwnPropertyNames||function(t){return Pe(t,Ee)}},xe={f:Object.getOwnPropertySymbols},Ae=A([].concat),Fe=q("Reflect","ownKeys")||function(t){var e=Re.f(Dt(t)),n=xe.f;return n?Ae(e,n(t)):e},_e=function(t,e,n){for(var r=Fe(e),o=qt.f,i=_t.f,u=0;u<r.length;u++){var c=r[u];yt(t,c)||n&&yt(n,c)||o(t,c,i(e,c))}},ke=/#|\.prototype\./,Be=function(t,e){var n=De[Ie(t)];return n==ze||n!=Me&&(L(e)?v(e):!!e)},Ie=Be.normalize=function(t){return String(t).replace(ke,".").toLowerCase()},De=Be.data={},Me=Be.NATIVE="N",ze=Be.POLYFILL="P",Ne=Be,Le=_t.f,We=function(t,e){var n,r,o,i,u,c=t.target,a=t.global,f=t.stat;if(n=a?h:f?h[c]||ut(c,{}):(h[c]||{}).prototype)for(r in e){if(i=e[r],o=t.noTargetGet?(u=Le(n,r))&&u.value:n[r],!Ne(a?r:c+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;_e(i,o)}(t.sham||o&&o.sham)&&$t(i,"sham",!0),de(n,r,i,t)}},Ge=Object.keys||function(t){return Pe(t,Ce)},qe=Object.assign,$e=Object.defineProperty,He=A([].concat),Ue=!qe||v((function(){if(g&&1!==qe({b:1},qe($e({},"a",{enumerable:!0,get:function(){$e(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},n=Symbol(),r="abcdefghijklmnopqrst";return t[n]=7,r.split("").forEach((function(t){e[t]=t})),7!=qe({},t)[n]||Ge(qe({},e)).join("")!=r}))?function(t,e){for(var n=st(t),r=arguments.length,o=1,i=xe.f,u=T.f;r>o;)for(var c,a=D(arguments[o++]),f=i?He(Ge(a),i(a)):Ge(a),l=f.length,s=0;l>s;)c=f[s++],g&&!O(u,a,c)||(n[c]=a[c]);return n}:qe;We({target:"Object",stat:!0,forced:Object.assign!==Ue},{assign:Ue});var Ve=Array.isArray||function(t){return"Array"==k(t)},Xe=function(t,e,n){var r=Ct(e);r in t?qt.f(t,r,P(0,n)):t[r]=n},Ke={};Ke[jt("toStringTag")]="z";var Qe="[object z]"===String(Ke),Ye=jt("toStringTag"),Je=h.Object,Ze="Arguments"==k(function(){return arguments}()),tn=Qe?k:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=Je(t),Ye))?n:Ze?k(e):"Object"==(r=k(e))&&L(e.callee)?"Arguments":r},en=function(){},nn=[],rn=q("Reflect","construct"),on=/^\s*(?:class|function)\b/,un=A(on.exec),cn=!on.exec(en),an=function(t){if(!L(t))return!1;try{return rn(en,nn,t),!0}catch(t){return!1}},fn=function(t){if(!L(t))return!1;switch(tn(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return cn||!!un(on,Kt(t))}catch(t){return!0}};fn.sham=!0;var ln,sn=!rn||v((function(){var t;return an(an.call)||!an(Object)||!an((function(){t=!0}))||t}))?fn:an,pn=jt("species"),yn=h.Array,dn=function(t,e){return new(function(t){var e;return Ve(t)&&(e=t.constructor,(sn(e)&&(e===yn||Ve(e.prototype))||W(e)&&null===(e=e[pn]))&&(e=void 0)),void 0===e?yn:e}(t))(0===e?0:e)},bn=jt("species"),hn=jt("isConcatSpreadable"),vn=9007199254740991,gn="Maximum allowed index exceeded",mn=h.TypeError,wn=Q>=51||!v((function(){var t=[];return t[hn]=!1,t.concat()[0]!==t})),On=(ln="concat",Q>=51||!v((function(){var t=[];return(t.constructor={})[bn]=function(){return{foo:1}},1!==t[ln](Boolean).foo}))),jn=function(t){if(!W(t))return!1;var e=t[hn];return void 0!==e?!!e:Ve(t)};We({target:"Array",proto:!0,forced:!wn||!On},{concat:function(t){var e,n,r,o,i,u=st(this),c=dn(u,0),a=0;for(e=-1,r=arguments.length;e<r;e++)if(jn(i=-1===e?u:arguments[e])){if(a+(o=Oe(i))>vn)throw mn(gn);for(n=0;n<o;n++,a++)n in i&&Xe(c,a,i[n])}else{if(a>=vn)throw mn(gn);Xe(c,a++,i)}return c.length=a,c}});var Sn,Tn=A(A.bind),Pn=A([].push),Cn=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,u=7==t,c=5==t||i;return function(a,f,l,s){for(var p,y,d=st(a),b=D(d),h=function(t,e){return rt(t),void 0===e?t:m?Tn(t,e):function(){return t.apply(e,arguments)}}(f,l),v=Oe(b),g=0,w=s||dn,O=e?w(a,v):n||u?w(a,0):void 0;v>g;g++)if((c||g in b)&&(y=h(p=b[g],g,d),t))if(e)O[g]=y;else if(y)switch(t){case 3:return!0;case 5:return p;case 6:return g;case 2:Pn(O,p)}else switch(t){case 4:return!1;case 7:Pn(O,p)}return i?-1:r||o?o:O}},En={forEach:Cn(0),map:Cn(1),filter:Cn(2),some:Cn(3),every:Cn(4),find:Cn(5),findIndex:Cn(6),filterReject:Cn(7)},Rn=g&&!kt?Object.defineProperties:function(t,e){Dt(t);for(var n,r=N(e),o=Ge(e),i=o.length,u=0;i>u;)qt.f(t,n=o[u++],r[n]);return t},xn={f:Rn},An=q("document","documentElement"),Fn=Zt("IE_PROTO"),_n=function(){},kn=function(t){return"<script>"+t+"</"+"script>"},Bn=function(t){t.write(kn("")),t.close();var e=t.parentWindow.Object;return t=null,e},In=function(){try{Sn=new ActiveXObject("htmlfile")}catch(t){}var t,e;In="undefined"!=typeof document?document.domain&&Sn?Bn(Sn):((e=xt("iframe")).style.display="none",An.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(kn("document.F=Object")),t.close(),t.F):Bn(Sn);for(var n=Ce.length;n--;)delete In.prototype[Ce[n]];return In()};te[Fn]=!0;var Dn=Object.create||function(t,e){var n;return null!==t?(_n.prototype=Dt(t),n=new _n,_n.prototype=null,n[Fn]=t):n=In(),void 0===e?n:xn.f(n,e)},Mn=jt("unscopables"),zn=Array.prototype;null==zn[Mn]&&qt.f(zn,Mn,{configurable:!0,value:Dn(null)});var Nn,Ln=En.find,Wn="find",Gn=!0;Wn in[]&&Array(1).find((function(){Gn=!1})),We({target:"Array",proto:!0,forced:Gn},{find:function(t){return Ln(this,t,arguments.length>1?arguments[1]:void 0)}}),Nn=Wn,zn[Mn][Nn]=!0;var qn=Qe?{}.toString:function(){return"[object "+tn(this)+"]"};Qe||de(Object.prototype,"toString",qn,{unsafe:!0});var $n=A([].join),Hn=D!=Object,Un=function(t,e){var n=[][t];return!!n&&v((function(){n.call(null,e||function(){return 1},1)}))}("join",",");We({target:"Array",proto:!0,forced:Hn||!Un},{join:function(t){return $n(N(this),void 0===t?",":t)}});var Vn=n.default.fn.bootstrapTable.utils;n.default.extend(n.default.fn.bootstrapTable.locales,{formatCopyRows:function(){return"Copy Rows"}}),n.default.extend(n.default.fn.bootstrapTable.defaults,n.default.fn.bootstrapTable.locales),n.default.extend(n.default.fn.bootstrapTable.defaults.icons,{copy:{bootstrap3:"glyphicon-copy icon-pencil",bootstrap5:"bi-clipboard",materialize:"content_copy","bootstrap-table":"icon-copy"}[n.default.fn.bootstrapTable.theme]||"fa-copy"});n.default.extend(n.default.fn.bootstrapTable.defaults,{showCopyRows:!1,copyWithHidden:!1,copyDelimiter:", ",copyNewline:"\n"}),n.default.extend(n.default.fn.bootstrapTable.columnDefaults,{ignoreCopy:!1,rawCopy:!1}),n.default.fn.bootstrapTable.methods.push("copyColumnsToClipboard"),n.default.BootstrapTable=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&u(t,e)}(p,t);var e,c,f,s=a(p);function p(){return r(this,p),s.apply(this,arguments)}return e=p,c=[{key:"initToolbar",value:function(){var t;this.options.showCopyRows&&this.header.stateField&&(this.buttons=Object.assign(this.buttons,{copyRows:{text:this.options.formatCopyRows(),icon:this.options.icons.copy,event:this.copyColumnsToClipboard,attributes:{"aria-label":this.options.formatCopyRows(),title:this.options.formatCopyRows()}}}));for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];(t=l(i(p.prototype),"initToolbar",this)).call.apply(t,[this].concat(n)),this.$copyButton=this.$toolbar.find('>.columns [name="copyRows"]'),this.options.showCopyRows&&this.header.stateField&&this.updateCopyButton()}},{key:"copyColumnsToClipboard",value:function(){var t=this,e=[];n.default.each(this.getSelections(),(function(r,o){var i=[];n.default.each(t.options.columns[0],(function(e,n){if(n.field!==t.header.stateField&&(!t.options.copyWithHidden||t.options.copyWithHidden&&n.visible)&&!n.ignoreCopy&&null!==o[n.field]){var u=n.rawCopy?o[n.field]:Vn.calculateObjectValue(n,t.header.formatters[e],[o[n.field],o,r],o[n.field]);i.push(u)}})),e.push(i.join(t.options.copyDelimiter))})),function(t){var e=document.createElement("textarea");n.default(e).html(t),document.body.appendChild(e),e.select();try{document.execCommand("copy")}catch(t){console.warn("Oops, unable to copy")}n.default(e).remove()}(e.join(this.options.copyNewline))}},{key:"updateSelected",value:function(){l(i(p.prototype),"updateSelected",this).call(this),this.updateCopyButton()}},{key:"updateCopyButton",value:function(){this.options.showCopyRows&&this.header.stateField&&this.$copyButton&&this.$copyButton.prop("disabled",!this.getSelections().length)}}],c&&o(e.prototype,c),f&&o(e,f),Object.defineProperty(e,"prototype",{writable:!1}),p}(n.default.BootstrapTable)}));
