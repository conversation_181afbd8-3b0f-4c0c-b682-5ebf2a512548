/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.20.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var n=e(t);function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,e){return u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},u(t,e)}function c(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function a(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=i(t);if(e){var o=i(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return c(this,n)}}function f(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=i(t)););return t}function s(){return s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var r=f(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},s.apply(this,arguments)}var l="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function p(t,e){return t(e={exports:{}},e.exports),e.exports}var y,b,d=function(t){return t&&t.Math==Math&&t},h=d("object"==typeof globalThis&&globalThis)||d("object"==typeof window&&window)||d("object"==typeof self&&self)||d("object"==typeof l&&l)||function(){return this}()||Function("return this")(),v=function(t){try{return!!t()}catch(t){return!0}},g=!v((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),m=!v((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),w=Function.prototype.call,O=m?w.bind(w):function(){return w.apply(w,arguments)},j={}.propertyIsEnumerable,S=Object.getOwnPropertyDescriptor,T={f:S&&!j.call({1:2},1)?function(t){var e=S(this,t);return!!e&&e.enumerable}:j},P=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},C=Function.prototype,E=C.bind,V=C.call,x=m&&E.bind(V,V),A=m?function(t){return t&&x(t)}:function(t){return t&&function(){return V.apply(t,arguments)}},B=A({}.toString),R=A("".slice),_=function(t){return R(B(t),8,-1)},F=h.Object,k=A("".split),I=v((function(){return!F("z").propertyIsEnumerable(0)}))?function(t){return"String"==_(t)?k(t,""):F(t)}:F,M=h.TypeError,z=function(t){if(null==t)throw M("Can't call method on "+t);return t},D=function(t){return I(z(t))},L=function(t){return"function"==typeof t},N=function(t){return"object"==typeof t?null!==t:L(t)},G=function(t){return L(t)?t:void 0},q=function(t,e){return arguments.length<2?G(h[t]):h[t]&&h[t][e]},W=A({}.isPrototypeOf),$=q("navigator","userAgent")||"",U=h.process,X=h.Deno,K=U&&U.versions||X&&X.version,Q=K&&K.v8;Q&&(b=(y=Q.split("."))[0]>0&&y[0]<4?1:+(y[0]+y[1])),!b&&$&&(!(y=$.match(/Edge\/(\d+)/))||y[1]>=74)&&(y=$.match(/Chrome\/(\d+)/))&&(b=+y[1]);var Y=b,H=!!Object.getOwnPropertySymbols&&!v((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&Y&&Y<41})),J=H&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Z=h.Object,tt=J?function(t){return"symbol"==typeof t}:function(t){var e=q("Symbol");return L(e)&&W(e.prototype,Z(t))},et=h.String,nt=h.TypeError,rt=function(t){if(L(t))return t;throw nt(function(t){try{return et(t)}catch(t){return"Object"}}(t)+" is not a function")},ot=h.TypeError,it=Object.defineProperty,ut=function(t,e){try{it(h,t,{value:e,configurable:!0,writable:!0})}catch(n){h[t]=e}return e},ct="__core-js_shared__",at=h[ct]||ut(ct,{}),ft=p((function(t){(t.exports=function(t,e){return at[t]||(at[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.21.1",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})})),st=h.Object,lt=function(t){return st(z(t))},pt=A({}.hasOwnProperty),yt=Object.hasOwn||function(t,e){return pt(lt(t),e)},bt=0,dt=Math.random(),ht=A(1..toString),vt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+ht(++bt+dt,36)},gt=ft("wks"),mt=h.Symbol,wt=mt&&mt.for,Ot=J?mt:mt&&mt.withoutSetter||vt,jt=function(t){if(!yt(gt,t)||!H&&"string"!=typeof gt[t]){var e="Symbol."+t;H&&yt(mt,t)?gt[t]=mt[t]:gt[t]=J&&wt?wt(e):Ot(e)}return gt[t]},St=h.TypeError,Tt=jt("toPrimitive"),Pt=function(t,e){if(!N(t)||tt(t))return t;var n,r,o=null==(n=t[Tt])?void 0:rt(n);if(o){if(void 0===e&&(e="default"),r=O(o,t,e),!N(r)||tt(r))return r;throw St("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var n,r;if("string"===e&&L(n=t.toString)&&!N(r=O(n,t)))return r;if(L(n=t.valueOf)&&!N(r=O(n,t)))return r;if("string"!==e&&L(n=t.toString)&&!N(r=O(n,t)))return r;throw ot("Can't convert object to primitive value")}(t,e)},Ct=function(t){var e=Pt(t,"string");return tt(e)?e:e+""},Et=h.document,Vt=N(Et)&&N(Et.createElement),xt=function(t){return Vt?Et.createElement(t):{}},At=!g&&!v((function(){return 7!=Object.defineProperty(xt("div"),"a",{get:function(){return 7}}).a})),Bt=Object.getOwnPropertyDescriptor,Rt={f:g?Bt:function(t,e){if(t=D(t),e=Ct(e),At)try{return Bt(t,e)}catch(t){}if(yt(t,e))return P(!O(T.f,t,e),t[e])}},_t=g&&v((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Ft=h.String,kt=h.TypeError,It=function(t){if(N(t))return t;throw kt(Ft(t)+" is not an object")},Mt=h.TypeError,zt=Object.defineProperty,Dt=Object.getOwnPropertyDescriptor,Lt="enumerable",Nt="configurable",Gt="writable",qt={f:g?_t?function(t,e,n){if(It(t),e=Ct(e),It(n),"function"==typeof t&&"prototype"===e&&"value"in n&&Gt in n&&!n.writable){var r=Dt(t,e);r&&r.writable&&(t[e]=n.value,n={configurable:Nt in n?n.configurable:r.configurable,enumerable:Lt in n?n.enumerable:r.enumerable,writable:!1})}return zt(t,e,n)}:zt:function(t,e,n){if(It(t),e=Ct(e),It(n),At)try{return zt(t,e,n)}catch(t){}if("get"in n||"set"in n)throw Mt("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},Wt=g?function(t,e,n){return qt.f(t,e,P(1,n))}:function(t,e,n){return t[e]=n,t},$t=A(Function.toString);L(at.inspectSource)||(at.inspectSource=function(t){return $t(t)});var Ut,Xt,Kt,Qt=at.inspectSource,Yt=h.WeakMap,Ht=L(Yt)&&/native code/.test(Qt(Yt)),Jt=ft("keys"),Zt=function(t){return Jt[t]||(Jt[t]=vt(t))},te={},ee="Object already initialized",ne=h.TypeError,re=h.WeakMap;if(Ht||at.state){var oe=at.state||(at.state=new re),ie=A(oe.get),ue=A(oe.has),ce=A(oe.set);Ut=function(t,e){if(ue(oe,t))throw new ne(ee);return e.facade=t,ce(oe,t,e),e},Xt=function(t){return ie(oe,t)||{}},Kt=function(t){return ue(oe,t)}}else{var ae=Zt("state");te[ae]=!0,Ut=function(t,e){if(yt(t,ae))throw new ne(ee);return e.facade=t,Wt(t,ae,e),e},Xt=function(t){return yt(t,ae)?t[ae]:{}},Kt=function(t){return yt(t,ae)}}var fe={set:Ut,get:Xt,has:Kt,enforce:function(t){return Kt(t)?Xt(t):Ut(t,{})},getterFor:function(t){return function(e){var n;if(!N(e)||(n=Xt(e)).type!==t)throw ne("Incompatible receiver, "+t+" required");return n}}},se=Function.prototype,le=g&&Object.getOwnPropertyDescriptor,pe=yt(se,"name"),ye={EXISTS:pe,PROPER:pe&&"something"===function(){}.name,CONFIGURABLE:pe&&(!g||g&&le(se,"name").configurable)},be=p((function(t){var e=ye.CONFIGURABLE,n=fe.get,r=fe.enforce,o=String(String).split("String");(t.exports=function(t,n,i,u){var c,a=!!u&&!!u.unsafe,f=!!u&&!!u.enumerable,s=!!u&&!!u.noTargetGet,l=u&&void 0!==u.name?u.name:n;L(i)&&("Symbol("===String(l).slice(0,7)&&(l="["+String(l).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!yt(i,"name")||e&&i.name!==l)&&Wt(i,"name",l),(c=r(i)).source||(c.source=o.join("string"==typeof l?l:""))),t!==h?(a?!s&&t[n]&&(f=!0):delete t[n],f?t[n]=i:Wt(t,n,i)):f?t[n]=i:ut(n,i)})(Function.prototype,"toString",(function(){return L(this)&&n(this).source||Qt(this)}))})),de=Math.ceil,he=Math.floor,ve=function(t){var e=+t;return e!=e||0===e?0:(e>0?he:de)(e)},ge=Math.max,me=Math.min,we=function(t,e){var n=ve(t);return n<0?ge(n+e,0):me(n,e)},Oe=Math.min,je=function(t){return(e=t.length)>0?Oe(ve(e),9007199254740991):0;var e},Se=function(t){return function(e,n,r){var o,i=D(e),u=je(i),c=we(r,u);if(t&&n!=n){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===n)return t||c||0;return!t&&-1}},Te={includes:Se(!0),indexOf:Se(!1)}.indexOf,Pe=A([].push),Ce=function(t,e){var n,r=D(t),o=0,i=[];for(n in r)!yt(te,n)&&yt(r,n)&&Pe(i,n);for(;e.length>o;)yt(r,n=e[o++])&&(~Te(i,n)||Pe(i,n));return i},Ee=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Ve=Ee.concat("length","prototype"),xe={f:Object.getOwnPropertyNames||function(t){return Ce(t,Ve)}},Ae={f:Object.getOwnPropertySymbols},Be=A([].concat),Re=q("Reflect","ownKeys")||function(t){var e=xe.f(It(t)),n=Ae.f;return n?Be(e,n(t)):e},_e=function(t,e,n){for(var r=Re(e),o=qt.f,i=Rt.f,u=0;u<r.length;u++){var c=r[u];yt(t,c)||n&&yt(n,c)||o(t,c,i(e,c))}},Fe=/#|\.prototype\./,ke=function(t,e){var n=Me[Ie(t)];return n==De||n!=ze&&(L(e)?v(e):!!e)},Ie=ke.normalize=function(t){return String(t).replace(Fe,".").toLowerCase()},Me=ke.data={},ze=ke.NATIVE="N",De=ke.POLYFILL="P",Le=ke,Ne=Rt.f,Ge=function(t,e){var n,r,o,i,u,c=t.target,a=t.global,f=t.stat;if(n=a?h:f?h[c]||ut(c,{}):(h[c]||{}).prototype)for(r in e){if(i=e[r],o=t.noTargetGet?(u=Ne(n,r))&&u.value:n[r],!Le(a?r:c+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;_e(i,o)}(t.sham||o&&o.sham)&&Wt(i,"sham",!0),be(n,r,i,t)}},qe=Object.keys||function(t){return Ce(t,Ee)},We=Object.assign,$e=Object.defineProperty,Ue=A([].concat),Xe=!We||v((function(){if(g&&1!==We({b:1},We($e({},"a",{enumerable:!0,get:function(){$e(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},n=Symbol(),r="abcdefghijklmnopqrst";return t[n]=7,r.split("").forEach((function(t){e[t]=t})),7!=We({},t)[n]||qe(We({},e)).join("")!=r}))?function(t,e){for(var n=lt(t),r=arguments.length,o=1,i=Ae.f,u=T.f;r>o;)for(var c,a=I(arguments[o++]),f=i?Ue(qe(a),i(a)):qe(a),s=f.length,l=0;s>l;)c=f[l++],g&&!O(u,a,c)||(n[c]=a[c]);return n}:We;Ge({target:"Object",stat:!0,forced:Object.assign!==Xe},{assign:Xe});var Ke=Array.isArray||function(t){return"Array"==_(t)},Qe=function(t,e,n){var r=Ct(e);r in t?qt.f(t,r,P(0,n)):t[r]=n},Ye={};Ye[jt("toStringTag")]="z";var He="[object z]"===String(Ye),Je=jt("toStringTag"),Ze=h.Object,tn="Arguments"==_(function(){return arguments}()),en=He?_:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=Ze(t),Je))?n:tn?_(e):"Object"==(r=_(e))&&L(e.callee)?"Arguments":r},nn=function(){},rn=[],on=q("Reflect","construct"),un=/^\s*(?:class|function)\b/,cn=A(un.exec),an=!un.exec(nn),fn=function(t){if(!L(t))return!1;try{return on(nn,rn,t),!0}catch(t){return!1}},sn=function(t){if(!L(t))return!1;switch(en(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return an||!!cn(un,Qt(t))}catch(t){return!0}};sn.sham=!0;var ln=!on||v((function(){var t;return fn(fn.call)||!fn(Object)||!fn((function(){t=!0}))||t}))?sn:fn,pn=jt("species"),yn=h.Array,bn=function(t,e){return new(function(t){var e;return Ke(t)&&(e=t.constructor,(ln(e)&&(e===yn||Ke(e.prototype))||N(e)&&null===(e=e[pn]))&&(e=void 0)),void 0===e?yn:e}(t))(0===e?0:e)},dn=jt("species"),hn=function(t){return Y>=51||!v((function(){var e=[];return(e.constructor={})[dn]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},vn=jt("isConcatSpreadable"),gn=9007199254740991,mn="Maximum allowed index exceeded",wn=h.TypeError,On=Y>=51||!v((function(){var t=[];return t[vn]=!1,t.concat()[0]!==t})),jn=hn("concat"),Sn=function(t){if(!N(t))return!1;var e=t[vn];return void 0!==e?!!e:Ke(t)};Ge({target:"Array",proto:!0,forced:!On||!jn},{concat:function(t){var e,n,r,o,i,u=lt(this),c=bn(u,0),a=0;for(e=-1,r=arguments.length;e<r;e++)if(Sn(i=-1===e?u:arguments[e])){if(a+(o=je(i))>gn)throw wn(mn);for(n=0;n<o;n++,a++)n in i&&Qe(c,a,i[n])}else{if(a>=gn)throw wn(mn);Qe(c,a++,i)}return c.length=a,c}});var Tn,Pn=A(A.bind),Cn=A([].push),En=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,u=7==t,c=5==t||i;return function(a,f,s,l){for(var p,y,b=lt(a),d=I(b),h=function(t,e){return rt(t),void 0===e?t:m?Pn(t,e):function(){return t.apply(e,arguments)}}(f,s),v=je(d),g=0,w=l||bn,O=e?w(a,v):n||u?w(a,0):void 0;v>g;g++)if((c||g in d)&&(y=h(p=d[g],g,b),t))if(e)O[g]=y;else if(y)switch(t){case 3:return!0;case 5:return p;case 6:return g;case 2:Cn(O,p)}else switch(t){case 4:return!1;case 7:Cn(O,p)}return i?-1:r||o?o:O}},Vn={forEach:En(0),map:En(1),filter:En(2),some:En(3),every:En(4),find:En(5),findIndex:En(6),filterReject:En(7)},xn=g&&!_t?Object.defineProperties:function(t,e){It(t);for(var n,r=D(e),o=qe(e),i=o.length,u=0;i>u;)qt.f(t,n=o[u++],r[n]);return t},An={f:xn},Bn=q("document","documentElement"),Rn=Zt("IE_PROTO"),_n=function(){},Fn=function(t){return"<script>"+t+"</"+"script>"},kn=function(t){t.write(Fn("")),t.close();var e=t.parentWindow.Object;return t=null,e},In=function(){try{Tn=new ActiveXObject("htmlfile")}catch(t){}var t,e;In="undefined"!=typeof document?document.domain&&Tn?kn(Tn):((e=xt("iframe")).style.display="none",Bn.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(Fn("document.F=Object")),t.close(),t.F):kn(Tn);for(var n=Ee.length;n--;)delete In.prototype[Ee[n]];return In()};te[Rn]=!0;var Mn=Object.create||function(t,e){var n;return null!==t?(_n.prototype=It(t),n=new _n,_n.prototype=null,n[Rn]=t):n=In(),void 0===e?n:An.f(n,e)},zn=jt("unscopables"),Dn=Array.prototype;null==Dn[zn]&&qt.f(Dn,zn,{configurable:!0,value:Mn(null)});var Ln,Nn=Vn.find,Gn="find",qn=!0;Gn in[]&&Array(1).find((function(){qn=!1})),Ge({target:"Array",proto:!0,forced:qn},{find:function(t){return Nn(this,t,arguments.length>1?arguments[1]:void 0)}}),Ln=Gn,Dn[zn][Ln]=!0;var Wn=He?{}.toString:function(){return"[object "+en(this)+"]"};He||be(Object.prototype,"toString",Wn,{unsafe:!0});var $n=A([].slice),Un=hn("slice"),Xn=jt("species"),Kn=h.Array,Qn=Math.max;Ge({target:"Array",proto:!0,forced:!Un},{slice:function(t,e){var n,r,o,i=D(this),u=je(i),c=we(t,u),a=we(void 0===e?u:e,u);if(Ke(i)&&(n=i.constructor,(ln(n)&&(n===Kn||Ke(n.prototype))||N(n)&&null===(n=n[Xn]))&&(n=void 0),n===Kn||void 0===n))return $n(i,c,a);for(r=new(void 0===n?Kn:n)(Qn(a-c,0)),o=0;c<a;c++,o++)c in i&&Qe(r,o,i[c]);return r.length=o,r}});var Yn=n.default.fn.bootstrapTable.utils;n.default.extend(n.default.fn.bootstrapTable.defaults,{customView:!1,showCustomView:!1,showCustomViewButton:!1}),n.default.extend(n.default.fn.bootstrapTable.defaults.icons,{customView:{bootstrap3:"glyphicon glyphicon-eye-open",bootstrap5:"bi-eye",bootstrap4:"fa fa-eye",semantic:"fa fa-eye",foundation:"fa fa-eye",bulma:"fa fa-eye",materialize:"remove_red_eye"}[n.default.fn.bootstrapTable.theme]||"fa-eye"}),n.default.extend(n.default.fn.bootstrapTable.defaults,{onCustomViewPostBody:function(){return!1},onCustomViewPreBody:function(){return!1}}),n.default.extend(n.default.fn.bootstrapTable.locales,{formatToggleCustomView:function(){return"Toggle custom view"}}),n.default.extend(n.default.fn.bootstrapTable.defaults,n.default.fn.bootstrapTable.locales),n.default.fn.bootstrapTable.methods.push("toggleCustomView"),n.default.extend(n.default.fn.bootstrapTable.Constructor.EVENTS,{"custom-view-post-body.bs.table":"onCustomViewPostBody","custom-view-pre-body.bs.table":"onCustomViewPreBody"}),n.default.BootstrapTable=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&u(t,e)}(l,t);var e,n,c,f=a(l);function l(){return r(this,l),f.apply(this,arguments)}return e=l,n=[{key:"init",value:function(){this.showCustomView=this.options.showCustomView,s(i(l.prototype),"init",this).call(this)}},{key:"initToolbar",value:function(){var t;this.options.customView&&this.options.showCustomViewButton&&(this.buttons=Object.assign(this.buttons,{customView:{text:this.options.formatToggleCustomView(),icon:this.options.icons.customView,event:this.toggleCustomView,attributes:{"aria-label":this.options.formatToggleCustomView(),title:this.options.formatToggleCustomView()}}}));for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];(t=s(i(l.prototype),"initToolbar",this)).call.apply(t,[this].concat(n))}},{key:"initBody",value:function(){if(s(i(l.prototype),"initBody",this).call(this),this.options.customView){var t=this.$el,e=this.$container.find(".fixed-table-custom-view");if(t.hide(),e.hide(),this.options.customView&&this.showCustomView){var n=this.getData().slice(this.pageFrom-1,this.pageTo),r=Yn.calculateObjectValue(this,this.options.customView,[n],"");this.trigger("custom-view-pre-body",n,r),1===e.length?e.show().html(r):this.$tableBody.after('<div class="fixed-table-custom-view">'.concat(r,"</div>")),this.trigger("custom-view-post-body",n,r)}else t.show()}}},{key:"toggleCustomView",value:function(){this.showCustomView=!this.showCustomView,this.initBody()}}],n&&o(e.prototype,n),c&&o(e,c),Object.defineProperty(e,"prototype",{writable:!1}),l}(n.default.BootstrapTable)}));
