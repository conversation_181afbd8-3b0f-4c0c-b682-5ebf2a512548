/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.20.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=e(t);function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,e){return u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},u(t,e)}function c(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function f(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=i(t);if(e){var o=i(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return c(this,r)}}function a(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=i(t)););return t}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,r){var n=a(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},l.apply(this,arguments)}var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function p(t,e){return t(e={exports:{}},e.exports),e.exports}var y,b,h=function(t){return t&&t.Math==Math&&t},d=h("object"==typeof globalThis&&globalThis)||h("object"==typeof window&&window)||h("object"==typeof self&&self)||h("object"==typeof s&&s)||function(){return this}()||Function("return this")(),v=function(t){try{return!!t()}catch(t){return!0}},g=!v((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),m=!v((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),w=Function.prototype.call,O=m?w.bind(w):function(){return w.apply(w,arguments)},j={}.propertyIsEnumerable,S=Object.getOwnPropertyDescriptor,P={f:S&&!j.call({1:2},1)?function(t){var e=S(this,t);return!!e&&e.enumerable}:j},T=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},E=Function.prototype,x=E.bind,A=E.call,R=m&&x.bind(A,A),_=m?function(t){return t&&R(t)}:function(t){return t&&function(){return A.apply(t,arguments)}},F=_({}.toString),M=_("".slice),k=function(t){return M(F(t),8,-1)},C=d.Object,I=_("".split),z=v((function(){return!C("z").propertyIsEnumerable(0)}))?function(t){return"String"==k(t)?I(t,""):C(t)}:C,D=d.TypeError,L=function(t){if(null==t)throw D("Can't call method on "+t);return t},B=function(t){return z(L(t))},N=function(t){return"function"==typeof t},G=function(t){return"object"==typeof t?null!==t:N(t)},U=function(t){return N(t)?t:void 0},q=function(t,e){return arguments.length<2?U(d[t]):d[t]&&d[t][e]},W=_({}.isPrototypeOf),K=q("navigator","userAgent")||"",Q=d.process,V=d.Deno,X=Q&&Q.versions||V&&V.version,Y=X&&X.v8;Y&&(b=(y=Y.split("."))[0]>0&&y[0]<4?1:+(y[0]+y[1])),!b&&K&&(!(y=K.match(/Edge\/(\d+)/))||y[1]>=74)&&(y=K.match(/Chrome\/(\d+)/))&&(b=+y[1]);var $=b,H=!!Object.getOwnPropertySymbols&&!v((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&$&&$<41})),J=H&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Z=d.Object,tt=J?function(t){return"symbol"==typeof t}:function(t){var e=q("Symbol");return N(e)&&W(e.prototype,Z(t))},et=d.String,rt=d.TypeError,nt=function(t){if(N(t))return t;throw rt(function(t){try{return et(t)}catch(t){return"Object"}}(t)+" is not a function")},ot=d.TypeError,it=Object.defineProperty,ut=function(t,e){try{it(d,t,{value:e,configurable:!0,writable:!0})}catch(r){d[t]=e}return e},ct="__core-js_shared__",ft=d[ct]||ut(ct,{}),at=p((function(t){(t.exports=function(t,e){return ft[t]||(ft[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.21.1",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})})),lt=d.Object,st=function(t){return lt(L(t))},pt=_({}.hasOwnProperty),yt=Object.hasOwn||function(t,e){return pt(st(t),e)},bt=0,ht=Math.random(),dt=_(1..toString),vt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+dt(++bt+ht,36)},gt=at("wks"),mt=d.Symbol,wt=mt&&mt.for,Ot=J?mt:mt&&mt.withoutSetter||vt,jt=function(t){if(!yt(gt,t)||!H&&"string"!=typeof gt[t]){var e="Symbol."+t;H&&yt(mt,t)?gt[t]=mt[t]:gt[t]=J&&wt?wt(e):Ot(e)}return gt[t]},St=d.TypeError,Pt=jt("toPrimitive"),Tt=function(t,e){if(!G(t)||tt(t))return t;var r,n,o=null==(r=t[Pt])?void 0:nt(r);if(o){if(void 0===e&&(e="default"),n=O(o,t,e),!G(n)||tt(n))return n;throw St("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var r,n;if("string"===e&&N(r=t.toString)&&!G(n=O(r,t)))return n;if(N(r=t.valueOf)&&!G(n=O(r,t)))return n;if("string"!==e&&N(r=t.toString)&&!G(n=O(r,t)))return n;throw ot("Can't convert object to primitive value")}(t,e)},Et=function(t){var e=Tt(t,"string");return tt(e)?e:e+""},xt=d.document,At=G(xt)&&G(xt.createElement),Rt=!g&&!v((function(){return 7!=Object.defineProperty((t="div",At?xt.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),_t=Object.getOwnPropertyDescriptor,Ft={f:g?_t:function(t,e){if(t=B(t),e=Et(e),Rt)try{return _t(t,e)}catch(t){}if(yt(t,e))return T(!O(P.f,t,e),t[e])}},Mt=g&&v((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),kt=d.String,Ct=d.TypeError,It=function(t){if(G(t))return t;throw Ct(kt(t)+" is not an object")},zt=d.TypeError,Dt=Object.defineProperty,Lt=Object.getOwnPropertyDescriptor,Bt="enumerable",Nt="configurable",Gt="writable",Ut={f:g?Mt?function(t,e,r){if(It(t),e=Et(e),It(r),"function"==typeof t&&"prototype"===e&&"value"in r&&Gt in r&&!r.writable){var n=Lt(t,e);n&&n.writable&&(t[e]=r.value,r={configurable:Nt in r?r.configurable:n.configurable,enumerable:Bt in r?r.enumerable:n.enumerable,writable:!1})}return Dt(t,e,r)}:Dt:function(t,e,r){if(It(t),e=Et(e),It(r),Rt)try{return Dt(t,e,r)}catch(t){}if("get"in r||"set"in r)throw zt("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},qt=g?function(t,e,r){return Ut.f(t,e,T(1,r))}:function(t,e,r){return t[e]=r,t},Wt=_(Function.toString);N(ft.inspectSource)||(ft.inspectSource=function(t){return Wt(t)});var Kt,Qt,Vt,Xt,Yt=ft.inspectSource,$t=d.WeakMap,Ht=N($t)&&/native code/.test(Yt($t)),Jt=at("keys"),Zt={},te="Object already initialized",ee=d.TypeError,re=d.WeakMap;if(Ht||ft.state){var ne=ft.state||(ft.state=new re),oe=_(ne.get),ie=_(ne.has),ue=_(ne.set);Kt=function(t,e){if(ie(ne,t))throw new ee(te);return e.facade=t,ue(ne,t,e),e},Qt=function(t){return oe(ne,t)||{}},Vt=function(t){return ie(ne,t)}}else{var ce=Jt[Xt="state"]||(Jt[Xt]=vt(Xt));Zt[ce]=!0,Kt=function(t,e){if(yt(t,ce))throw new ee(te);return e.facade=t,qt(t,ce,e),e},Qt=function(t){return yt(t,ce)?t[ce]:{}},Vt=function(t){return yt(t,ce)}}var fe={set:Kt,get:Qt,has:Vt,enforce:function(t){return Vt(t)?Qt(t):Kt(t,{})},getterFor:function(t){return function(e){var r;if(!G(e)||(r=Qt(e)).type!==t)throw ee("Incompatible receiver, "+t+" required");return r}}},ae=Function.prototype,le=g&&Object.getOwnPropertyDescriptor,se=yt(ae,"name"),pe={EXISTS:se,PROPER:se&&"something"===function(){}.name,CONFIGURABLE:se&&(!g||g&&le(ae,"name").configurable)},ye=p((function(t){var e=pe.CONFIGURABLE,r=fe.get,n=fe.enforce,o=String(String).split("String");(t.exports=function(t,r,i,u){var c,f=!!u&&!!u.unsafe,a=!!u&&!!u.enumerable,l=!!u&&!!u.noTargetGet,s=u&&void 0!==u.name?u.name:r;N(i)&&("Symbol("===String(s).slice(0,7)&&(s="["+String(s).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!yt(i,"name")||e&&i.name!==s)&&qt(i,"name",s),(c=n(i)).source||(c.source=o.join("string"==typeof s?s:""))),t!==d?(f?!l&&t[r]&&(a=!0):delete t[r],a?t[r]=i:qt(t,r,i)):a?t[r]=i:ut(r,i)})(Function.prototype,"toString",(function(){return N(this)&&r(this).source||Yt(this)}))})),be=Math.ceil,he=Math.floor,de=function(t){var e=+t;return e!=e||0===e?0:(e>0?he:be)(e)},ve=Math.max,ge=Math.min,me=Math.min,we=function(t){return(e=t.length)>0?me(de(e),9007199254740991):0;var e},Oe=function(t){return function(e,r,n){var o,i=B(e),u=we(i),c=function(t,e){var r=de(t);return r<0?ve(r+e,0):ge(r,e)}(n,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},je={includes:Oe(!0),indexOf:Oe(!1)}.indexOf,Se=_([].push),Pe=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),Te=Object.getOwnPropertyNames||function(t){return function(t,e){var r,n=B(t),o=0,i=[];for(r in n)!yt(Zt,r)&&yt(n,r)&&Se(i,r);for(;e.length>o;)yt(n,r=e[o++])&&(~je(i,r)||Se(i,r));return i}(t,Pe)},Ee={f:Te},xe={f:Object.getOwnPropertySymbols},Ae=_([].concat),Re=q("Reflect","ownKeys")||function(t){var e=Ee.f(It(t)),r=xe.f;return r?Ae(e,r(t)):e},_e=function(t,e,r){for(var n=Re(e),o=Ut.f,i=Ft.f,u=0;u<n.length;u++){var c=n[u];yt(t,c)||r&&yt(r,c)||o(t,c,i(e,c))}},Fe=/#|\.prototype\./,Me=function(t,e){var r=Ce[ke(t)];return r==ze||r!=Ie&&(N(e)?v(e):!!e)},ke=Me.normalize=function(t){return String(t).replace(Fe,".").toLowerCase()},Ce=Me.data={},Ie=Me.NATIVE="N",ze=Me.POLYFILL="P",De=Me,Le=Ft.f,Be=Array.isArray||function(t){return"Array"==k(t)},Ne=function(t,e,r){var n=Et(e);n in t?Ut.f(t,n,T(0,r)):t[n]=r},Ge={};Ge[jt("toStringTag")]="z";var Ue="[object z]"===String(Ge),qe=jt("toStringTag"),We=d.Object,Ke="Arguments"==k(function(){return arguments}()),Qe=Ue?k:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=We(t),qe))?r:Ke?k(e):"Object"==(n=k(e))&&N(e.callee)?"Arguments":n},Ve=function(){},Xe=[],Ye=q("Reflect","construct"),$e=/^\s*(?:class|function)\b/,He=_($e.exec),Je=!$e.exec(Ve),Ze=function(t){if(!N(t))return!1;try{return Ye(Ve,Xe,t),!0}catch(t){return!1}},tr=function(t){if(!N(t))return!1;switch(Qe(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Je||!!He($e,Yt(t))}catch(t){return!0}};tr.sham=!0;var er,rr=!Ye||v((function(){var t;return Ze(Ze.call)||!Ze(Object)||!Ze((function(){t=!0}))||t}))?tr:Ze,nr=jt("species"),or=d.Array,ir=function(t,e){return new(function(t){var e;return Be(t)&&(e=t.constructor,(rr(e)&&(e===or||Be(e.prototype))||G(e)&&null===(e=e[nr]))&&(e=void 0)),void 0===e?or:e}(t))(0===e?0:e)},ur=jt("species"),cr=jt("isConcatSpreadable"),fr=9007199254740991,ar="Maximum allowed index exceeded",lr=d.TypeError,sr=$>=51||!v((function(){var t=[];return t[cr]=!1,t.concat()[0]!==t})),pr=(er="concat",$>=51||!v((function(){var t=[];return(t.constructor={})[ur]=function(){return{foo:1}},1!==t[er](Boolean).foo}))),yr=function(t){if(!G(t))return!1;var e=t[cr];return void 0!==e?!!e:Be(t)};!function(t,e){var r,n,o,i,u,c=t.target,f=t.global,a=t.stat;if(r=f?d:a?d[c]||ut(c,{}):(d[c]||{}).prototype)for(n in e){if(i=e[n],o=t.noTargetGet?(u=Le(r,n))&&u.value:r[n],!De(f?n:c+(a?".":"#")+n,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;_e(i,o)}(t.sham||o&&o.sham)&&qt(i,"sham",!0),ye(r,n,i,t)}}({target:"Array",proto:!0,forced:!sr||!pr},{concat:function(t){var e,r,n,o,i,u=st(this),c=ir(u,0),f=0;for(e=-1,n=arguments.length;e<n;e++)if(yr(i=-1===e?u:arguments[e])){if(f+(o=we(i))>fr)throw lr(ar);for(r=0;r<o;r++,f++)r in i&&Ne(c,f,i[r])}else{if(f>=fr)throw lr(ar);Ne(c,f++,i)}return c.length=f,c}}),r.default.extend(r.default.fn.bootstrapTable.defaults,{deferUrl:void 0}),r.default.BootstrapTable=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&u(t,e)}(s,t);var e,r,c,a=f(s);function s(){return n(this,s),a.apply(this,arguments)}return e=s,r=[{key:"init",value:function(){for(var t,e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];(t=l(i(s.prototype),"init",this)).call.apply(t,[this].concat(r)),this.options.deferUrl&&(this.options.url=this.options.deferUrl)}}],r&&o(e.prototype,r),c&&o(e,c),Object.defineProperty(e,"prototype",{writable:!1}),s}(r.default.BootstrapTable)}));
