/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.20.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var n=e(t);function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,e){return u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},u(t,e)}function c(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function a(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=i(t);if(e){var o=i(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return c(this,n)}}function f(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=i(t)););return t}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var r=f(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},l.apply(this,arguments)}var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function p(t,e){return t(e={exports:{}},e.exports),e.exports}var y,d,v=function(t){return t&&t.Math==Math&&t},b=v("object"==typeof globalThis&&globalThis)||v("object"==typeof window&&window)||v("object"==typeof self&&self)||v("object"==typeof s&&s)||function(){return this}()||Function("return this")(),g=function(t){try{return!!t()}catch(t){return!0}},h=!g((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),m=!g((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),w=Function.prototype.call,O=m?w.bind(w):function(){return w.apply(w,arguments)},x={}.propertyIsEnumerable,j=Object.getOwnPropertyDescriptor,S={f:j&&!x.call({1:2},1)?function(t){var e=j(this,t);return!!e&&e.enumerable}:x},E=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},P=Function.prototype,I=P.bind,T=P.call,R=m&&I.bind(T,T),A=m?function(t){return t&&R(t)}:function(t){return t&&function(){return T.apply(t,arguments)}},k=A({}.toString),_=A("".slice),C=function(t){return _(k(t),8,-1)},F=b.Object,M=A("".split),D=g((function(){return!F("z").propertyIsEnumerable(0)}))?function(t){return"String"==C(t)?M(t,""):F(t)}:F,N=b.TypeError,B=function(t){if(null==t)throw N("Can't call method on "+t);return t},z=function(t){return D(B(t))},L=function(t){return"function"==typeof t},$=function(t){return"object"==typeof t?null!==t:L(t)},G=function(t){return L(t)?t:void 0},K=function(t,e){return arguments.length<2?G(b[t]):b[t]&&b[t][e]},U=A({}.isPrototypeOf),q=K("navigator","userAgent")||"",W=b.process,Y=b.Deno,X=W&&W.versions||Y&&Y.version,Q=X&&X.v8;Q&&(d=(y=Q.split("."))[0]>0&&y[0]<4?1:+(y[0]+y[1])),!d&&q&&(!(y=q.match(/Edge\/(\d+)/))||y[1]>=74)&&(y=q.match(/Chrome\/(\d+)/))&&(d=+y[1]);var V=d,H=!!Object.getOwnPropertySymbols&&!g((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&V&&V<41})),J=H&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Z=b.Object,tt=J?function(t){return"symbol"==typeof t}:function(t){var e=K("Symbol");return L(e)&&U(e.prototype,Z(t))},et=b.String,nt=b.TypeError,rt=function(t){if(L(t))return t;throw nt(function(t){try{return et(t)}catch(t){return"Object"}}(t)+" is not a function")},ot=function(t,e){var n=t[e];return null==n?void 0:rt(n)},it=b.TypeError,ut=Object.defineProperty,ct=function(t,e){try{ut(b,t,{value:e,configurable:!0,writable:!0})}catch(n){b[t]=e}return e},at="__core-js_shared__",ft=b[at]||ct(at,{}),lt=p((function(t){(t.exports=function(t,e){return ft[t]||(ft[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.21.1",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})})),st=b.Object,pt=function(t){return st(B(t))},yt=A({}.hasOwnProperty),dt=Object.hasOwn||function(t,e){return yt(pt(t),e)},vt=0,bt=Math.random(),gt=A(1..toString),ht=function(t){return"Symbol("+(void 0===t?"":t)+")_"+gt(++vt+bt,36)},mt=lt("wks"),wt=b.Symbol,Ot=wt&&wt.for,xt=J?wt:wt&&wt.withoutSetter||ht,jt=function(t){if(!dt(mt,t)||!H&&"string"!=typeof mt[t]){var e="Symbol."+t;H&&dt(wt,t)?mt[t]=wt[t]:mt[t]=J&&Ot?Ot(e):xt(e)}return mt[t]},St=b.TypeError,Et=jt("toPrimitive"),Pt=function(t,e){if(!$(t)||tt(t))return t;var n,r=ot(t,Et);if(r){if(void 0===e&&(e="default"),n=O(r,t,e),!$(n)||tt(n))return n;throw St("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var n,r;if("string"===e&&L(n=t.toString)&&!$(r=O(n,t)))return r;if(L(n=t.valueOf)&&!$(r=O(n,t)))return r;if("string"!==e&&L(n=t.toString)&&!$(r=O(n,t)))return r;throw it("Can't convert object to primitive value")}(t,e)},It=function(t){var e=Pt(t,"string");return tt(e)?e:e+""},Tt=b.document,Rt=$(Tt)&&$(Tt.createElement),At=function(t){return Rt?Tt.createElement(t):{}},kt=!h&&!g((function(){return 7!=Object.defineProperty(At("div"),"a",{get:function(){return 7}}).a})),_t=Object.getOwnPropertyDescriptor,Ct={f:h?_t:function(t,e){if(t=z(t),e=It(e),kt)try{return _t(t,e)}catch(t){}if(dt(t,e))return E(!O(S.f,t,e),t[e])}},Ft=h&&g((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Mt=b.String,Dt=b.TypeError,Nt=function(t){if($(t))return t;throw Dt(Mt(t)+" is not an object")},Bt=b.TypeError,zt=Object.defineProperty,Lt=Object.getOwnPropertyDescriptor,$t="enumerable",Gt="configurable",Kt="writable",Ut={f:h?Ft?function(t,e,n){if(Nt(t),e=It(e),Nt(n),"function"==typeof t&&"prototype"===e&&"value"in n&&Kt in n&&!n.writable){var r=Lt(t,e);r&&r.writable&&(t[e]=n.value,n={configurable:Gt in n?n.configurable:r.configurable,enumerable:$t in n?n.enumerable:r.enumerable,writable:!1})}return zt(t,e,n)}:zt:function(t,e,n){if(Nt(t),e=It(e),Nt(n),kt)try{return zt(t,e,n)}catch(t){}if("get"in n||"set"in n)throw Bt("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},qt=h?function(t,e,n){return Ut.f(t,e,E(1,n))}:function(t,e,n){return t[e]=n,t},Wt=A(Function.toString);L(ft.inspectSource)||(ft.inspectSource=function(t){return Wt(t)});var Yt,Xt,Qt,Vt=ft.inspectSource,Ht=b.WeakMap,Jt=L(Ht)&&/native code/.test(Vt(Ht)),Zt=lt("keys"),te=function(t){return Zt[t]||(Zt[t]=ht(t))},ee={},ne="Object already initialized",re=b.TypeError,oe=b.WeakMap;if(Jt||ft.state){var ie=ft.state||(ft.state=new oe),ue=A(ie.get),ce=A(ie.has),ae=A(ie.set);Yt=function(t,e){if(ce(ie,t))throw new re(ne);return e.facade=t,ae(ie,t,e),e},Xt=function(t){return ue(ie,t)||{}},Qt=function(t){return ce(ie,t)}}else{var fe=te("state");ee[fe]=!0,Yt=function(t,e){if(dt(t,fe))throw new re(ne);return e.facade=t,qt(t,fe,e),e},Xt=function(t){return dt(t,fe)?t[fe]:{}},Qt=function(t){return dt(t,fe)}}var le={set:Yt,get:Xt,has:Qt,enforce:function(t){return Qt(t)?Xt(t):Yt(t,{})},getterFor:function(t){return function(e){var n;if(!$(e)||(n=Xt(e)).type!==t)throw re("Incompatible receiver, "+t+" required");return n}}},se=Function.prototype,pe=h&&Object.getOwnPropertyDescriptor,ye=dt(se,"name"),de={EXISTS:ye,PROPER:ye&&"something"===function(){}.name,CONFIGURABLE:ye&&(!h||h&&pe(se,"name").configurable)},ve=p((function(t){var e=de.CONFIGURABLE,n=le.get,r=le.enforce,o=String(String).split("String");(t.exports=function(t,n,i,u){var c,a=!!u&&!!u.unsafe,f=!!u&&!!u.enumerable,l=!!u&&!!u.noTargetGet,s=u&&void 0!==u.name?u.name:n;L(i)&&("Symbol("===String(s).slice(0,7)&&(s="["+String(s).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!dt(i,"name")||e&&i.name!==s)&&qt(i,"name",s),(c=r(i)).source||(c.source=o.join("string"==typeof s?s:""))),t!==b?(a?!l&&t[n]&&(f=!0):delete t[n],f?t[n]=i:qt(t,n,i)):f?t[n]=i:ct(n,i)})(Function.prototype,"toString",(function(){return L(this)&&n(this).source||Vt(this)}))})),be=Math.ceil,ge=Math.floor,he=function(t){var e=+t;return e!=e||0===e?0:(e>0?ge:be)(e)},me=Math.max,we=Math.min,Oe=Math.min,xe=function(t){return(e=t.length)>0?Oe(he(e),9007199254740991):0;var e},je=function(t){return function(e,n,r){var o,i=z(e),u=xe(i),c=function(t,e){var n=he(t);return n<0?me(n+e,0):we(n,e)}(r,u);if(t&&n!=n){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===n)return t||c||0;return!t&&-1}},Se={includes:je(!0),indexOf:je(!1)}.indexOf,Ee=A([].push),Pe=function(t,e){var n,r=z(t),o=0,i=[];for(n in r)!dt(ee,n)&&dt(r,n)&&Ee(i,n);for(;e.length>o;)dt(r,n=e[o++])&&(~Se(i,n)||Ee(i,n));return i},Ie=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Te=Ie.concat("length","prototype"),Re={f:Object.getOwnPropertyNames||function(t){return Pe(t,Te)}},Ae={f:Object.getOwnPropertySymbols},ke=A([].concat),_e=K("Reflect","ownKeys")||function(t){var e=Re.f(Nt(t)),n=Ae.f;return n?ke(e,n(t)):e},Ce=function(t,e,n){for(var r=_e(e),o=Ut.f,i=Ct.f,u=0;u<r.length;u++){var c=r[u];dt(t,c)||n&&dt(n,c)||o(t,c,i(e,c))}},Fe=/#|\.prototype\./,Me=function(t,e){var n=Ne[De(t)];return n==ze||n!=Be&&(L(e)?g(e):!!e)},De=Me.normalize=function(t){return String(t).replace(Fe,".").toLowerCase()},Ne=Me.data={},Be=Me.NATIVE="N",ze=Me.POLYFILL="P",Le=Me,$e=Ct.f,Ge=function(t,e){var n,r,o,i,u,c=t.target,a=t.global,f=t.stat;if(n=a?b:f?b[c]||ct(c,{}):(b[c]||{}).prototype)for(r in e){if(i=e[r],o=t.noTargetGet?(u=$e(n,r))&&u.value:n[r],!Le(a?r:c+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ce(i,o)}(t.sham||o&&o.sham)&&qt(i,"sham",!0),ve(n,r,i,t)}},Ke=Array.isArray||function(t){return"Array"==C(t)},Ue=function(t,e,n){var r=It(e);r in t?Ut.f(t,r,E(0,n)):t[r]=n},qe={};qe[jt("toStringTag")]="z";var We="[object z]"===String(qe),Ye=jt("toStringTag"),Xe=b.Object,Qe="Arguments"==C(function(){return arguments}()),Ve=We?C:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=Xe(t),Ye))?n:Qe?C(e):"Object"==(r=C(e))&&L(e.callee)?"Arguments":r},He=function(){},Je=[],Ze=K("Reflect","construct"),tn=/^\s*(?:class|function)\b/,en=A(tn.exec),nn=!tn.exec(He),rn=function(t){if(!L(t))return!1;try{return Ze(He,Je,t),!0}catch(t){return!1}},on=function(t){if(!L(t))return!1;switch(Ve(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return nn||!!en(tn,Vt(t))}catch(t){return!0}};on.sham=!0;var un,cn=!Ze||g((function(){var t;return rn(rn.call)||!rn(Object)||!rn((function(){t=!0}))||t}))?on:rn,an=jt("species"),fn=b.Array,ln=function(t,e){return new(function(t){var e;return Ke(t)&&(e=t.constructor,(cn(e)&&(e===fn||Ke(e.prototype))||$(e)&&null===(e=e[an]))&&(e=void 0)),void 0===e?fn:e}(t))(0===e?0:e)},sn=jt("species"),pn=jt("isConcatSpreadable"),yn=9007199254740991,dn="Maximum allowed index exceeded",vn=b.TypeError,bn=V>=51||!g((function(){var t=[];return t[pn]=!1,t.concat()[0]!==t})),gn=(un="concat",V>=51||!g((function(){var t=[];return(t.constructor={})[sn]=function(){return{foo:1}},1!==t[un](Boolean).foo}))),hn=function(t){if(!$(t))return!1;var e=t[pn];return void 0!==e?!!e:Ke(t)};Ge({target:"Array",proto:!0,forced:!bn||!gn},{concat:function(t){var e,n,r,o,i,u=pt(this),c=ln(u,0),a=0;for(e=-1,r=arguments.length;e<r;e++)if(hn(i=-1===e?u:arguments[e])){if(a+(o=xe(i))>yn)throw vn(dn);for(n=0;n<o;n++,a++)n in i&&Ue(c,a,i[n])}else{if(a>=yn)throw vn(dn);Ue(c,a++,i)}return c.length=a,c}});var mn,wn=A(A.bind),On=A([].push),xn=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,u=7==t,c=5==t||i;return function(a,f,l,s){for(var p,y,d=pt(a),v=D(d),b=function(t,e){return rt(t),void 0===e?t:m?wn(t,e):function(){return t.apply(e,arguments)}}(f,l),g=xe(v),h=0,w=s||ln,O=e?w(a,g):n||u?w(a,0):void 0;g>h;h++)if((c||h in v)&&(y=b(p=v[h],h,d),t))if(e)O[h]=y;else if(y)switch(t){case 3:return!0;case 5:return p;case 6:return h;case 2:On(O,p)}else switch(t){case 4:return!1;case 7:On(O,p)}return i?-1:r||o?o:O}},jn={forEach:xn(0),map:xn(1),filter:xn(2),some:xn(3),every:xn(4),find:xn(5),findIndex:xn(6),filterReject:xn(7)},Sn=Object.keys||function(t){return Pe(t,Ie)},En=h&&!Ft?Object.defineProperties:function(t,e){Nt(t);for(var n,r=z(e),o=Sn(e),i=o.length,u=0;i>u;)Ut.f(t,n=o[u++],r[n]);return t},Pn={f:En},In=K("document","documentElement"),Tn=te("IE_PROTO"),Rn=function(){},An=function(t){return"<script>"+t+"</"+"script>"},kn=function(t){t.write(An("")),t.close();var e=t.parentWindow.Object;return t=null,e},_n=function(){try{mn=new ActiveXObject("htmlfile")}catch(t){}var t,e;_n="undefined"!=typeof document?document.domain&&mn?kn(mn):((e=At("iframe")).style.display="none",In.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(An("document.F=Object")),t.close(),t.F):kn(mn);for(var n=Ie.length;n--;)delete _n.prototype[Ie[n]];return _n()};ee[Tn]=!0;var Cn=Object.create||function(t,e){var n;return null!==t?(Rn.prototype=Nt(t),n=new Rn,Rn.prototype=null,n[Tn]=t):n=_n(),void 0===e?n:Pn.f(n,e)},Fn=jt("unscopables"),Mn=Array.prototype;null==Mn[Fn]&&Ut.f(Mn,Fn,{configurable:!0,value:Cn(null)});var Dn,Nn=jn.find,Bn="find",zn=!0;Bn in[]&&Array(1).find((function(){zn=!1})),Ge({target:"Array",proto:!0,forced:zn},{find:function(t){return Nn(this,t,arguments.length>1?arguments[1]:void 0)}}),Dn=Bn,Mn[Fn][Dn]=!0;var Ln=We?{}.toString:function(){return"[object "+Ve(this)+"]"};We||ve(Object.prototype,"toString",Ln,{unsafe:!0});var $n,Gn,Kn=b.String,Un=function(t){if("Symbol"===Ve(t))throw TypeError("Cannot convert a Symbol value to a string");return Kn(t)},qn=function(){var t=Nt(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e},Wn=b.RegExp,Yn=g((function(){var t=Wn("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),Xn=Yn||g((function(){return!Wn("a","y").sticky})),Qn={BROKEN_CARET:Yn||g((function(){var t=Wn("^r","gy");return t.lastIndex=2,null!=t.exec("str")})),MISSED_STICKY:Xn,UNSUPPORTED_Y:Yn},Vn=b.RegExp,Hn=g((function(){var t=Vn(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)})),Jn=b.RegExp,Zn=g((function(){var t=Jn("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})),tr=le.get,er=lt("native-string-replace",String.prototype.replace),nr=RegExp.prototype.exec,rr=nr,or=A("".charAt),ir=A("".indexOf),ur=A("".replace),cr=A("".slice),ar=(Gn=/b*/g,O(nr,$n=/a/,"a"),O(nr,Gn,"a"),0!==$n.lastIndex||0!==Gn.lastIndex),fr=Qn.BROKEN_CARET,lr=void 0!==/()??/.exec("")[1];(ar||lr||fr||Hn||Zn)&&(rr=function(t){var e,n,r,o,i,u,c,a=this,f=tr(a),l=Un(t),s=f.raw;if(s)return s.lastIndex=a.lastIndex,e=O(rr,s,l),a.lastIndex=s.lastIndex,e;var p=f.groups,y=fr&&a.sticky,d=O(qn,a),v=a.source,b=0,g=l;if(y&&(d=ur(d,"y",""),-1===ir(d,"g")&&(d+="g"),g=cr(l,a.lastIndex),a.lastIndex>0&&(!a.multiline||a.multiline&&"\n"!==or(l,a.lastIndex-1))&&(v="(?: "+v+")",g=" "+g,b++),n=new RegExp("^(?:"+v+")",d)),lr&&(n=new RegExp("^"+v+"$(?!\\s)",d)),ar&&(r=a.lastIndex),o=O(nr,y?n:a,g),y?o?(o.input=cr(o.input,b),o[0]=cr(o[0],b),o.index=a.lastIndex,a.lastIndex+=o[0].length):a.lastIndex=0:ar&&o&&(a.lastIndex=a.global?o.index+o[0].length:r),lr&&o&&o.length>1&&O(er,o[0],n,(function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(o[i]=void 0)})),o&&p)for(o.groups=u=Cn(null),i=0;i<p.length;i++)u[(c=p[i])[0]]=o[c[1]];return o});var sr=rr;Ge({target:"RegExp",proto:!0,forced:/./.exec!==sr},{exec:sr});var pr=jt("species"),yr=RegExp.prototype,dr=Object.is||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e},vr=b.TypeError;!function(t,e,n,r){var o=jt(t),i=!g((function(){var e={};return e[o]=function(){return 7},7!=""[t](e)})),u=i&&!g((function(){var e=!1,n=/a/;return"split"===t&&((n={}).constructor={},n.constructor[pr]=function(){return n},n.flags="",n[o]=/./[o]),n.exec=function(){return e=!0,null},n[o](""),!e}));if(!i||!u||n){var c=A(/./[o]),a=e(o,""[t],(function(t,e,n,r,o){var u=A(t),a=e.exec;return a===sr||a===yr.exec?i&&!o?{done:!0,value:c(e,n,r)}:{done:!0,value:u(n,e,r)}:{done:!1}}));ve(String.prototype,t,a[0]),ve(yr,o,a[1])}r&&qt(yr[o],"sham",!0)}("search",(function(t,e,n){return[function(e){var n=B(this),r=null==e?void 0:ot(e,t);return r?O(r,e,n):new RegExp(e)[t](Un(n))},function(t){var r=Nt(this),o=Un(t),i=n(e,r,o);if(i.done)return i.value;var u=r.lastIndex;dr(u,0)||(r.lastIndex=0);var c=function(t,e){var n=t.exec;if(L(n)){var r=O(n,t,e);return null!==r&&Nt(r),r}if("RegExp"===C(t))return O(sr,t,e);throw vr("RegExp#exec called on incompatible receiver")}(r,o);return dr(r.lastIndex,u)||(r.lastIndex=u),null===c?-1:c.index}]}));var br=n.default.fn.bootstrapTable.utils;n.default.extend(n.default.fn.bootstrapTable.defaults,{keyEvents:!1}),n.default.BootstrapTable=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&u(t,e)}(p,t);var e,c,f,s=a(p);function p(){return r(this,p),s.apply(this,arguments)}return e=p,c=[{key:"init",value:function(){for(var t,e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];(t=l(i(p.prototype),"init",this)).call.apply(t,[this].concat(n)),this.options.keyEvents&&this.initKeyEvents()}},{key:"initKeyEvents",value:function(){var t=this;n.default(document).off("keydown").on("keydown",(function(e){var r=br.getSearchInput(t),o=t.$toolbar.find('button[name="refresh"]'),i=t.$toolbar.find('button[name="toggle"]'),u=t.$toolbar.find('button[name="paginationSwitch"]');if(document.activeElement===r.get(0)||!n.default.contains(document.activeElement,t.$toolbar.get(0)))return!0;switch(e.keyCode){case 83:if(!t.options.search)return;return r.focus(),!1;case 82:if(!t.options.showRefresh)return;return o.click(),!1;case 84:if(!t.options.showToggle)return;return i.click(),!1;case 80:if(!t.options.showPaginationSwitch)return;return u.click(),!1;case 37:if(!t.options.pagination)return;return t.prevPage(),!1;case 39:if(!t.options.pagination)return;return void t.nextPage()}}))}}],c&&o(e.prototype,c),f&&o(e,f),Object.defineProperty(e,"prototype",{writable:!1}),p}(n.default.BootstrapTable)}));
