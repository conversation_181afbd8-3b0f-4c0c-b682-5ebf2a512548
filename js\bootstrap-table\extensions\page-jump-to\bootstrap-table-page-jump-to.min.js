/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.20.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var e=n(t);function r(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}function o(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,n){return u=Object.setPrototypeOf||function(t,n){return t.__proto__=n,t},u(t,n)}function c(t,n){if(n&&("object"==typeof n||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function a(t){var n=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var e,r=i(t);if(n){var o=i(this).constructor;e=Reflect.construct(r,arguments,o)}else e=r.apply(this,arguments);return c(this,e)}}function f(t,n){for(;!Object.prototype.hasOwnProperty.call(t,n)&&null!==(t=i(t)););return t}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,n,e){var r=f(t,n);if(r){var o=Object.getOwnPropertyDescriptor(r,n);return o.get?o.get.call(arguments.length<3?t:e):o.value}},l.apply(this,arguments)}var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function p(t,n){return t(n={exports:{}},n.exports),n.exports}var y,b,d=function(t){return t&&t.Math==Math&&t},h=d("object"==typeof globalThis&&globalThis)||d("object"==typeof window&&window)||d("object"==typeof self&&self)||d("object"==typeof s&&s)||function(){return this}()||Function("return this")(),v=function(t){try{return!!t()}catch(t){return!0}},g=!v((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),m=!v((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),w=Function.prototype.call,O=m?w.bind(w):function(){return w.apply(w,arguments)},j={}.propertyIsEnumerable,S=Object.getOwnPropertyDescriptor,P={f:S&&!j.call({1:2},1)?function(t){var n=S(this,t);return!!n&&n.enumerable}:j},T=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},E=Function.prototype,x=E.bind,A=E.call,R=m&&x.bind(A,A),F=m?function(t){return t&&R(t)}:function(t){return t&&function(){return A.apply(t,arguments)}},_=F({}.toString),k=F("".slice),C=function(t){return k(_(t),8,-1)},I=h.Object,M=F("".split),z=v((function(){return!I("z").propertyIsEnumerable(0)}))?function(t){return"String"==C(t)?M(t,""):I(t)}:I,B=h.TypeError,N=function(t){if(null==t)throw B("Can't call method on "+t);return t},D=function(t){return z(N(t))},G=function(t){return"function"==typeof t},L=function(t){return"object"==typeof t?null!==t:G(t)},J=function(t){return G(t)?t:void 0},q=function(t,n){return arguments.length<2?J(h[t]):h[t]&&h[t][n]},W=F({}.isPrototypeOf),U=q("navigator","userAgent")||"",X=h.process,$=h.Deno,K=X&&X.versions||$&&$.version,Q=K&&K.v8;Q&&(b=(y=Q.split("."))[0]>0&&y[0]<4?1:+(y[0]+y[1])),!b&&U&&(!(y=U.match(/Edge\/(\d+)/))||y[1]>=74)&&(y=U.match(/Chrome\/(\d+)/))&&(b=+y[1]);var V=b,Y=!!Object.getOwnPropertySymbols&&!v((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&V&&V<41})),H=Y&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Z=h.Object,tt=H?function(t){return"symbol"==typeof t}:function(t){var n=q("Symbol");return G(n)&&W(n.prototype,Z(t))},nt=h.String,et=h.TypeError,rt=function(t){if(G(t))return t;throw et(function(t){try{return nt(t)}catch(t){return"Object"}}(t)+" is not a function")},ot=h.TypeError,it=Object.defineProperty,ut=function(t,n){try{it(h,t,{value:n,configurable:!0,writable:!0})}catch(e){h[t]=n}return n},ct="__core-js_shared__",at=h[ct]||ut(ct,{}),ft=p((function(t){(t.exports=function(t,n){return at[t]||(at[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.21.1",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})})),lt=h.Object,st=function(t){return lt(N(t))},pt=F({}.hasOwnProperty),yt=Object.hasOwn||function(t,n){return pt(st(t),n)},bt=0,dt=Math.random(),ht=F(1..toString),vt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+ht(++bt+dt,36)},gt=ft("wks"),mt=h.Symbol,wt=mt&&mt.for,Ot=H?mt:mt&&mt.withoutSetter||vt,jt=function(t){if(!yt(gt,t)||!Y&&"string"!=typeof gt[t]){var n="Symbol."+t;Y&&yt(mt,t)?gt[t]=mt[t]:gt[t]=H&&wt?wt(n):Ot(n)}return gt[t]},St=h.TypeError,Pt=jt("toPrimitive"),Tt=function(t,n){if(!L(t)||tt(t))return t;var e,r,o=null==(e=t[Pt])?void 0:rt(e);if(o){if(void 0===n&&(n="default"),r=O(o,t,n),!L(r)||tt(r))return r;throw St("Can't convert object to primitive value")}return void 0===n&&(n="number"),function(t,n){var e,r;if("string"===n&&G(e=t.toString)&&!L(r=O(e,t)))return r;if(G(e=t.valueOf)&&!L(r=O(e,t)))return r;if("string"!==n&&G(e=t.toString)&&!L(r=O(e,t)))return r;throw ot("Can't convert object to primitive value")}(t,n)},Et=function(t){var n=Tt(t,"string");return tt(n)?n:n+""},xt=h.document,At=L(xt)&&L(xt.createElement),Rt=function(t){return At?xt.createElement(t):{}},Ft=!g&&!v((function(){return 7!=Object.defineProperty(Rt("div"),"a",{get:function(){return 7}}).a})),_t=Object.getOwnPropertyDescriptor,kt={f:g?_t:function(t,n){if(t=D(t),n=Et(n),Ft)try{return _t(t,n)}catch(t){}if(yt(t,n))return T(!O(P.f,t,n),t[n])}},Ct=g&&v((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),It=h.String,Mt=h.TypeError,zt=function(t){if(L(t))return t;throw Mt(It(t)+" is not an object")},Bt=h.TypeError,Nt=Object.defineProperty,Dt=Object.getOwnPropertyDescriptor,Gt="enumerable",Lt="configurable",Jt="writable",qt={f:g?Ct?function(t,n,e){if(zt(t),n=Et(n),zt(e),"function"==typeof t&&"prototype"===n&&"value"in e&&Jt in e&&!e.writable){var r=Dt(t,n);r&&r.writable&&(t[n]=e.value,e={configurable:Lt in e?e.configurable:r.configurable,enumerable:Gt in e?e.enumerable:r.enumerable,writable:!1})}return Nt(t,n,e)}:Nt:function(t,n,e){if(zt(t),n=Et(n),zt(e),Ft)try{return Nt(t,n,e)}catch(t){}if("get"in e||"set"in e)throw Bt("Accessors not supported");return"value"in e&&(t[n]=e.value),t}},Wt=g?function(t,n,e){return qt.f(t,n,T(1,e))}:function(t,n,e){return t[n]=e,t},Ut=F(Function.toString);G(at.inspectSource)||(at.inspectSource=function(t){return Ut(t)});var Xt,$t,Kt,Qt=at.inspectSource,Vt=h.WeakMap,Yt=G(Vt)&&/native code/.test(Qt(Vt)),Ht=ft("keys"),Zt=function(t){return Ht[t]||(Ht[t]=vt(t))},tn={},nn="Object already initialized",en=h.TypeError,rn=h.WeakMap;if(Yt||at.state){var on=at.state||(at.state=new rn),un=F(on.get),cn=F(on.has),an=F(on.set);Xt=function(t,n){if(cn(on,t))throw new en(nn);return n.facade=t,an(on,t,n),n},$t=function(t){return un(on,t)||{}},Kt=function(t){return cn(on,t)}}else{var fn=Zt("state");tn[fn]=!0,Xt=function(t,n){if(yt(t,fn))throw new en(nn);return n.facade=t,Wt(t,fn,n),n},$t=function(t){return yt(t,fn)?t[fn]:{}},Kt=function(t){return yt(t,fn)}}var ln={set:Xt,get:$t,has:Kt,enforce:function(t){return Kt(t)?$t(t):Xt(t,{})},getterFor:function(t){return function(n){var e;if(!L(n)||(e=$t(n)).type!==t)throw en("Incompatible receiver, "+t+" required");return e}}},sn=Function.prototype,pn=g&&Object.getOwnPropertyDescriptor,yn=yt(sn,"name"),bn={EXISTS:yn,PROPER:yn&&"something"===function(){}.name,CONFIGURABLE:yn&&(!g||g&&pn(sn,"name").configurable)},dn=p((function(t){var n=bn.CONFIGURABLE,e=ln.get,r=ln.enforce,o=String(String).split("String");(t.exports=function(t,e,i,u){var c,a=!!u&&!!u.unsafe,f=!!u&&!!u.enumerable,l=!!u&&!!u.noTargetGet,s=u&&void 0!==u.name?u.name:e;G(i)&&("Symbol("===String(s).slice(0,7)&&(s="["+String(s).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!yt(i,"name")||n&&i.name!==s)&&Wt(i,"name",s),(c=r(i)).source||(c.source=o.join("string"==typeof s?s:""))),t!==h?(a?!l&&t[e]&&(f=!0):delete t[e],f?t[e]=i:Wt(t,e,i)):f?t[e]=i:ut(e,i)})(Function.prototype,"toString",(function(){return G(this)&&e(this).source||Qt(this)}))})),hn=Math.ceil,vn=Math.floor,gn=function(t){var n=+t;return n!=n||0===n?0:(n>0?vn:hn)(n)},mn=Math.max,wn=Math.min,On=Math.min,jn=function(t){return(n=t.length)>0?On(gn(n),9007199254740991):0;var n},Sn=function(t){return function(n,e,r){var o,i=D(n),u=jn(i),c=function(t,n){var e=gn(t);return e<0?mn(e+n,0):wn(e,n)}(r,u);if(t&&e!=e){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===e)return t||c||0;return!t&&-1}},Pn={includes:Sn(!0),indexOf:Sn(!1)}.indexOf,Tn=F([].push),En=function(t,n){var e,r=D(t),o=0,i=[];for(e in r)!yt(tn,e)&&yt(r,e)&&Tn(i,e);for(;n.length>o;)yt(r,e=n[o++])&&(~Pn(i,e)||Tn(i,e));return i},xn=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],An=xn.concat("length","prototype"),Rn={f:Object.getOwnPropertyNames||function(t){return En(t,An)}},Fn={f:Object.getOwnPropertySymbols},_n=F([].concat),kn=q("Reflect","ownKeys")||function(t){var n=Rn.f(zt(t)),e=Fn.f;return e?_n(n,e(t)):n},Cn=function(t,n,e){for(var r=kn(n),o=qt.f,i=kt.f,u=0;u<r.length;u++){var c=r[u];yt(t,c)||e&&yt(e,c)||o(t,c,i(n,c))}},In=/#|\.prototype\./,Mn=function(t,n){var e=Bn[zn(t)];return e==Dn||e!=Nn&&(G(n)?v(n):!!n)},zn=Mn.normalize=function(t){return String(t).replace(In,".").toLowerCase()},Bn=Mn.data={},Nn=Mn.NATIVE="N",Dn=Mn.POLYFILL="P",Gn=Mn,Ln=kt.f,Jn=function(t,n){var e,r,o,i,u,c=t.target,a=t.global,f=t.stat;if(e=a?h:f?h[c]||ut(c,{}):(h[c]||{}).prototype)for(r in n){if(i=n[r],o=t.noTargetGet?(u=Ln(e,r))&&u.value:e[r],!Gn(a?r:c+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Cn(i,o)}(t.sham||o&&o.sham)&&Wt(i,"sham",!0),dn(e,r,i,t)}},qn=Array.isArray||function(t){return"Array"==C(t)},Wn=function(t,n,e){var r=Et(n);r in t?qt.f(t,r,T(0,e)):t[r]=e},Un={};Un[jt("toStringTag")]="z";var Xn="[object z]"===String(Un),$n=jt("toStringTag"),Kn=h.Object,Qn="Arguments"==C(function(){return arguments}()),Vn=Xn?C:function(t){var n,e,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(e=function(t,n){try{return t[n]}catch(t){}}(n=Kn(t),$n))?e:Qn?C(n):"Object"==(r=C(n))&&G(n.callee)?"Arguments":r},Yn=function(){},Hn=[],Zn=q("Reflect","construct"),te=/^\s*(?:class|function)\b/,ne=F(te.exec),ee=!te.exec(Yn),re=function(t){if(!G(t))return!1;try{return Zn(Yn,Hn,t),!0}catch(t){return!1}},oe=function(t){if(!G(t))return!1;switch(Vn(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return ee||!!ne(te,Qt(t))}catch(t){return!0}};oe.sham=!0;var ie,ue=!Zn||v((function(){var t;return re(re.call)||!re(Object)||!re((function(){t=!0}))||t}))?oe:re,ce=jt("species"),ae=h.Array,fe=function(t,n){return new(function(t){var n;return qn(t)&&(n=t.constructor,(ue(n)&&(n===ae||qn(n.prototype))||L(n)&&null===(n=n[ce]))&&(n=void 0)),void 0===n?ae:n}(t))(0===n?0:n)},le=jt("species"),se=jt("isConcatSpreadable"),pe=9007199254740991,ye="Maximum allowed index exceeded",be=h.TypeError,de=V>=51||!v((function(){var t=[];return t[se]=!1,t.concat()[0]!==t})),he=(ie="concat",V>=51||!v((function(){var t=[];return(t.constructor={})[le]=function(){return{foo:1}},1!==t[ie](Boolean).foo}))),ve=function(t){if(!L(t))return!1;var n=t[se];return void 0!==n?!!n:qn(t)};Jn({target:"Array",proto:!0,forced:!de||!he},{concat:function(t){var n,e,r,o,i,u=st(this),c=fe(u,0),a=0;for(n=-1,r=arguments.length;n<r;n++)if(ve(i=-1===n?u:arguments[n])){if(a+(o=jn(i))>pe)throw be(ye);for(e=0;e<o;e++,a++)e in i&&Wn(c,a,i[e])}else{if(a>=pe)throw be(ye);Wn(c,a++,i)}return c.length=a,c}});var ge,me=F(F.bind),we=F([].push),Oe=function(t){var n=1==t,e=2==t,r=3==t,o=4==t,i=6==t,u=7==t,c=5==t||i;return function(a,f,l,s){for(var p,y,b=st(a),d=z(b),h=function(t,n){return rt(t),void 0===n?t:m?me(t,n):function(){return t.apply(n,arguments)}}(f,l),v=jn(d),g=0,w=s||fe,O=n?w(a,v):e||u?w(a,0):void 0;v>g;g++)if((c||g in d)&&(y=h(p=d[g],g,b),t))if(n)O[g]=y;else if(y)switch(t){case 3:return!0;case 5:return p;case 6:return g;case 2:we(O,p)}else switch(t){case 4:return!1;case 7:we(O,p)}return i?-1:r||o?o:O}},je={forEach:Oe(0),map:Oe(1),filter:Oe(2),some:Oe(3),every:Oe(4),find:Oe(5),findIndex:Oe(6),filterReject:Oe(7)},Se=Object.keys||function(t){return En(t,xn)},Pe=g&&!Ct?Object.defineProperties:function(t,n){zt(t);for(var e,r=D(n),o=Se(n),i=o.length,u=0;i>u;)qt.f(t,e=o[u++],r[e]);return t},Te={f:Pe},Ee=q("document","documentElement"),xe=Zt("IE_PROTO"),Ae=function(){},Re=function(t){return"<script>"+t+"</"+"script>"},Fe=function(t){t.write(Re("")),t.close();var n=t.parentWindow.Object;return t=null,n},_e=function(){try{ge=new ActiveXObject("htmlfile")}catch(t){}var t,n;_e="undefined"!=typeof document?document.domain&&ge?Fe(ge):((n=Rt("iframe")).style.display="none",Ee.appendChild(n),n.src=String("javascript:"),(t=n.contentWindow.document).open(),t.write(Re("document.F=Object")),t.close(),t.F):Fe(ge);for(var e=xn.length;e--;)delete _e.prototype[xn[e]];return _e()};tn[xe]=!0;var ke=Object.create||function(t,n){var e;return null!==t?(Ae.prototype=zt(t),e=new Ae,Ae.prototype=null,e[xe]=t):e=_e(),void 0===n?e:Te.f(e,n)},Ce=jt("unscopables"),Ie=Array.prototype;null==Ie[Ce]&&qt.f(Ie,Ce,{configurable:!0,value:ke(null)});var Me,ze=je.find,Be="find",Ne=!0;Be in[]&&Array(1).find((function(){Ne=!1})),Jn({target:"Array",proto:!0,forced:Ne},{find:function(t){return ze(this,t,arguments.length>1?arguments[1]:void 0)}}),Me=Be,Ie[Ce][Me]=!0;var De=Xn?{}.toString:function(){return"[object "+Vn(this)+"]"};Xn||dn(Object.prototype,"toString",De,{unsafe:!0});var Ge=e.default.fn.bootstrapTable.utils;e.default.extend(e.default.fn.bootstrapTable.defaults,{showJumpTo:!1,showJumpToByPages:0}),e.default.extend(e.default.fn.bootstrapTable.locales,{formatJumpTo:function(){return"GO"}}),e.default.extend(e.default.fn.bootstrapTable.defaults,e.default.fn.bootstrapTable.locales),e.default.BootstrapTable=function(t){!function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),n&&u(t,n)}(p,t);var n,c,f,s=a(p);function p(){return r(this,p),s.apply(this,arguments)}return n=p,c=[{key:"initPagination",value:function(){for(var t,n=this,r=arguments.length,o=new Array(r),u=0;u<r;u++)o[u]=arguments[u];if((t=l(i(p.prototype),"initPagination",this)).call.apply(t,[this].concat(o)),this.options.showJumpTo&&this.totalPages>=this.options.showJumpToByPages){var c=this.$pagination.find("> .pagination"),a=c.find(".page-jump-to");if(!a.length){var f=(a=e.default(Ge.sprintf(this.constants.html.inputGroup,'<input type="number"\n            class="'.concat(this.constants.classes.input).concat(Ge.sprintf(" %s%s",this.constants.classes.inputPrefix,this.options.iconSize),'"\n            value="').concat(this.options.pageNumber,'"\n            min="1"\n            max="').concat(this.totalPages,'">'),'<button class="'.concat(this.constants.buttonsClass,'"  type="button">\n          ').concat(this.options.formatJumpTo(),"\n          </button>"))).addClass("page-jump-to").appendTo(c)).find("input");a.find("button").click((function(){n.selectPage(+f.val())})),f.keyup((function(t){""!==f.val()&&(13!==t.keyCode?+f.val()<+f.attr("min")?f.val(f.attr("min")):+f.val()>+f.attr("max")&&f.val(f.attr("max")):n.selectPage(+f.val()))})),f.blur((function(){""===f.val()&&f.val(n.options.pageNumber)}))}}}}],c&&o(n.prototype,c),f&&o(n,f),Object.defineProperty(n,"prototype",{writable:!1}),p}(e.default.BootstrapTable)}));
