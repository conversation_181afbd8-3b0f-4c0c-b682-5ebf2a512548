/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.20.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=e(t),n="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,e){return t(e={exports:{}},e.exports),e.exports}var i,a,s=function(t){return t&&t.Math==Math&&t},u=s("object"==typeof globalThis&&globalThis)||s("object"==typeof window&&window)||s("object"==typeof self&&self)||s("object"==typeof n&&n)||function(){return this}()||Function("return this")(),c=function(t){try{return!!t()}catch(t){return!0}},f=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),p=!c((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),l=Function.prototype.call,h=p?l.bind(l):function(){return l.apply(l,arguments)},d={}.propertyIsEnumerable,y=Object.getOwnPropertyDescriptor,g={f:y&&!d.call({1:2},1)?function(t){var e=y(this,t);return!!e&&e.enumerable}:d},b=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},v=Function.prototype,m=v.bind,w=v.call,S=p&&m.bind(w,w),O=p?function(t){return t&&S(t)}:function(t){return t&&function(){return w.apply(t,arguments)}},j=O({}.toString),x=O("".slice),P=function(t){return x(j(t),8,-1)},T=u.Object,C=O("".split),E=c((function(){return!T("z").propertyIsEnumerable(0)}))?function(t){return"String"==P(t)?C(t,""):T(t)}:T,A=u.TypeError,z=function(t){if(null==t)throw A("Can't call method on "+t);return t},N=function(t){return E(z(t))},W=function(t){return"function"==typeof t},R=function(t){return"object"==typeof t?null!==t:W(t)},F=function(t){return W(t)?t:void 0},I=function(t,e){return arguments.length<2?F(u[t]):u[t]&&u[t][e]},L=O({}.isPrototypeOf),M=I("navigator","userAgent")||"",D=u.process,_=u.Deno,k=D&&D.versions||_&&_.version,q=k&&k.v8;q&&(a=(i=q.split("."))[0]>0&&i[0]<4?1:+(i[0]+i[1])),!a&&M&&(!(i=M.match(/Edge\/(\d+)/))||i[1]>=74)&&(i=M.match(/Chrome\/(\d+)/))&&(a=+i[1]);var G=a,V=!!Object.getOwnPropertySymbols&&!c((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&G&&G<41})),J=V&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,B=u.Object,H=J?function(t){return"symbol"==typeof t}:function(t){var e=I("Symbol");return W(e)&&L(e.prototype,B(t))},U=u.String,$=u.TypeError,K=function(t){if(W(t))return t;throw $(function(t){try{return U(t)}catch(t){return"Object"}}(t)+" is not a function")},Q=u.TypeError,X=Object.defineProperty,Y=function(t,e){try{X(u,t,{value:e,configurable:!0,writable:!0})}catch(r){u[t]=e}return e},Z="__core-js_shared__",tt=u[Z]||Y(Z,{}),et=o((function(t){(t.exports=function(t,e){return tt[t]||(tt[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.21.1",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})})),rt=u.Object,nt=function(t){return rt(z(t))},ot=O({}.hasOwnProperty),it=Object.hasOwn||function(t,e){return ot(nt(t),e)},at=0,st=Math.random(),ut=O(1..toString),ct=function(t){return"Symbol("+(void 0===t?"":t)+")_"+ut(++at+st,36)},ft=et("wks"),pt=u.Symbol,lt=pt&&pt.for,ht=J?pt:pt&&pt.withoutSetter||ct,dt=function(t){if(!it(ft,t)||!V&&"string"!=typeof ft[t]){var e="Symbol."+t;V&&it(pt,t)?ft[t]=pt[t]:ft[t]=J&&lt?lt(e):ht(e)}return ft[t]},yt=u.TypeError,gt=dt("toPrimitive"),bt=function(t,e){if(!R(t)||H(t))return t;var r,n,o=null==(r=t[gt])?void 0:K(r);if(o){if(void 0===e&&(e="default"),n=h(o,t,e),!R(n)||H(n))return n;throw yt("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var r,n;if("string"===e&&W(r=t.toString)&&!R(n=h(r,t)))return n;if(W(r=t.valueOf)&&!R(n=h(r,t)))return n;if("string"!==e&&W(r=t.toString)&&!R(n=h(r,t)))return n;throw Q("Can't convert object to primitive value")}(t,e)},vt=function(t){var e=bt(t,"string");return H(e)?e:e+""},mt=u.document,wt=R(mt)&&R(mt.createElement),St=!f&&!c((function(){return 7!=Object.defineProperty((t="div",wt?mt.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),Ot=Object.getOwnPropertyDescriptor,jt={f:f?Ot:function(t,e){if(t=N(t),e=vt(e),St)try{return Ot(t,e)}catch(t){}if(it(t,e))return b(!h(g.f,t,e),t[e])}},xt=f&&c((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Pt=u.String,Tt=u.TypeError,Ct=function(t){if(R(t))return t;throw Tt(Pt(t)+" is not an object")},Et=u.TypeError,At=Object.defineProperty,zt=Object.getOwnPropertyDescriptor,Nt="enumerable",Wt="configurable",Rt="writable",Ft={f:f?xt?function(t,e,r){if(Ct(t),e=vt(e),Ct(r),"function"==typeof t&&"prototype"===e&&"value"in r&&Rt in r&&!r.writable){var n=zt(t,e);n&&n.writable&&(t[e]=r.value,r={configurable:Wt in r?r.configurable:n.configurable,enumerable:Nt in r?r.enumerable:n.enumerable,writable:!1})}return At(t,e,r)}:At:function(t,e,r){if(Ct(t),e=vt(e),Ct(r),St)try{return At(t,e,r)}catch(t){}if("get"in r||"set"in r)throw Et("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},It=f?function(t,e,r){return Ft.f(t,e,b(1,r))}:function(t,e,r){return t[e]=r,t},Lt=O(Function.toString);W(tt.inspectSource)||(tt.inspectSource=function(t){return Lt(t)});var Mt,Dt,_t,kt,qt=tt.inspectSource,Gt=u.WeakMap,Vt=W(Gt)&&/native code/.test(qt(Gt)),Jt=et("keys"),Bt={},Ht="Object already initialized",Ut=u.TypeError,$t=u.WeakMap;if(Vt||tt.state){var Kt=tt.state||(tt.state=new $t),Qt=O(Kt.get),Xt=O(Kt.has),Yt=O(Kt.set);Mt=function(t,e){if(Xt(Kt,t))throw new Ut(Ht);return e.facade=t,Yt(Kt,t,e),e},Dt=function(t){return Qt(Kt,t)||{}},_t=function(t){return Xt(Kt,t)}}else{var Zt=Jt[kt="state"]||(Jt[kt]=ct(kt));Bt[Zt]=!0,Mt=function(t,e){if(it(t,Zt))throw new Ut(Ht);return e.facade=t,It(t,Zt,e),e},Dt=function(t){return it(t,Zt)?t[Zt]:{}},_t=function(t){return it(t,Zt)}}var te={set:Mt,get:Dt,has:_t,enforce:function(t){return _t(t)?Dt(t):Mt(t,{})},getterFor:function(t){return function(e){var r;if(!R(e)||(r=Dt(e)).type!==t)throw Ut("Incompatible receiver, "+t+" required");return r}}},ee=Function.prototype,re=f&&Object.getOwnPropertyDescriptor,ne=it(ee,"name"),oe={EXISTS:ne,PROPER:ne&&"something"===function(){}.name,CONFIGURABLE:ne&&(!f||f&&re(ee,"name").configurable)},ie=o((function(t){var e=oe.CONFIGURABLE,r=te.get,n=te.enforce,o=String(String).split("String");(t.exports=function(t,r,i,a){var s,c=!!a&&!!a.unsafe,f=!!a&&!!a.enumerable,p=!!a&&!!a.noTargetGet,l=a&&void 0!==a.name?a.name:r;W(i)&&("Symbol("===String(l).slice(0,7)&&(l="["+String(l).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!it(i,"name")||e&&i.name!==l)&&It(i,"name",l),(s=n(i)).source||(s.source=o.join("string"==typeof l?l:""))),t!==u?(c?!p&&t[r]&&(f=!0):delete t[r],f?t[r]=i:It(t,r,i)):f?t[r]=i:Y(r,i)})(Function.prototype,"toString",(function(){return W(this)&&r(this).source||qt(this)}))})),ae=Math.ceil,se=Math.floor,ue=function(t){var e=+t;return e!=e||0===e?0:(e>0?se:ae)(e)},ce=Math.max,fe=Math.min,pe=function(t,e){var r=ue(t);return r<0?ce(r+e,0):fe(r,e)},le=Math.min,he=function(t){return(e=t.length)>0?le(ue(e),9007199254740991):0;var e},de=function(t){return function(e,r,n){var o,i=N(e),a=he(i),s=pe(n,a);if(t&&r!=r){for(;a>s;)if((o=i[s++])!=o)return!0}else for(;a>s;s++)if((t||s in i)&&i[s]===r)return t||s||0;return!t&&-1}},ye={includes:de(!0),indexOf:de(!1)},ge=ye.indexOf,be=O([].push),ve=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),me=Object.getOwnPropertyNames||function(t){return function(t,e){var r,n=N(t),o=0,i=[];for(r in n)!it(Bt,r)&&it(n,r)&&be(i,r);for(;e.length>o;)it(n,r=e[o++])&&(~ge(i,r)||be(i,r));return i}(t,ve)},we={f:me},Se={f:Object.getOwnPropertySymbols},Oe=O([].concat),je=I("Reflect","ownKeys")||function(t){var e=we.f(Ct(t)),r=Se.f;return r?Oe(e,r(t)):e},xe=function(t,e,r){for(var n=je(e),o=Ft.f,i=jt.f,a=0;a<n.length;a++){var s=n[a];it(t,s)||r&&it(r,s)||o(t,s,i(e,s))}},Pe=/#|\.prototype\./,Te=function(t,e){var r=Ee[Ce(t)];return r==ze||r!=Ae&&(W(e)?c(e):!!e)},Ce=Te.normalize=function(t){return String(t).replace(Pe,".").toLowerCase()},Ee=Te.data={},Ae=Te.NATIVE="N",ze=Te.POLYFILL="P",Ne=Te,We=jt.f,Re=function(t,e){var r,n,o,i,a,s=t.target,c=t.global,f=t.stat;if(r=c?u:f?u[s]||Y(s,{}):(u[s]||{}).prototype)for(n in e){if(i=e[n],o=t.noTargetGet?(a=We(r,n))&&a.value:r[n],!Ne(c?n:s+(f?".":"#")+n,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;xe(i,o)}(t.sham||o&&o.sham)&&It(i,"sham",!0),ie(r,n,i,t)}},Fe=Array.isArray||function(t){return"Array"==P(t)},Ie={};Ie[dt("toStringTag")]="z";var Le="[object z]"===String(Ie),Me=dt("toStringTag"),De=u.Object,_e="Arguments"==P(function(){return arguments}()),ke=Le?P:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=De(t),Me))?r:_e?P(e):"Object"==(n=P(e))&&W(e.callee)?"Arguments":n},qe=function(){},Ge=[],Ve=I("Reflect","construct"),Je=/^\s*(?:class|function)\b/,Be=O(Je.exec),He=!Je.exec(qe),Ue=function(t){if(!W(t))return!1;try{return Ve(qe,Ge,t),!0}catch(t){return!1}},$e=function(t){if(!W(t))return!1;switch(ke(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return He||!!Be(Je,qt(t))}catch(t){return!0}};$e.sham=!0;var Ke=!Ve||c((function(){var t;return Ue(Ue.call)||!Ue(Object)||!Ue((function(){t=!0}))||t}))?$e:Ue,Qe=function(t,e,r){var n=vt(e);n in t?Ft.f(t,n,b(0,r)):t[n]=r},Xe=dt("species"),Ye=function(t){return G>=51||!c((function(){var e=[];return(e.constructor={})[Xe]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},Ze=O([].slice),tr=Ye("slice"),er=dt("species"),rr=u.Array,nr=Math.max;Re({target:"Array",proto:!0,forced:!tr},{slice:function(t,e){var r,n,o,i=N(this),a=he(i),s=pe(t,a),u=pe(void 0===e?a:e,a);if(Fe(i)&&(r=i.constructor,(Ke(r)&&(r===rr||Fe(r.prototype))||R(r)&&null===(r=r[er]))&&(r=void 0),r===rr||void 0===r))return Ze(i,s,u);for(n=new(void 0===r?rr:r)(nr(u-s,0)),o=0;s<u;s++,o++)s in i&&Qe(n,o,i[s]);return n.length=o,n}});var or=u.String,ir=function(t){if("Symbol"===ke(t))throw TypeError("Cannot convert a Symbol value to a string");return or(t)},ar="\t\n\v\f\r                　\u2028\u2029\ufeff",sr=O("".replace),ur="["+ar+"]",cr=RegExp("^"+ur+ur+"*"),fr=RegExp(ur+ur+"*$"),pr=function(t){return function(e){var r=ir(z(e));return 1&t&&(r=sr(r,cr,"")),2&t&&(r=sr(r,fr,"")),r}},lr={start:pr(1),end:pr(2),trim:pr(3)}.trim,hr=u.parseInt,dr=u.Symbol,yr=dr&&dr.iterator,gr=/^[+-]?0x/i,br=O(gr.exec),vr=8!==hr(ar+"08")||22!==hr(ar+"0x16")||yr&&!c((function(){hr(Object(yr))}))?function(t,e){var r=lr(ir(t));return hr(r,e>>>0||(br(gr,r)?16:10))}:hr;Re({global:!0,forced:parseInt!=vr},{parseInt:vr});var mr,wr,Sr=ye.indexOf,Or=O([].indexOf),jr=!!Or&&1/Or([1],1,-0)<0,xr=!!(wr=[]["indexOf"])&&c((function(){wr.call(null,mr||function(){return 1},1)}));Re({target:"Array",proto:!0,forced:jr||!xr},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return jr?Or(this,t,e)||0:Sr(this,t,e)}});var Pr=O(O.bind),Tr=dt("species"),Cr=u.Array,Er=function(t,e){return new(function(t){var e;return Fe(t)&&(e=t.constructor,(Ke(e)&&(e===Cr||Fe(e.prototype))||R(e)&&null===(e=e[Tr]))&&(e=void 0)),void 0===e?Cr:e}(t))(0===e?0:e)},Ar=O([].push),zr=function(t){var e=1==t,r=2==t,n=3==t,o=4==t,i=6==t,a=7==t,s=5==t||i;return function(u,c,f,l){for(var h,d,y=nt(u),g=E(y),b=function(t,e){return K(t),void 0===e?t:p?Pr(t,e):function(){return t.apply(e,arguments)}}(c,f),v=he(g),m=0,w=l||Er,S=e?w(u,v):r||a?w(u,0):void 0;v>m;m++)if((s||m in g)&&(d=b(h=g[m],m,y),t))if(e)S[m]=d;else if(d)switch(t){case 3:return!0;case 5:return h;case 6:return m;case 2:Ar(S,h)}else switch(t){case 4:return!1;case 7:Ar(S,h)}return i?-1:n||o?o:S}},Nr={forEach:zr(0),map:zr(1),filter:zr(2),some:zr(3),every:zr(4),find:zr(5),findIndex:zr(6),filterReject:zr(7)}.filter;Re({target:"Array",proto:!0,forced:!Ye("filter")},{filter:function(t){return Nr(this,t,arguments.length>1?arguments[1]:void 0)}});var Wr=Le?{}.toString:function(){return"[object "+ke(this)+"]"};Le||ie(Object.prototype,"toString",Wr,{unsafe:!0});var Rr=r.default.fn.bootstrapTable.utils;r.default.extend(r.default.fn.bootstrapTable.defaults,{usePipeline:!1,pipelineSize:1e3,onCachedDataHit:function(t){return!1},onCachedDataReset:function(t){return!1}}),r.default.extend(r.default.fn.bootstrapTable.Constructor.EVENTS,{"cached-data-hit.bs.table":"onCachedDataHit","cached-data-reset.bs.table":"onCachedDataReset"});var Fr=r.default.fn.bootstrapTable.Constructor,Ir=Fr.prototype.init,Lr=Fr.prototype.onSearch,Mr=Fr.prototype.onSort,Dr=Fr.prototype.onPageListChange;Fr.prototype.init=function(){this.initPipeline();for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];Ir.apply(this,Array.prototype.slice.apply(e))},Fr.prototype.initPipeline=function(){this.cacheRequestJSON={},this.cacheWindows=[],this.currWindow=0,this.resetCache=!0},Fr.prototype.onSearch=function(){this.options.usePipeline&&(this.resetCache=!0),Lr.apply(this,Array.prototype.slice.apply(arguments))},Fr.prototype.onSort=function(){this.options.usePipeline&&(this.resetCache=!0),Mr.apply(this,Array.prototype.slice.apply(arguments))},Fr.prototype.onPageListChange=function(t){var e=r.default(t.currentTarget),n=parseInt(e.text(),10);this.options.pipelineSize=this.calculatePipelineSize(this.options.pipelineSize,n),this.resetCache=!0,Dr.apply(this,Array.prototype.slice.apply(arguments))},Fr.prototype.calculatePipelineSize=function(t,e){return 0===e?0:Math.ceil(t/e)*e},Fr.prototype.setCacheWindows=function(){this.cacheWindows=[];for(var t=this.options.totalRows/this.options.pipelineSize,e=0;e<=t;e++){var r=e*this.options.pipelineSize;this.cacheWindows[e]={lower:r,upper:r+this.options.pipelineSize-1}}},Fr.prototype.setCurrWindow=function(t){this.currWindow=0;for(var e=0;e<this.cacheWindows.length;e++)if(this.cacheWindows[e].lower<=t&&t<=this.cacheWindows[e].upper){this.currWindow=e;break}},Fr.prototype.drawFromCache=function(t,e){var n=r.default.extend(!0,{},this.cacheRequestJSON),o=t-this.cacheWindows[this.currWindow].lower,i=o+e;return n.rows=n.rows.slice(o,i),n},Fr.prototype.initServer=function(t,e,n){var o,i={},a=this.header.fields.indexOf(this.options.sortName),s={searchText:this.searchText,sortName:this.options.sortName,sortOrder:this.options.sortOrder};if(this.header.sortNames[a]&&(s.sortName=this.header.sortNames[a]),this.options.pagination&&"server"===this.options.sidePagination&&(s.pageSize=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,s.pageNumber=this.options.pageNumber),n||this.options.url||this.options.ajax){var u=!0;if("limit"===this.options.queryParamsType&&(s={searchText:s.searchText,sortName:s.sortName,sortOrder:s.sortOrder},this.options.pagination&&"server"===this.options.sidePagination))if(s.limit=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,s.offset=(this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize)*(this.options.pageNumber-1),this.options.usePipeline)if(this.cacheWindows.length){var c=this.cacheWindows[this.currWindow];this.resetCache||s.offset<c.lower||s.offset>c.upper?(u=!0,this.setCurrWindow(s.offset),s.drawOffset=s.offset,s.offset=this.cacheWindows[this.currWindow].lower):u=!1}else u=!0,s.drawOffset=s.offset;else 0===s.limit&&delete s.limit;if(this.resetCache&&(u=!0,this.resetCache=!1),this.options.usePipeline&&u&&(s.drawLimit=s.limit,s.limit=this.options.pipelineSize),!u){var f=this.drawFromCache(s.offset,s.limit);return this.load(f),this.trigger("load-success",f),void this.trigger("cached-data-hit",f)}if(r.default.isEmptyObject(this.filterColumnsPartial)||(s.filter=JSON.stringify(this.filterColumnsPartial,null)),i=Rr.calculateObjectValue(this.options,this.options.queryParams,[s],i),r.default.extend(i,e||{}),!1!==i){t||this.$tableLoading.show();var p=this;o=r.default.extend({},Rr.calculateObjectValue(null,this.options.ajaxOptions),{type:this.options.method,url:n||this.options.url,data:"application/json"===this.options.contentType&&"post"===this.options.method?JSON.stringify(i):i,cache:this.options.cache,contentType:this.options.contentType,dataType:this.options.dataType,success:function(e){e=Rr.calculateObjectValue(p.options,p.options.responseHandler,[e],e),p.options.usePipeline&&(p.cacheRequestJSON=r.default.extend(!0,{},e),p.options.totalRows=e[p.options.totalField],p.setCacheWindows(),p.setCurrWindow(s.drawOffset),e=p.drawFromCache(s.drawOffset,s.drawLimit),p.trigger("cached-data-reset",e)),p.load(e),p.trigger("load-success",e),t||p.hideLoading()},error:function(e){var r=[];"server"===p.options.sidePagination&&((r={})[p.options.totalField]=0,r[p.options.dataField]=[]),p.load(r),p.trigger("load-error",e.status,e),t||p.hideLoading()}}),this.options.ajax?Rr.calculateObjectValue(this,this.options.ajax,[o],null):(this._xhr&&4!==this._xhr.readyState&&this._xhr.abort(),this._xhr=r.default.ajax(o))}}}}));
