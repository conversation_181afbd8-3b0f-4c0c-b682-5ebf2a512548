/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.20.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=e(t);function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,e){return u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},u(t,e)}function c(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function a(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=i(t);if(e){var o=i(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return c(this,r)}}function f(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=i(t)););return t}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,r){var n=f(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},l.apply(this,arguments)}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==r)return;var n,o,i=[],u=!0,c=!1;try{for(r=r.call(t);!(u=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);u=!0);}catch(t){c=!0,o=t}finally{try{u||null==r.return||r.return()}finally{if(c)throw o}}return i}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return p(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return p(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var d="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function y(t,e){return t(e={exports:{}},e.exports),e.exports}var h,b,v=function(t){return t&&t.Math==Math&&t},g=v("object"==typeof globalThis&&globalThis)||v("object"==typeof window&&window)||v("object"==typeof self&&self)||v("object"==typeof d&&d)||function(){return this}()||Function("return this")(),m=function(t){try{return!!t()}catch(t){return!0}},w=!m((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),O=!m((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),j=Function.prototype.call,S=O?j.bind(j):function(){return j.apply(j,arguments)},T={}.propertyIsEnumerable,R=Object.getOwnPropertyDescriptor,E={f:R&&!T.call({1:2},1)?function(t){var e=R(this,t);return!!e&&e.enumerable}:T},P=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},x=Function.prototype,A=x.bind,C=x.call,k=O&&A.bind(C,C),I=O?function(t){return t&&k(t)}:function(t){return t&&function(){return C.apply(t,arguments)}},_=I({}.toString),F=I("".slice),M=function(t){return F(_(t),8,-1)},D=g.Object,z=I("".split),B=m((function(){return!D("z").propertyIsEnumerable(0)}))?function(t){return"String"==M(t)?z(t,""):D(t)}:D,L=g.TypeError,N=function(t){if(null==t)throw L("Can't call method on "+t);return t},V=function(t){return B(N(t))},G=function(t){return"function"==typeof t},$=function(t){return"object"==typeof t?null!==t:G(t)},q=function(t){return G(t)?t:void 0},H=function(t,e){return arguments.length<2?q(g[t]):g[t]&&g[t][e]},U=I({}.isPrototypeOf),W=H("navigator","userAgent")||"",X=g.process,K=g.Deno,Q=X&&X.versions||K&&K.version,Y=Q&&Q.v8;Y&&(b=(h=Y.split("."))[0]>0&&h[0]<4?1:+(h[0]+h[1])),!b&&W&&(!(h=W.match(/Edge\/(\d+)/))||h[1]>=74)&&(h=W.match(/Chrome\/(\d+)/))&&(b=+h[1]);var J=b,Z=!!Object.getOwnPropertySymbols&&!m((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&J&&J<41})),tt=Z&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,et=g.Object,rt=tt?function(t){return"symbol"==typeof t}:function(t){var e=H("Symbol");return G(e)&&U(e.prototype,et(t))},nt=g.String,ot=g.TypeError,it=function(t){if(G(t))return t;throw ot(function(t){try{return nt(t)}catch(t){return"Object"}}(t)+" is not a function")},ut=g.TypeError,ct=Object.defineProperty,at=function(t,e){try{ct(g,t,{value:e,configurable:!0,writable:!0})}catch(r){g[t]=e}return e},ft="__core-js_shared__",lt=g[ft]||at(ft,{}),st=y((function(t){(t.exports=function(t,e){return lt[t]||(lt[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.21.1",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})})),pt=g.Object,dt=function(t){return pt(N(t))},yt=I({}.hasOwnProperty),ht=Object.hasOwn||function(t,e){return yt(dt(t),e)},bt=0,vt=Math.random(),gt=I(1..toString),mt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+gt(++bt+vt,36)},wt=st("wks"),Ot=g.Symbol,jt=Ot&&Ot.for,St=tt?Ot:Ot&&Ot.withoutSetter||mt,Tt=function(t){if(!ht(wt,t)||!Z&&"string"!=typeof wt[t]){var e="Symbol."+t;Z&&ht(Ot,t)?wt[t]=Ot[t]:wt[t]=tt&&jt?jt(e):St(e)}return wt[t]},Rt=g.TypeError,Et=Tt("toPrimitive"),Pt=function(t,e){if(!$(t)||rt(t))return t;var r,n,o=null==(r=t[Et])?void 0:it(r);if(o){if(void 0===e&&(e="default"),n=S(o,t,e),!$(n)||rt(n))return n;throw Rt("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var r,n;if("string"===e&&G(r=t.toString)&&!$(n=S(r,t)))return n;if(G(r=t.valueOf)&&!$(n=S(r,t)))return n;if("string"!==e&&G(r=t.toString)&&!$(n=S(r,t)))return n;throw ut("Can't convert object to primitive value")}(t,e)},xt=function(t){var e=Pt(t,"string");return rt(e)?e:e+""},At=g.document,Ct=$(At)&&$(At.createElement),kt=function(t){return Ct?At.createElement(t):{}},It=!w&&!m((function(){return 7!=Object.defineProperty(kt("div"),"a",{get:function(){return 7}}).a})),_t=Object.getOwnPropertyDescriptor,Ft={f:w?_t:function(t,e){if(t=V(t),e=xt(e),It)try{return _t(t,e)}catch(t){}if(ht(t,e))return P(!S(E.f,t,e),t[e])}},Mt=w&&m((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Dt=g.String,zt=g.TypeError,Bt=function(t){if($(t))return t;throw zt(Dt(t)+" is not an object")},Lt=g.TypeError,Nt=Object.defineProperty,Vt=Object.getOwnPropertyDescriptor,Gt="enumerable",$t="configurable",qt="writable",Ht={f:w?Mt?function(t,e,r){if(Bt(t),e=xt(e),Bt(r),"function"==typeof t&&"prototype"===e&&"value"in r&&qt in r&&!r.writable){var n=Vt(t,e);n&&n.writable&&(t[e]=r.value,r={configurable:$t in r?r.configurable:n.configurable,enumerable:Gt in r?r.enumerable:n.enumerable,writable:!1})}return Nt(t,e,r)}:Nt:function(t,e,r){if(Bt(t),e=xt(e),Bt(r),It)try{return Nt(t,e,r)}catch(t){}if("get"in r||"set"in r)throw Lt("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},Ut=w?function(t,e,r){return Ht.f(t,e,P(1,r))}:function(t,e,r){return t[e]=r,t},Wt=I(Function.toString);G(lt.inspectSource)||(lt.inspectSource=function(t){return Wt(t)});var Xt,Kt,Qt,Yt=lt.inspectSource,Jt=g.WeakMap,Zt=G(Jt)&&/native code/.test(Yt(Jt)),te=st("keys"),ee=function(t){return te[t]||(te[t]=mt(t))},re={},ne="Object already initialized",oe=g.TypeError,ie=g.WeakMap;if(Zt||lt.state){var ue=lt.state||(lt.state=new ie),ce=I(ue.get),ae=I(ue.has),fe=I(ue.set);Xt=function(t,e){if(ae(ue,t))throw new oe(ne);return e.facade=t,fe(ue,t,e),e},Kt=function(t){return ce(ue,t)||{}},Qt=function(t){return ae(ue,t)}}else{var le=ee("state");re[le]=!0,Xt=function(t,e){if(ht(t,le))throw new oe(ne);return e.facade=t,Ut(t,le,e),e},Kt=function(t){return ht(t,le)?t[le]:{}},Qt=function(t){return ht(t,le)}}var se={set:Xt,get:Kt,has:Qt,enforce:function(t){return Qt(t)?Kt(t):Xt(t,{})},getterFor:function(t){return function(e){var r;if(!$(e)||(r=Kt(e)).type!==t)throw oe("Incompatible receiver, "+t+" required");return r}}},pe=Function.prototype,de=w&&Object.getOwnPropertyDescriptor,ye=ht(pe,"name"),he={EXISTS:ye,PROPER:ye&&"something"===function(){}.name,CONFIGURABLE:ye&&(!w||w&&de(pe,"name").configurable)},be=y((function(t){var e=he.CONFIGURABLE,r=se.get,n=se.enforce,o=String(String).split("String");(t.exports=function(t,r,i,u){var c,a=!!u&&!!u.unsafe,f=!!u&&!!u.enumerable,l=!!u&&!!u.noTargetGet,s=u&&void 0!==u.name?u.name:r;G(i)&&("Symbol("===String(s).slice(0,7)&&(s="["+String(s).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!ht(i,"name")||e&&i.name!==s)&&Ut(i,"name",s),(c=n(i)).source||(c.source=o.join("string"==typeof s?s:""))),t!==g?(a?!l&&t[r]&&(f=!0):delete t[r],f?t[r]=i:Ut(t,r,i)):f?t[r]=i:at(r,i)})(Function.prototype,"toString",(function(){return G(this)&&r(this).source||Yt(this)}))})),ve=Math.ceil,ge=Math.floor,me=function(t){var e=+t;return e!=e||0===e?0:(e>0?ge:ve)(e)},we=Math.max,Oe=Math.min,je=Math.min,Se=function(t){return(e=t.length)>0?je(me(e),9007199254740991):0;var e},Te=function(t){return function(e,r,n){var o,i=V(e),u=Se(i),c=function(t,e){var r=me(t);return r<0?we(r+e,0):Oe(r,e)}(n,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},Re={includes:Te(!0),indexOf:Te(!1)}.indexOf,Ee=I([].push),Pe=function(t,e){var r,n=V(t),o=0,i=[];for(r in n)!ht(re,r)&&ht(n,r)&&Ee(i,r);for(;e.length>o;)ht(n,r=e[o++])&&(~Re(i,r)||Ee(i,r));return i},xe=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Ae=xe.concat("length","prototype"),Ce={f:Object.getOwnPropertyNames||function(t){return Pe(t,Ae)}},ke={f:Object.getOwnPropertySymbols},Ie=I([].concat),_e=H("Reflect","ownKeys")||function(t){var e=Ce.f(Bt(t)),r=ke.f;return r?Ie(e,r(t)):e},Fe=function(t,e,r){for(var n=_e(e),o=Ht.f,i=Ft.f,u=0;u<n.length;u++){var c=n[u];ht(t,c)||r&&ht(r,c)||o(t,c,i(e,c))}},Me=/#|\.prototype\./,De=function(t,e){var r=Be[ze(t)];return r==Ne||r!=Le&&(G(e)?m(e):!!e)},ze=De.normalize=function(t){return String(t).replace(Me,".").toLowerCase()},Be=De.data={},Le=De.NATIVE="N",Ne=De.POLYFILL="P",Ve=De,Ge=Ft.f,$e=function(t,e){var r,n,o,i,u,c=t.target,a=t.global,f=t.stat;if(r=a?g:f?g[c]||at(c,{}):(g[c]||{}).prototype)for(n in e){if(i=e[n],o=t.noTargetGet?(u=Ge(r,n))&&u.value:r[n],!Ve(a?n:c+(f?".":"#")+n,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Fe(i,o)}(t.sham||o&&o.sham)&&Ut(i,"sham",!0),be(r,n,i,t)}},qe=Object.keys||function(t){return Pe(t,xe)},He=I(E.f),Ue=I([].push),We=function(t){return function(e){for(var r,n=V(e),o=qe(n),i=o.length,u=0,c=[];i>u;)r=o[u++],w&&!He(n,r)||Ue(c,t?[r,n[r]]:n[r]);return c}},Xe={entries:We(!0),values:We(!1)}.entries;$e({target:"Object",stat:!0},{entries:function(t){return Xe(t)}});var Ke=I(I.bind),Qe=Array.isArray||function(t){return"Array"==M(t)},Ye={};Ye[Tt("toStringTag")]="z";var Je="[object z]"===String(Ye),Ze=Tt("toStringTag"),tr=g.Object,er="Arguments"==M(function(){return arguments}()),rr=Je?M:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=tr(t),Ze))?r:er?M(e):"Object"==(n=M(e))&&G(e.callee)?"Arguments":n},nr=function(){},or=[],ir=H("Reflect","construct"),ur=/^\s*(?:class|function)\b/,cr=I(ur.exec),ar=!ur.exec(nr),fr=function(t){if(!G(t))return!1;try{return ir(nr,or,t),!0}catch(t){return!1}},lr=function(t){if(!G(t))return!1;switch(rr(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return ar||!!cr(ur,Yt(t))}catch(t){return!0}};lr.sham=!0;var sr,pr=!ir||m((function(){var t;return fr(fr.call)||!fr(Object)||!fr((function(){t=!0}))||t}))?lr:fr,dr=Tt("species"),yr=g.Array,hr=function(t,e){return new(function(t){var e;return Qe(t)&&(e=t.constructor,(pr(e)&&(e===yr||Qe(e.prototype))||$(e)&&null===(e=e[dr]))&&(e=void 0)),void 0===e?yr:e}(t))(0===e?0:e)},br=I([].push),vr=function(t){var e=1==t,r=2==t,n=3==t,o=4==t,i=6==t,u=7==t,c=5==t||i;return function(a,f,l,s){for(var p,d,y=dt(a),h=B(y),b=function(t,e){return it(t),void 0===e?t:O?Ke(t,e):function(){return t.apply(e,arguments)}}(f,l),v=Se(h),g=0,m=s||hr,w=e?m(a,v):r||u?m(a,0):void 0;v>g;g++)if((c||g in h)&&(d=b(p=h[g],g,y),t))if(e)w[g]=d;else if(d)switch(t){case 3:return!0;case 5:return p;case 6:return g;case 2:br(w,p)}else switch(t){case 4:return!1;case 7:br(w,p)}return i?-1:n||o?o:w}},gr={forEach:vr(0),map:vr(1),filter:vr(2),some:vr(3),every:vr(4),find:vr(5),findIndex:vr(6),filterReject:vr(7)},mr=w&&!Mt?Object.defineProperties:function(t,e){Bt(t);for(var r,n=V(e),o=qe(e),i=o.length,u=0;i>u;)Ht.f(t,r=o[u++],n[r]);return t},wr={f:mr},Or=H("document","documentElement"),jr=ee("IE_PROTO"),Sr=function(){},Tr=function(t){return"<script>"+t+"</"+"script>"},Rr=function(t){t.write(Tr("")),t.close();var e=t.parentWindow.Object;return t=null,e},Er=function(){try{sr=new ActiveXObject("htmlfile")}catch(t){}var t,e;Er="undefined"!=typeof document?document.domain&&sr?Rr(sr):((e=kt("iframe")).style.display="none",Or.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(Tr("document.F=Object")),t.close(),t.F):Rr(sr);for(var r=xe.length;r--;)delete Er.prototype[xe[r]];return Er()};re[jr]=!0;var Pr=Object.create||function(t,e){var r;return null!==t?(Sr.prototype=Bt(t),r=new Sr,Sr.prototype=null,r[jr]=t):r=Er(),void 0===e?r:wr.f(r,e)},xr=Tt("unscopables"),Ar=Array.prototype;null==Ar[xr]&&Ht.f(Ar,xr,{configurable:!0,value:Pr(null)});var Cr,kr=gr.find,Ir="find",_r=!0;Ir in[]&&Array(1).find((function(){_r=!1})),$e({target:"Array",proto:!0,forced:_r},{find:function(t){return kr(this,t,arguments.length>1?arguments[1]:void 0)}}),Cr=Ir,Ar[xr][Cr]=!0;var Fr=Je?{}.toString:function(){return"[object "+rr(this)+"]"};Je||be(Object.prototype,"toString",Fr,{unsafe:!0});var Mr=g.String,Dr=function(t){if("Symbol"===rr(t))throw TypeError("Cannot convert a Symbol value to a string");return Mr(t)},zr="\t\n\v\f\r                　\u2028\u2029\ufeff",Br=I("".replace),Lr="["+zr+"]",Nr=RegExp("^"+Lr+Lr+"*"),Vr=RegExp(Lr+Lr+"*$"),Gr=function(t){return function(e){var r=Dr(N(e));return 1&t&&(r=Br(r,Nr,"")),2&t&&(r=Br(r,Vr,"")),r}},$r={start:Gr(1),end:Gr(2),trim:Gr(3)}.trim,qr=g.parseInt,Hr=g.Symbol,Ur=Hr&&Hr.iterator,Wr=/^[+-]?0x/i,Xr=I(Wr.exec),Kr=8!==qr(zr+"08")||22!==qr(zr+"0x16")||Ur&&!m((function(){qr(Object(Ur))}))?function(t,e){var r=$r(Dr(t));return qr(r,e>>>0||(Xr(Wr,r)?16:10))}:qr;$e({global:!0,forced:parseInt!=Kr},{parseInt:Kr});var Qr=Tt("species"),Yr=function(t){return J>=51||!m((function(){var e=[];return(e.constructor={})[Qr]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},Jr=gr.filter;$e({target:"Array",proto:!0,forced:!Yr("filter")},{filter:function(t){return Jr(this,t,arguments.length>1?arguments[1]:void 0)}});var Zr=function(t,e,r){var n=xt(e);n in t?Ht.f(t,n,P(0,r)):t[n]=r},tn=Tt("isConcatSpreadable"),en=9007199254740991,rn="Maximum allowed index exceeded",nn=g.TypeError,on=J>=51||!m((function(){var t=[];return t[tn]=!1,t.concat()[0]!==t})),un=Yr("concat"),cn=function(t){if(!$(t))return!1;var e=t[tn];return void 0!==e?!!e:Qe(t)};$e({target:"Array",proto:!0,forced:!on||!un},{concat:function(t){var e,r,n,o,i,u=dt(this),c=hr(u,0),a=0;for(e=-1,n=arguments.length;e<n;e++)if(cn(i=-1===e?u:arguments[e])){if(a+(o=Se(i))>en)throw nn(rn);for(r=0;r<o;r++,a++)r in i&&Zr(c,a,i[r])}else{if(a>=en)throw nn(rn);Zr(c,a++,i)}return c.length=a,c}}),r.default.akottr.dragtable.prototype._restoreState=function(t){for(var e=0,r=0,n=Object.entries(t);r<n.length;r++){var o=s(n[r],2),i=o[0],u=o[1],c=this.originalTable.el.find('th[data-field="'.concat(i,'"]'));c.length?(this.originalTable.startIndex=c.prevAll().length+1,this.originalTable.endIndex=parseInt(u,10)+1-e,this._bubbleCols()):e++}};var an=function(){Array.prototype.filter||(Array.prototype.filter=function(t){if(null==this)throw new TypeError;var e=Object(this),r=e.length>>>0;if("function"!=typeof t)throw new TypeError;for(var n=[],o=arguments.length>=2?arguments[1]:void 0,i=0;i<r;i++)if(i in e){var u=e[i];t.call(o,u,i,e)&&n.push(u)}return n})};r.default.extend(r.default.fn.bootstrapTable.defaults,{reorderableColumns:!1,maxMovingRows:10,onReorderColumn:function(t){return!1},dragaccept:null}),r.default.extend(r.default.fn.bootstrapTable.Constructor.EVENTS,{"reorder-column.bs.table":"onReorderColumn"}),r.default.fn.bootstrapTable.methods.push("orderColumns"),r.default.BootstrapTable=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&u(t,e)}(p,t);var e,c,f,s=a(p);function p(){return n(this,p),s.apply(this,arguments)}return e=p,c=[{key:"initHeader",value:function(){for(var t,e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];(t=l(i(p.prototype),"initHeader",this)).call.apply(t,[this].concat(r)),this.options.reorderableColumns&&this.makeRowsReorderable()}},{key:"_toggleColumn",value:function(){for(var t,e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];(t=l(i(p.prototype),"_toggleColumn",this)).call.apply(t,[this].concat(r)),this.options.reorderableColumns&&this.makeRowsReorderable()}},{key:"toggleView",value:function(){for(var t,e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];(t=l(i(p.prototype),"toggleView",this)).call.apply(t,[this].concat(r)),this.options.reorderableColumns&&(this.options.cardView||this.makeRowsReorderable())}},{key:"resetView",value:function(){for(var t,e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];(t=l(i(p.prototype),"resetView",this)).call.apply(t,[this].concat(r)),this.options.reorderableColumns&&this.makeRowsReorderable()}},{key:"makeRowsReorderable",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;try{r.default(this.$el).dragtable("destroy")}catch(t){}r.default(this.$el).dragtable({maxMovingRows:this.options.maxMovingRows,dragaccept:this.options.dragaccept,clickDelay:200,dragHandle:".th-inner",restoreState:e||this.columnsSortOrder,beforeStop:function(e){var n={};e.el.find("th").each((function(t,e){n[r.default(e).data("field")]=t})),t.columnsSortOrder=n,t.options.cookie&&t.persistReorderColumnsState(t);var o=[],i=[],u=[],c=[],a=-1,f=[];if(t.$header.find("th:not(.detail)").each((function(t,e){o.push(r.default(e).data("field")),i.push(r.default(e).data("formatter"))})),o.length<t.columns.length){c=t.columns.filter((function(t){return!t.visible}));for(var l=0;l<c.length;l++)o.push(c[l].field),i.push(c[l].formatter)}for(var s=0;s<o.length;s++)-1!==(a=t.fieldsColumnsIndex[o[s]])&&(t.fieldsColumnsIndex[o[s]]=s,t.columns[a].fieldIndex=s,u.push(t.columns[a]));t.columns=u,an(),r.default.each(t.columns,(function(e,r){var n=!1,o=r.field;t.options.columns[0].filter((function(t){return!(!n&&t.field===o&&(f.push(t),n=!0,1))}))})),t.options.columns[0]=f,t.header.fields=o,t.header.formatters=i,t.initHeader(),t.initToolbar(),t.initSearchText(),t.initBody(),t.resetView(),t.trigger("reorder-column",o)}})}},{key:"orderColumns",value:function(t){this.columnsSortOrder=t,this.makeRowsReorderable()}}],c&&o(e.prototype,c),f&&o(e,f),Object.defineProperty(e,"prototype",{writable:!1}),p}(r.default.BootstrapTable)}));
