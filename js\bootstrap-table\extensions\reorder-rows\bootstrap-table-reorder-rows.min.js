/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.20.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var n=e(t);function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,e){return u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},u(t,e)}function a(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function c(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=i(t);if(e){var o=i(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return a(this,n)}}function f(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=i(t)););return t}function s(){return s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var r=f(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},s.apply(this,arguments)}var l="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function p(t,e){return t(e={exports:{}},e.exports),e.exports}var d,y,h=function(t){return t&&t.Math==Math&&t},g=h("object"==typeof globalThis&&globalThis)||h("object"==typeof window&&window)||h("object"==typeof self&&self)||h("object"==typeof l&&l)||function(){return this}()||Function("return this")(),b=function(t){try{return!!t()}catch(t){return!0}},v=!b((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),w=!b((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),m=Function.prototype.call,O=w?m.bind(m):function(){return m.apply(m,arguments)},S={}.propertyIsEnumerable,j=Object.getOwnPropertyDescriptor,x={f:j&&!S.call({1:2},1)?function(t){var e=j(this,t);return!!e&&e.enumerable}:S},D=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},P=Function.prototype,R=P.bind,T=P.call,E=w&&R.bind(T,T),A=w?function(t){return t&&E(t)}:function(t){return t&&function(){return T.apply(t,arguments)}},k=A({}.toString),I=A("".slice),_=function(t){return I(k(t),8,-1)},C=g.Object,F=A("".split),M=b((function(){return!C("z").propertyIsEnumerable(0)}))?function(t){return"String"==_(t)?F(t,""):C(t)}:C,B=g.TypeError,z=function(t){if(null==t)throw B("Can't call method on "+t);return t},L=function(t){return M(z(t))},N=function(t){return"function"==typeof t},G=function(t){return"object"==typeof t?null!==t:N(t)},$=function(t){return N(t)?t:void 0},q=function(t,e){return arguments.length<2?$(g[t]):g[t]&&g[t][e]},H=A({}.isPrototypeOf),U=q("navigator","userAgent")||"",V=g.process,W=g.Deno,K=V&&V.versions||W&&W.version,Q=K&&K.v8;Q&&(y=(d=Q.split("."))[0]>0&&d[0]<4?1:+(d[0]+d[1])),!y&&U&&(!(d=U.match(/Edge\/(\d+)/))||d[1]>=74)&&(d=U.match(/Chrome\/(\d+)/))&&(y=+d[1]);var X=y,Y=!!Object.getOwnPropertySymbols&&!b((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&X&&X<41})),J=Y&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Z=g.Object,tt=J?function(t){return"symbol"==typeof t}:function(t){var e=q("Symbol");return N(e)&&H(e.prototype,Z(t))},et=g.String,nt=g.TypeError,rt=function(t){if(N(t))return t;throw nt(function(t){try{return et(t)}catch(t){return"Object"}}(t)+" is not a function")},ot=g.TypeError,it=Object.defineProperty,ut=function(t,e){try{it(g,t,{value:e,configurable:!0,writable:!0})}catch(n){g[t]=e}return e},at="__core-js_shared__",ct=g[at]||ut(at,{}),ft=p((function(t){(t.exports=function(t,e){return ct[t]||(ct[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.21.1",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})})),st=g.Object,lt=function(t){return st(z(t))},pt=A({}.hasOwnProperty),dt=Object.hasOwn||function(t,e){return pt(lt(t),e)},yt=0,ht=Math.random(),gt=A(1..toString),bt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+gt(++yt+ht,36)},vt=ft("wks"),wt=g.Symbol,mt=wt&&wt.for,Ot=J?wt:wt&&wt.withoutSetter||bt,St=function(t){if(!dt(vt,t)||!Y&&"string"!=typeof vt[t]){var e="Symbol."+t;Y&&dt(wt,t)?vt[t]=wt[t]:vt[t]=J&&mt?mt(e):Ot(e)}return vt[t]},jt=g.TypeError,xt=St("toPrimitive"),Dt=function(t,e){if(!G(t)||tt(t))return t;var n,r,o=null==(n=t[xt])?void 0:rt(n);if(o){if(void 0===e&&(e="default"),r=O(o,t,e),!G(r)||tt(r))return r;throw jt("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var n,r;if("string"===e&&N(n=t.toString)&&!G(r=O(n,t)))return r;if(N(n=t.valueOf)&&!G(r=O(n,t)))return r;if("string"!==e&&N(n=t.toString)&&!G(r=O(n,t)))return r;throw ot("Can't convert object to primitive value")}(t,e)},Pt=function(t){var e=Dt(t,"string");return tt(e)?e:e+""},Rt=g.document,Tt=G(Rt)&&G(Rt.createElement),Et=!v&&!b((function(){return 7!=Object.defineProperty((t="div",Tt?Rt.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),At=Object.getOwnPropertyDescriptor,kt={f:v?At:function(t,e){if(t=L(t),e=Pt(e),Et)try{return At(t,e)}catch(t){}if(dt(t,e))return D(!O(x.f,t,e),t[e])}},It=v&&b((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),_t=g.String,Ct=g.TypeError,Ft=function(t){if(G(t))return t;throw Ct(_t(t)+" is not an object")},Mt=g.TypeError,Bt=Object.defineProperty,zt=Object.getOwnPropertyDescriptor,Lt="enumerable",Nt="configurable",Gt="writable",$t={f:v?It?function(t,e,n){if(Ft(t),e=Pt(e),Ft(n),"function"==typeof t&&"prototype"===e&&"value"in n&&Gt in n&&!n.writable){var r=zt(t,e);r&&r.writable&&(t[e]=n.value,n={configurable:Nt in n?n.configurable:r.configurable,enumerable:Lt in n?n.enumerable:r.enumerable,writable:!1})}return Bt(t,e,n)}:Bt:function(t,e,n){if(Ft(t),e=Pt(e),Ft(n),Et)try{return Bt(t,e,n)}catch(t){}if("get"in n||"set"in n)throw Mt("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},qt=v?function(t,e,n){return $t.f(t,e,D(1,n))}:function(t,e,n){return t[e]=n,t},Ht=A(Function.toString);N(ct.inspectSource)||(ct.inspectSource=function(t){return Ht(t)});var Ut,Vt,Wt,Kt,Qt=ct.inspectSource,Xt=g.WeakMap,Yt=N(Xt)&&/native code/.test(Qt(Xt)),Jt=ft("keys"),Zt={},te="Object already initialized",ee=g.TypeError,ne=g.WeakMap;if(Yt||ct.state){var re=ct.state||(ct.state=new ne),oe=A(re.get),ie=A(re.has),ue=A(re.set);Ut=function(t,e){if(ie(re,t))throw new ee(te);return e.facade=t,ue(re,t,e),e},Vt=function(t){return oe(re,t)||{}},Wt=function(t){return ie(re,t)}}else{var ae=Jt[Kt="state"]||(Jt[Kt]=bt(Kt));Zt[ae]=!0,Ut=function(t,e){if(dt(t,ae))throw new ee(te);return e.facade=t,qt(t,ae,e),e},Vt=function(t){return dt(t,ae)?t[ae]:{}},Wt=function(t){return dt(t,ae)}}var ce={set:Ut,get:Vt,has:Wt,enforce:function(t){return Wt(t)?Vt(t):Ut(t,{})},getterFor:function(t){return function(e){var n;if(!G(e)||(n=Vt(e)).type!==t)throw ee("Incompatible receiver, "+t+" required");return n}}},fe=Function.prototype,se=v&&Object.getOwnPropertyDescriptor,le=dt(fe,"name"),pe={EXISTS:le,PROPER:le&&"something"===function(){}.name,CONFIGURABLE:le&&(!v||v&&se(fe,"name").configurable)},de=p((function(t){var e=pe.CONFIGURABLE,n=ce.get,r=ce.enforce,o=String(String).split("String");(t.exports=function(t,n,i,u){var a,c=!!u&&!!u.unsafe,f=!!u&&!!u.enumerable,s=!!u&&!!u.noTargetGet,l=u&&void 0!==u.name?u.name:n;N(i)&&("Symbol("===String(l).slice(0,7)&&(l="["+String(l).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!dt(i,"name")||e&&i.name!==l)&&qt(i,"name",l),(a=r(i)).source||(a.source=o.join("string"==typeof l?l:""))),t!==g?(c?!s&&t[n]&&(f=!0):delete t[n],f?t[n]=i:qt(t,n,i)):f?t[n]=i:ut(n,i)})(Function.prototype,"toString",(function(){return N(this)&&n(this).source||Qt(this)}))})),ye=Math.ceil,he=Math.floor,ge=function(t){var e=+t;return e!=e||0===e?0:(e>0?he:ye)(e)},be=Math.max,ve=Math.min,we=function(t,e){var n=ge(t);return n<0?be(n+e,0):ve(n,e)},me=Math.min,Oe=function(t){return(e=t.length)>0?me(ge(e),9007199254740991):0;var e},Se=function(t){return function(e,n,r){var o,i=L(e),u=Oe(i),a=we(r,u);if(t&&n!=n){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===n)return t||a||0;return!t&&-1}},je={includes:Se(!0),indexOf:Se(!1)},xe=je.indexOf,De=A([].push),Pe=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),Re=Object.getOwnPropertyNames||function(t){return function(t,e){var n,r=L(t),o=0,i=[];for(n in r)!dt(Zt,n)&&dt(r,n)&&De(i,n);for(;e.length>o;)dt(r,n=e[o++])&&(~xe(i,n)||De(i,n));return i}(t,Pe)},Te={f:Re},Ee={f:Object.getOwnPropertySymbols},Ae=A([].concat),ke=q("Reflect","ownKeys")||function(t){var e=Te.f(Ft(t)),n=Ee.f;return n?Ae(e,n(t)):e},Ie=function(t,e,n){for(var r=ke(e),o=$t.f,i=kt.f,u=0;u<r.length;u++){var a=r[u];dt(t,a)||n&&dt(n,a)||o(t,a,i(e,a))}},_e=/#|\.prototype\./,Ce=function(t,e){var n=Me[Fe(t)];return n==ze||n!=Be&&(N(e)?b(e):!!e)},Fe=Ce.normalize=function(t){return String(t).replace(_e,".").toLowerCase()},Me=Ce.data={},Be=Ce.NATIVE="N",ze=Ce.POLYFILL="P",Le=Ce,Ne=kt.f,Ge=function(t,e){var n,r,o,i,u,a=t.target,c=t.global,f=t.stat;if(n=c?g:f?g[a]||ut(a,{}):(g[a]||{}).prototype)for(r in e){if(i=e[r],o=t.noTargetGet?(u=Ne(n,r))&&u.value:n[r],!Le(c?r:a+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ie(i,o)}(t.sham||o&&o.sham)&&qt(i,"sham",!0),de(n,r,i,t)}},$e=Array.isArray||function(t){return"Array"==_(t)},qe=function(t,e,n){var r=Pt(e);r in t?$t.f(t,r,D(0,n)):t[r]=n},He={};He[St("toStringTag")]="z";var Ue="[object z]"===String(He),Ve=St("toStringTag"),We=g.Object,Ke="Arguments"==_(function(){return arguments}()),Qe=Ue?_:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=We(t),Ve))?n:Ke?_(e):"Object"==(r=_(e))&&N(e.callee)?"Arguments":r},Xe=function(){},Ye=[],Je=q("Reflect","construct"),Ze=/^\s*(?:class|function)\b/,tn=A(Ze.exec),en=!Ze.exec(Xe),nn=function(t){if(!N(t))return!1;try{return Je(Xe,Ye,t),!0}catch(t){return!1}},rn=function(t){if(!N(t))return!1;switch(Qe(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return en||!!tn(Ze,Qt(t))}catch(t){return!0}};rn.sham=!0;var on=!Je||b((function(){var t;return nn(nn.call)||!nn(Object)||!nn((function(){t=!0}))||t}))?rn:nn,un=St("species"),an=g.Array,cn=function(t,e){return new(function(t){var e;return $e(t)&&(e=t.constructor,(on(e)&&(e===an||$e(e.prototype))||G(e)&&null===(e=e[un]))&&(e=void 0)),void 0===e?an:e}(t))(0===e?0:e)},fn=St("species"),sn=function(t){return X>=51||!b((function(){var e=[];return(e.constructor={})[fn]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},ln=St("isConcatSpreadable"),pn=9007199254740991,dn="Maximum allowed index exceeded",yn=g.TypeError,hn=X>=51||!b((function(){var t=[];return t[ln]=!1,t.concat()[0]!==t})),gn=sn("concat"),bn=function(t){if(!G(t))return!1;var e=t[ln];return void 0!==e?!!e:$e(t)};Ge({target:"Array",proto:!0,forced:!hn||!gn},{concat:function(t){var e,n,r,o,i,u=lt(this),a=cn(u,0),c=0;for(e=-1,r=arguments.length;e<r;e++)if(bn(i=-1===e?u:arguments[e])){if(c+(o=Oe(i))>pn)throw yn(dn);for(n=0;n<o;n++,c++)n in i&&qe(a,c,i[n])}else{if(c>=pn)throw yn(dn);qe(a,c++,i)}return a.length=c,a}});var vn,wn,mn=je.indexOf,On=A([].indexOf),Sn=!!On&&1/On([1],1,-0)<0,jn=!!(wn=[]["indexOf"])&&b((function(){wn.call(null,vn||function(){return 1},1)}));Ge({target:"Array",proto:!0,forced:Sn||!jn},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return Sn?On(this,t,e)||0:mn(this,t,e)}});var xn=sn("splice"),Dn=g.TypeError,Pn=Math.max,Rn=Math.min,Tn=9007199254740991,En="Maximum allowed length exceeded";Ge({target:"Array",proto:!0,forced:!xn},{splice:function(t,e){var n,r,o,i,u,a,c=lt(this),f=Oe(c),s=we(t,f),l=arguments.length;if(0===l?n=r=0:1===l?(n=0,r=f-s):(n=l-2,r=Rn(Pn(ge(e),0),f-s)),f+n-r>Tn)throw Dn(En);for(o=cn(c,r),i=0;i<r;i++)(u=s+i)in c&&qe(o,i,c[u]);if(o.length=r,n<r){for(i=s;i<f-r;i++)a=i+n,(u=i+r)in c?c[a]=c[u]:delete c[a];for(i=f;i>f-r+n;i--)delete c[i-1]}else if(n>r)for(i=f-r;i>s;i--)a=i+n-1,(u=i+r-1)in c?c[a]=c[u]:delete c[a];for(i=0;i<n;i++)c[i+s]=arguments[i+2];return c.length=f-r+n,o}});var An=function(t,e){return{id:"customId_".concat(e)}};n.default.extend(n.default.fn.bootstrapTable.defaults,{reorderableRows:!1,onDragStyle:null,onDropStyle:null,onDragClass:"reorder_rows_onDragClass",dragHandle:">tbody>tr>td:not(.bs-checkbox)",useRowAttrFunc:!1,onReorderRowsDrag:function(t){return!1},onReorderRowsDrop:function(t){return!1},onReorderRow:function(t){return!1}}),n.default.extend(n.default.fn.bootstrapTable.Constructor.EVENTS,{"reorder-row.bs.table":"onReorderRow"}),n.default.BootstrapTable=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&u(t,e)}(p,t);var e,a,f,l=c(p);function p(){return r(this,p),l.apply(this,arguments)}return e=p,a=[{key:"init",value:function(){for(var t,e=this,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];if(this.options.reorderableRows){this.options.useRowAttrFunc&&(this.options.rowAttributes=An);var u=this.options.onPostBody;this.options.onPostBody=function(){setTimeout((function(){e.makeRowsReorderable(),u.call(e.options,e.options.data)}),1)},(t=s(i(p.prototype),"init",this)).call.apply(t,[this].concat(r))}else{var a;(a=s(i(p.prototype),"init",this)).call.apply(a,[this].concat(r))}}},{key:"makeRowsReorderable",value:function(){var t=this;this.$el.tableDnD({onDragStyle:this.options.onDragStyle,onDropStyle:this.options.onDropStyle,onDragClass:this.options.onDragClass,onAllowDrop:function(e,n){return t.onAllowDrop(e,n)},onDragStop:function(e,n){return t.onDragStop(e,n)},onDragStart:function(e,n){return t.onDropStart(e,n)},onDrop:function(e,n){return t.onDrop(e,n)},dragHandle:this.options.dragHandle})}},{key:"onDropStart",value:function(t,e){this.$draggingTd=n.default(e).css("cursor","move"),this.draggingIndex=n.default(this.$draggingTd.parent()).data("index"),this.options.onReorderRowsDrag(this.data[this.draggingIndex])}},{key:"onDragStop",value:function(t,e){var r=n.default(e).data("index"),o=this.data[r];this.options.onDragStop(t,o,e)}},{key:"onAllowDrop",value:function(t,e){var r=n.default(e).data("index"),o=n.default(t).data("index"),i=this.data[r],u=this.data[o];return this.options.onAllowDrop(u,i,t,e)}},{key:"onDrop",value:function(t){this.$draggingTd.css("cursor","");for(var e=[],r=0;r<t.tBodies[0].rows.length;r++){var o=n.default(t.tBodies[0].rows[r]);e.push(this.data[o.data("index")]),o.data("index",r)}var i=this.data[this.draggingIndex],u=e.indexOf(this.data[this.draggingIndex]),a=this.data[u],c=this.options.data.indexOf(this.data[u]);this.options.data.splice(this.options.data.indexOf(i),1),this.options.data.splice(c,0,i),this.initSearch(),this.options.onReorderRowsDrop(a),this.trigger("reorder-row",e,i,a)}},{key:"initSearch",value:function(){this.ignoreInitSort=!0,s(i(p.prototype),"initSearch",this).call(this)}},{key:"initSort",value:function(){this.ignoreInitSort?this.ignoreInitSort=!1:s(i(p.prototype),"initSort",this).call(this)}}],a&&o(e.prototype,a),f&&o(e,f),Object.defineProperty(e,"prototype",{writable:!1}),p}(n.default.BootstrapTable)}));
