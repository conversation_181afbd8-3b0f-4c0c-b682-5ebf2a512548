/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.20.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var n=e(t);function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,e){return u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},u(t,e)}function c(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function f(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=i(t);if(e){var o=i(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return c(this,n)}}function a(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=i(t)););return t}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var r=a(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},l.apply(this,arguments)}var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function p(t,e){return t(e={exports:{}},e.exports),e.exports}var y,b,h=function(t){return t&&t.Math==Math&&t},v=h("object"==typeof globalThis&&globalThis)||h("object"==typeof window&&window)||h("object"==typeof self&&self)||h("object"==typeof s&&s)||function(){return this}()||Function("return this")(),d=function(t){try{return!!t()}catch(t){return!0}},g=!d((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),m=!d((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),w=Function.prototype.call,O=m?w.bind(w):function(){return w.apply(w,arguments)},j={}.propertyIsEnumerable,S=Object.getOwnPropertyDescriptor,P={f:S&&!j.call({1:2},1)?function(t){var e=S(this,t);return!!e&&e.enumerable}:j},T=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},E=Function.prototype,x=E.bind,A=E.call,z=m&&x.bind(A,A),R=m?function(t){return t&&z(t)}:function(t){return t&&function(){return A.apply(t,arguments)}},_=R({}.toString),C=R("".slice),F=function(t){return C(_(t),8,-1)},k=v.Object,M=R("".split),I=d((function(){return!k("z").propertyIsEnumerable(0)}))?function(t){return"String"==F(t)?M(t,""):k(t)}:k,B=v.TypeError,D=function(t){if(null==t)throw B("Can't call method on "+t);return t},L=function(t){return I(D(t))},N=function(t){return"function"==typeof t},V=function(t){return"object"==typeof t?null!==t:N(t)},G=function(t){return N(t)?t:void 0},$=function(t,e){return arguments.length<2?G(v[t]):v[t]&&v[t][e]},q=R({}.isPrototypeOf),U=$("navigator","userAgent")||"",W=v.process,K=v.Deno,Q=W&&W.versions||K&&K.version,X=Q&&Q.v8;X&&(b=(y=X.split("."))[0]>0&&y[0]<4?1:+(y[0]+y[1])),!b&&U&&(!(y=U.match(/Edge\/(\d+)/))||y[1]>=74)&&(y=U.match(/Chrome\/(\d+)/))&&(b=+y[1]);var Y=b,H=!!Object.getOwnPropertySymbols&&!d((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&Y&&Y<41})),J=H&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Z=v.Object,tt=J?function(t){return"symbol"==typeof t}:function(t){var e=$("Symbol");return N(e)&&q(e.prototype,Z(t))},et=v.String,nt=v.TypeError,rt=function(t){if(N(t))return t;throw nt(function(t){try{return et(t)}catch(t){return"Object"}}(t)+" is not a function")},ot=v.TypeError,it=Object.defineProperty,ut=function(t,e){try{it(v,t,{value:e,configurable:!0,writable:!0})}catch(n){v[t]=e}return e},ct="__core-js_shared__",ft=v[ct]||ut(ct,{}),at=p((function(t){(t.exports=function(t,e){return ft[t]||(ft[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.21.1",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})})),lt=v.Object,st=function(t){return lt(D(t))},pt=R({}.hasOwnProperty),yt=Object.hasOwn||function(t,e){return pt(st(t),e)},bt=0,ht=Math.random(),vt=R(1..toString),dt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+vt(++bt+ht,36)},gt=at("wks"),mt=v.Symbol,wt=mt&&mt.for,Ot=J?mt:mt&&mt.withoutSetter||dt,jt=function(t){if(!yt(gt,t)||!H&&"string"!=typeof gt[t]){var e="Symbol."+t;H&&yt(mt,t)?gt[t]=mt[t]:gt[t]=J&&wt?wt(e):Ot(e)}return gt[t]},St=v.TypeError,Pt=jt("toPrimitive"),Tt=function(t,e){if(!V(t)||tt(t))return t;var n,r,o=null==(n=t[Pt])?void 0:rt(n);if(o){if(void 0===e&&(e="default"),r=O(o,t,e),!V(r)||tt(r))return r;throw St("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var n,r;if("string"===e&&N(n=t.toString)&&!V(r=O(n,t)))return r;if(N(n=t.valueOf)&&!V(r=O(n,t)))return r;if("string"!==e&&N(n=t.toString)&&!V(r=O(n,t)))return r;throw ot("Can't convert object to primitive value")}(t,e)},Et=function(t){var e=Tt(t,"string");return tt(e)?e:e+""},xt=v.document,At=V(xt)&&V(xt.createElement),zt=!g&&!d((function(){return 7!=Object.defineProperty((t="div",At?xt.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),Rt=Object.getOwnPropertyDescriptor,_t={f:g?Rt:function(t,e){if(t=L(t),e=Et(e),zt)try{return Rt(t,e)}catch(t){}if(yt(t,e))return T(!O(P.f,t,e),t[e])}},Ct=g&&d((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Ft=v.String,kt=v.TypeError,Mt=function(t){if(V(t))return t;throw kt(Ft(t)+" is not an object")},It=v.TypeError,Bt=Object.defineProperty,Dt=Object.getOwnPropertyDescriptor,Lt="enumerable",Nt="configurable",Vt="writable",Gt={f:g?Ct?function(t,e,n){if(Mt(t),e=Et(e),Mt(n),"function"==typeof t&&"prototype"===e&&"value"in n&&Vt in n&&!n.writable){var r=Dt(t,e);r&&r.writable&&(t[e]=n.value,n={configurable:Nt in n?n.configurable:r.configurable,enumerable:Lt in n?n.enumerable:r.enumerable,writable:!1})}return Bt(t,e,n)}:Bt:function(t,e,n){if(Mt(t),e=Et(e),Mt(n),zt)try{return Bt(t,e,n)}catch(t){}if("get"in n||"set"in n)throw It("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},$t=g?function(t,e,n){return Gt.f(t,e,T(1,n))}:function(t,e,n){return t[e]=n,t},qt=R(Function.toString);N(ft.inspectSource)||(ft.inspectSource=function(t){return qt(t)});var Ut,Wt,Kt,Qt,Xt=ft.inspectSource,Yt=v.WeakMap,Ht=N(Yt)&&/native code/.test(Xt(Yt)),Jt=at("keys"),Zt={},te="Object already initialized",ee=v.TypeError,ne=v.WeakMap;if(Ht||ft.state){var re=ft.state||(ft.state=new ne),oe=R(re.get),ie=R(re.has),ue=R(re.set);Ut=function(t,e){if(ie(re,t))throw new ee(te);return e.facade=t,ue(re,t,e),e},Wt=function(t){return oe(re,t)||{}},Kt=function(t){return ie(re,t)}}else{var ce=Jt[Qt="state"]||(Jt[Qt]=dt(Qt));Zt[ce]=!0,Ut=function(t,e){if(yt(t,ce))throw new ee(te);return e.facade=t,$t(t,ce,e),e},Wt=function(t){return yt(t,ce)?t[ce]:{}},Kt=function(t){return yt(t,ce)}}var fe={set:Ut,get:Wt,has:Kt,enforce:function(t){return Kt(t)?Wt(t):Ut(t,{})},getterFor:function(t){return function(e){var n;if(!V(e)||(n=Wt(e)).type!==t)throw ee("Incompatible receiver, "+t+" required");return n}}},ae=Function.prototype,le=g&&Object.getOwnPropertyDescriptor,se=yt(ae,"name"),pe={EXISTS:se,PROPER:se&&"something"===function(){}.name,CONFIGURABLE:se&&(!g||g&&le(ae,"name").configurable)},ye=p((function(t){var e=pe.CONFIGURABLE,n=fe.get,r=fe.enforce,o=String(String).split("String");(t.exports=function(t,n,i,u){var c,f=!!u&&!!u.unsafe,a=!!u&&!!u.enumerable,l=!!u&&!!u.noTargetGet,s=u&&void 0!==u.name?u.name:n;N(i)&&("Symbol("===String(s).slice(0,7)&&(s="["+String(s).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!yt(i,"name")||e&&i.name!==s)&&$t(i,"name",s),(c=r(i)).source||(c.source=o.join("string"==typeof s?s:""))),t!==v?(f?!l&&t[n]&&(a=!0):delete t[n],a?t[n]=i:$t(t,n,i)):a?t[n]=i:ut(n,i)})(Function.prototype,"toString",(function(){return N(this)&&n(this).source||Xt(this)}))})),be=Math.ceil,he=Math.floor,ve=function(t){var e=+t;return e!=e||0===e?0:(e>0?he:be)(e)},de=Math.max,ge=Math.min,me=Math.min,we=function(t){return(e=t.length)>0?me(ve(e),9007199254740991):0;var e},Oe=function(t){return function(e,n,r){var o,i=L(e),u=we(i),c=function(t,e){var n=ve(t);return n<0?de(n+e,0):ge(n,e)}(r,u);if(t&&n!=n){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===n)return t||c||0;return!t&&-1}},je={includes:Oe(!0),indexOf:Oe(!1)}.indexOf,Se=R([].push),Pe=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),Te=Object.getOwnPropertyNames||function(t){return function(t,e){var n,r=L(t),o=0,i=[];for(n in r)!yt(Zt,n)&&yt(r,n)&&Se(i,n);for(;e.length>o;)yt(r,n=e[o++])&&(~je(i,n)||Se(i,n));return i}(t,Pe)},Ee={f:Te},xe={f:Object.getOwnPropertySymbols},Ae=R([].concat),ze=$("Reflect","ownKeys")||function(t){var e=Ee.f(Mt(t)),n=xe.f;return n?Ae(e,n(t)):e},Re=function(t,e,n){for(var r=ze(e),o=Gt.f,i=_t.f,u=0;u<r.length;u++){var c=r[u];yt(t,c)||n&&yt(n,c)||o(t,c,i(e,c))}},_e=/#|\.prototype\./,Ce=function(t,e){var n=ke[Fe(t)];return n==Ie||n!=Me&&(N(e)?d(e):!!e)},Fe=Ce.normalize=function(t){return String(t).replace(_e,".").toLowerCase()},ke=Ce.data={},Me=Ce.NATIVE="N",Ie=Ce.POLYFILL="P",Be=Ce,De=_t.f,Le=Array.isArray||function(t){return"Array"==F(t)},Ne=function(t,e,n){var r=Et(e);r in t?Gt.f(t,r,T(0,n)):t[r]=n},Ve={};Ve[jt("toStringTag")]="z";var Ge="[object z]"===String(Ve),$e=jt("toStringTag"),qe=v.Object,Ue="Arguments"==F(function(){return arguments}()),We=Ge?F:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=qe(t),$e))?n:Ue?F(e):"Object"==(r=F(e))&&N(e.callee)?"Arguments":r},Ke=function(){},Qe=[],Xe=$("Reflect","construct"),Ye=/^\s*(?:class|function)\b/,He=R(Ye.exec),Je=!Ye.exec(Ke),Ze=function(t){if(!N(t))return!1;try{return Xe(Ke,Qe,t),!0}catch(t){return!1}},tn=function(t){if(!N(t))return!1;switch(We(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Je||!!He(Ye,Xt(t))}catch(t){return!0}};tn.sham=!0;var en,nn=!Xe||d((function(){var t;return Ze(Ze.call)||!Ze(Object)||!Ze((function(){t=!0}))||t}))?tn:Ze,rn=jt("species"),on=v.Array,un=function(t,e){return new(function(t){var e;return Le(t)&&(e=t.constructor,(nn(e)&&(e===on||Le(e.prototype))||V(e)&&null===(e=e[rn]))&&(e=void 0)),void 0===e?on:e}(t))(0===e?0:e)},cn=jt("species"),fn=jt("isConcatSpreadable"),an=9007199254740991,ln="Maximum allowed index exceeded",sn=v.TypeError,pn=Y>=51||!d((function(){var t=[];return t[fn]=!1,t.concat()[0]!==t})),yn=(en="concat",Y>=51||!d((function(){var t=[];return(t.constructor={})[cn]=function(){return{foo:1}},1!==t[en](Boolean).foo}))),bn=function(t){if(!V(t))return!1;var e=t[fn];return void 0!==e?!!e:Le(t)};!function(t,e){var n,r,o,i,u,c=t.target,f=t.global,a=t.stat;if(n=f?v:a?v[c]||ut(c,{}):(v[c]||{}).prototype)for(r in e){if(i=e[r],o=t.noTargetGet?(u=De(n,r))&&u.value:n[r],!Be(f?r:c+(a?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Re(i,o)}(t.sham||o&&o.sham)&&$t(i,"sham",!0),ye(n,r,i,t)}}({target:"Array",proto:!0,forced:!pn||!yn},{concat:function(t){var e,n,r,o,i,u=st(this),c=un(u,0),f=0;for(e=-1,r=arguments.length;e<r;e++)if(bn(i=-1===e?u:arguments[e])){if(f+(o=we(i))>an)throw sn(ln);for(n=0;n<o;n++,f++)n in i&&Ne(c,f,i[n])}else{if(f>=an)throw sn(ln);Ne(c,f++,i)}return c.length=f,c}});var hn=function(t){return void 0!==t.$el.data("resizableColumns")},vn=function(t){t.options.resizable&&!t.options.cardView&&!hn(t)&&t.$el.is(":visible")&&t.$el.resizableColumns({store:window.store})},dn=function(t){hn(t)&&t.$el.data("resizableColumns").destroy()},gn=function(t){dn(t),vn(t)};n.default.extend(n.default.fn.bootstrapTable.defaults,{resizable:!1}),n.default.BootstrapTable=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&u(t,e)}(s,t);var e,n,c,a=f(s);function s(){return r(this,s),a.apply(this,arguments)}return e=s,n=[{key:"initBody",value:function(){for(var t,e=this,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];(t=l(i(s.prototype),"initBody",this)).call.apply(t,[this].concat(r)),this.$el.off("column-switch.bs.table page-change.bs.table").on("column-switch.bs.table page-change.bs.table",(function(){gn(e)})),gn(this)}},{key:"toggleView",value:function(){for(var t,e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];(t=l(i(s.prototype),"toggleView",this)).call.apply(t,[this].concat(n)),this.options.resizable&&this.options.cardView&&dn(this)}},{key:"resetView",value:function(){for(var t,e=this,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];(t=l(i(s.prototype),"resetView",this)).call.apply(t,[this].concat(r)),this.options.resizable&&setTimeout((function(){vn(e)}),100)}}],n&&o(e.prototype,n),c&&o(e,c),Object.defineProperty(e,"prototype",{writable:!1}),s}(n.default.BootstrapTable)}));
