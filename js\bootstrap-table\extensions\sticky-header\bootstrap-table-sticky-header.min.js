/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.20.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var n=e(t);function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function c(t,e){return c=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},c(t,e)}function a(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function u(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=i(t);if(e){var o=i(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return a(this,n)}}function f(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=i(t)););return t}function s(){return s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var r=f(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},s.apply(this,arguments)}var l="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function d(t,e){return t(e={exports:{}},e.exports),e.exports}var p,y,h=function(t){return t&&t.Math==Math&&t},b=h("object"==typeof globalThis&&globalThis)||h("object"==typeof window&&window)||h("object"==typeof self&&self)||h("object"==typeof l&&l)||function(){return this}()||Function("return this")(),v=function(t){try{return!!t()}catch(t){return!0}},g=!v((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),m=!v((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),w=Function.prototype.call,O=m?w.bind(w):function(){return w.apply(w,arguments)},k={}.propertyIsEnumerable,j=Object.getOwnPropertyDescriptor,S={f:j&&!k.call({1:2},1)?function(t){var e=j(this,t);return!!e&&e.enumerable}:k},$=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},P=Function.prototype,T=P.bind,C=P.call,E=m&&T.bind(C,C),_=m?function(t){return t&&E(t)}:function(t){return t&&function(){return C.apply(t,arguments)}},x=_({}.toString),H=_("".slice),A=function(t){return H(x(t),8,-1)},R=b.Object,B=_("".split),F=v((function(){return!R("z").propertyIsEnumerable(0)}))?function(t){return"String"==A(t)?B(t,""):R(t)}:R,I=b.TypeError,L=function(t){if(null==t)throw I("Can't call method on "+t);return t},z=function(t){return F(L(t))},M=function(t){return"function"==typeof t},N=function(t){return"object"==typeof t?null!==t:M(t)},D=function(t){return M(t)?t:void 0},q=function(t,e){return arguments.length<2?D(b[t]):b[t]&&b[t][e]},G=_({}.isPrototypeOf),X=q("navigator","userAgent")||"",Y=b.process,W=b.Deno,U=Y&&Y.versions||W&&W.version,V=U&&U.v8;V&&(y=(p=V.split("."))[0]>0&&p[0]<4?1:+(p[0]+p[1])),!y&&X&&(!(p=X.match(/Edge\/(\d+)/))||p[1]>=74)&&(p=X.match(/Chrome\/(\d+)/))&&(y=+p[1]);var K=y,Q=!!Object.getOwnPropertySymbols&&!v((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&K&&K<41})),J=Q&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Z=b.Object,tt=J?function(t){return"symbol"==typeof t}:function(t){var e=q("Symbol");return M(e)&&G(e.prototype,Z(t))},et=b.String,nt=b.TypeError,rt=function(t){if(M(t))return t;throw nt(function(t){try{return et(t)}catch(t){return"Object"}}(t)+" is not a function")},ot=b.TypeError,it=Object.defineProperty,ct=function(t,e){try{it(b,t,{value:e,configurable:!0,writable:!0})}catch(n){b[t]=e}return e},at="__core-js_shared__",ut=b[at]||ct(at,{}),ft=d((function(t){(t.exports=function(t,e){return ut[t]||(ut[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.21.1",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})})),st=b.Object,lt=function(t){return st(L(t))},dt=_({}.hasOwnProperty),pt=Object.hasOwn||function(t,e){return dt(lt(t),e)},yt=0,ht=Math.random(),bt=_(1..toString),vt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+bt(++yt+ht,36)},gt=ft("wks"),mt=b.Symbol,wt=mt&&mt.for,Ot=J?mt:mt&&mt.withoutSetter||vt,kt=function(t){if(!pt(gt,t)||!Q&&"string"!=typeof gt[t]){var e="Symbol."+t;Q&&pt(mt,t)?gt[t]=mt[t]:gt[t]=J&&wt?wt(e):Ot(e)}return gt[t]},jt=b.TypeError,St=kt("toPrimitive"),$t=function(t,e){if(!N(t)||tt(t))return t;var n,r,o=null==(n=t[St])?void 0:rt(n);if(o){if(void 0===e&&(e="default"),r=O(o,t,e),!N(r)||tt(r))return r;throw jt("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var n,r;if("string"===e&&M(n=t.toString)&&!N(r=O(n,t)))return r;if(M(n=t.valueOf)&&!N(r=O(n,t)))return r;if("string"!==e&&M(n=t.toString)&&!N(r=O(n,t)))return r;throw ot("Can't convert object to primitive value")}(t,e)},Pt=function(t){var e=$t(t,"string");return tt(e)?e:e+""},Tt=b.document,Ct=N(Tt)&&N(Tt.createElement),Et=function(t){return Ct?Tt.createElement(t):{}},_t=!g&&!v((function(){return 7!=Object.defineProperty(Et("div"),"a",{get:function(){return 7}}).a})),xt=Object.getOwnPropertyDescriptor,Ht={f:g?xt:function(t,e){if(t=z(t),e=Pt(e),_t)try{return xt(t,e)}catch(t){}if(pt(t,e))return $(!O(S.f,t,e),t[e])}},At=g&&v((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Rt=b.String,Bt=b.TypeError,Ft=function(t){if(N(t))return t;throw Bt(Rt(t)+" is not an object")},It=b.TypeError,Lt=Object.defineProperty,zt=Object.getOwnPropertyDescriptor,Mt="enumerable",Nt="configurable",Dt="writable",qt={f:g?At?function(t,e,n){if(Ft(t),e=Pt(e),Ft(n),"function"==typeof t&&"prototype"===e&&"value"in n&&Dt in n&&!n.writable){var r=zt(t,e);r&&r.writable&&(t[e]=n.value,n={configurable:Nt in n?n.configurable:r.configurable,enumerable:Mt in n?n.enumerable:r.enumerable,writable:!1})}return Lt(t,e,n)}:Lt:function(t,e,n){if(Ft(t),e=Pt(e),Ft(n),_t)try{return Lt(t,e,n)}catch(t){}if("get"in n||"set"in n)throw It("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},Gt=g?function(t,e,n){return qt.f(t,e,$(1,n))}:function(t,e,n){return t[e]=n,t},Xt=_(Function.toString);M(ut.inspectSource)||(ut.inspectSource=function(t){return Xt(t)});var Yt,Wt,Ut,Vt=ut.inspectSource,Kt=b.WeakMap,Qt=M(Kt)&&/native code/.test(Vt(Kt)),Jt=ft("keys"),Zt=function(t){return Jt[t]||(Jt[t]=vt(t))},te={},ee="Object already initialized",ne=b.TypeError,re=b.WeakMap;if(Qt||ut.state){var oe=ut.state||(ut.state=new re),ie=_(oe.get),ce=_(oe.has),ae=_(oe.set);Yt=function(t,e){if(ce(oe,t))throw new ne(ee);return e.facade=t,ae(oe,t,e),e},Wt=function(t){return ie(oe,t)||{}},Ut=function(t){return ce(oe,t)}}else{var ue=Zt("state");te[ue]=!0,Yt=function(t,e){if(pt(t,ue))throw new ne(ee);return e.facade=t,Gt(t,ue,e),e},Wt=function(t){return pt(t,ue)?t[ue]:{}},Ut=function(t){return pt(t,ue)}}var fe={set:Yt,get:Wt,has:Ut,enforce:function(t){return Ut(t)?Wt(t):Yt(t,{})},getterFor:function(t){return function(e){var n;if(!N(e)||(n=Wt(e)).type!==t)throw ne("Incompatible receiver, "+t+" required");return n}}},se=Function.prototype,le=g&&Object.getOwnPropertyDescriptor,de=pt(se,"name"),pe={EXISTS:de,PROPER:de&&"something"===function(){}.name,CONFIGURABLE:de&&(!g||g&&le(se,"name").configurable)},ye=d((function(t){var e=pe.CONFIGURABLE,n=fe.get,r=fe.enforce,o=String(String).split("String");(t.exports=function(t,n,i,c){var a,u=!!c&&!!c.unsafe,f=!!c&&!!c.enumerable,s=!!c&&!!c.noTargetGet,l=c&&void 0!==c.name?c.name:n;M(i)&&("Symbol("===String(l).slice(0,7)&&(l="["+String(l).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!pt(i,"name")||e&&i.name!==l)&&Gt(i,"name",l),(a=r(i)).source||(a.source=o.join("string"==typeof l?l:""))),t!==b?(u?!s&&t[n]&&(f=!0):delete t[n],f?t[n]=i:Gt(t,n,i)):f?t[n]=i:ct(n,i)})(Function.prototype,"toString",(function(){return M(this)&&n(this).source||Vt(this)}))})),he=Math.ceil,be=Math.floor,ve=function(t){var e=+t;return e!=e||0===e?0:(e>0?be:he)(e)},ge=Math.max,me=Math.min,we=Math.min,Oe=function(t){return(e=t.length)>0?we(ve(e),9007199254740991):0;var e},ke=function(t){return function(e,n,r){var o,i=z(e),c=Oe(i),a=function(t,e){var n=ve(t);return n<0?ge(n+e,0):me(n,e)}(r,c);if(t&&n!=n){for(;c>a;)if((o=i[a++])!=o)return!0}else for(;c>a;a++)if((t||a in i)&&i[a]===n)return t||a||0;return!t&&-1}},je={includes:ke(!0),indexOf:ke(!1)}.indexOf,Se=_([].push),$e=function(t,e){var n,r=z(t),o=0,i=[];for(n in r)!pt(te,n)&&pt(r,n)&&Se(i,n);for(;e.length>o;)pt(r,n=e[o++])&&(~je(i,n)||Se(i,n));return i},Pe=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Te=Pe.concat("length","prototype"),Ce={f:Object.getOwnPropertyNames||function(t){return $e(t,Te)}},Ee={f:Object.getOwnPropertySymbols},_e=_([].concat),xe=q("Reflect","ownKeys")||function(t){var e=Ce.f(Ft(t)),n=Ee.f;return n?_e(e,n(t)):e},He=function(t,e,n){for(var r=xe(e),o=qt.f,i=Ht.f,c=0;c<r.length;c++){var a=r[c];pt(t,a)||n&&pt(n,a)||o(t,a,i(e,a))}},Ae=/#|\.prototype\./,Re=function(t,e){var n=Fe[Be(t)];return n==Le||n!=Ie&&(M(e)?v(e):!!e)},Be=Re.normalize=function(t){return String(t).replace(Ae,".").toLowerCase()},Fe=Re.data={},Ie=Re.NATIVE="N",Le=Re.POLYFILL="P",ze=Re,Me=Ht.f,Ne=function(t,e){var n,r,o,i,c,a=t.target,u=t.global,f=t.stat;if(n=u?b:f?b[a]||ct(a,{}):(b[a]||{}).prototype)for(r in e){if(i=e[r],o=t.noTargetGet?(c=Me(n,r))&&c.value:n[r],!ze(u?r:a+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;He(i,o)}(t.sham||o&&o.sham)&&Gt(i,"sham",!0),ye(n,r,i,t)}},De=Array.isArray||function(t){return"Array"==A(t)},qe=function(t,e,n){var r=Pt(e);r in t?qt.f(t,r,$(0,n)):t[r]=n},Ge={};Ge[kt("toStringTag")]="z";var Xe="[object z]"===String(Ge),Ye=kt("toStringTag"),We=b.Object,Ue="Arguments"==A(function(){return arguments}()),Ve=Xe?A:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=We(t),Ye))?n:Ue?A(e):"Object"==(r=A(e))&&M(e.callee)?"Arguments":r},Ke=function(){},Qe=[],Je=q("Reflect","construct"),Ze=/^\s*(?:class|function)\b/,tn=_(Ze.exec),en=!Ze.exec(Ke),nn=function(t){if(!M(t))return!1;try{return Je(Ke,Qe,t),!0}catch(t){return!1}},rn=function(t){if(!M(t))return!1;switch(Ve(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return en||!!tn(Ze,Vt(t))}catch(t){return!0}};rn.sham=!0;var on,cn=!Je||v((function(){var t;return nn(nn.call)||!nn(Object)||!nn((function(){t=!0}))||t}))?rn:nn,an=kt("species"),un=b.Array,fn=function(t,e){return new(function(t){var e;return De(t)&&(e=t.constructor,(cn(e)&&(e===un||De(e.prototype))||N(e)&&null===(e=e[an]))&&(e=void 0)),void 0===e?un:e}(t))(0===e?0:e)},sn=kt("species"),ln=kt("isConcatSpreadable"),dn=9007199254740991,pn="Maximum allowed index exceeded",yn=b.TypeError,hn=K>=51||!v((function(){var t=[];return t[ln]=!1,t.concat()[0]!==t})),bn=(on="concat",K>=51||!v((function(){var t=[];return(t.constructor={})[sn]=function(){return{foo:1}},1!==t[on](Boolean).foo}))),vn=function(t){if(!N(t))return!1;var e=t[ln];return void 0!==e?!!e:De(t)};Ne({target:"Array",proto:!0,forced:!hn||!bn},{concat:function(t){var e,n,r,o,i,c=lt(this),a=fn(c,0),u=0;for(e=-1,r=arguments.length;e<r;e++)if(vn(i=-1===e?c:arguments[e])){if(u+(o=Oe(i))>dn)throw yn(pn);for(n=0;n<o;n++,u++)n in i&&qe(a,u,i[n])}else{if(u>=dn)throw yn(pn);qe(a,u++,i)}return a.length=u,a}});var gn,mn=_(_.bind),wn=_([].push),On=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,c=7==t,a=5==t||i;return function(u,f,s,l){for(var d,p,y=lt(u),h=F(y),b=function(t,e){return rt(t),void 0===e?t:m?mn(t,e):function(){return t.apply(e,arguments)}}(f,s),v=Oe(h),g=0,w=l||fn,O=e?w(u,v):n||c?w(u,0):void 0;v>g;g++)if((a||g in h)&&(p=b(d=h[g],g,y),t))if(e)O[g]=p;else if(p)switch(t){case 3:return!0;case 5:return d;case 6:return g;case 2:wn(O,d)}else switch(t){case 4:return!1;case 7:wn(O,d)}return i?-1:r||o?o:O}},kn={forEach:On(0),map:On(1),filter:On(2),some:On(3),every:On(4),find:On(5),findIndex:On(6),filterReject:On(7)},jn=Object.keys||function(t){return $e(t,Pe)},Sn=g&&!At?Object.defineProperties:function(t,e){Ft(t);for(var n,r=z(e),o=jn(e),i=o.length,c=0;i>c;)qt.f(t,n=o[c++],r[n]);return t},$n={f:Sn},Pn=q("document","documentElement"),Tn=Zt("IE_PROTO"),Cn=function(){},En=function(t){return"<script>"+t+"</"+"script>"},_n=function(t){t.write(En("")),t.close();var e=t.parentWindow.Object;return t=null,e},xn=function(){try{gn=new ActiveXObject("htmlfile")}catch(t){}var t,e;xn="undefined"!=typeof document?document.domain&&gn?_n(gn):((e=Et("iframe")).style.display="none",Pn.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(En("document.F=Object")),t.close(),t.F):_n(gn);for(var n=Pe.length;n--;)delete xn.prototype[Pe[n]];return xn()};te[Tn]=!0;var Hn=Object.create||function(t,e){var n;return null!==t?(Cn.prototype=Ft(t),n=new Cn,Cn.prototype=null,n[Tn]=t):n=xn(),void 0===e?n:$n.f(n,e)},An=kt("unscopables"),Rn=Array.prototype;null==Rn[An]&&qt.f(Rn,An,{configurable:!0,value:Hn(null)});var Bn,Fn=kn.find,In="find",Ln=!0;In in[]&&Array(1).find((function(){Ln=!1})),Ne({target:"Array",proto:!0,forced:Ln},{find:function(t){return Fn(this,t,arguments.length>1?arguments[1]:void 0)}}),Bn=In,Rn[An][Bn]=!0;var zn=Xe?{}.toString:function(){return"[object "+Ve(this)+"]"};Xe||ye(Object.prototype,"toString",zn,{unsafe:!0});var Mn=n.default.fn.bootstrapTable.utils;n.default.extend(n.default.fn.bootstrapTable.defaults,{stickyHeader:!1,stickyHeaderOffsetY:0,stickyHeaderOffsetLeft:0,stickyHeaderOffsetRight:0}),n.default.BootstrapTable=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&c(t,e)}(d,t);var e,a,f,l=u(d);function d(){return r(this,d),l.apply(this,arguments)}return e=d,a=[{key:"initHeader",value:function(){for(var t,e=this,r=arguments.length,o=new Array(r),c=0;c<r;c++)o[c]=arguments[c];if((t=s(i(d.prototype),"initHeader",this)).call.apply(t,[this].concat(o)),this.options.stickyHeader){this.$tableBody.find(".sticky-header-container,.sticky_anchor_begin,.sticky_anchor_end").remove(),this.$el.before('<div class="sticky-header-container"></div>'),this.$el.before('<div class="sticky_anchor_begin"></div>'),this.$el.after('<div class="sticky_anchor_end"></div>'),this.$header.addClass("sticky-header"),this.$stickyContainer=this.$tableBody.find(".sticky-header-container"),this.$stickyBegin=this.$tableBody.find(".sticky_anchor_begin"),this.$stickyEnd=this.$tableBody.find(".sticky_anchor_end"),this.$stickyHeader=this.$header.clone(!0,!0);var a=Mn.getEventName("resize.sticky-header-table",this.$el.attr("id")),u=Mn.getEventName("scroll.sticky-header-table",this.$el.attr("id"));n.default(window).off(a).on(a,(function(){return e.renderStickyHeader()})),n.default(window).off(u).on(u,(function(){return e.renderStickyHeader()})),this.$tableBody.off("scroll").on("scroll",(function(){return e.matchPositionX()}))}}},{key:"onColumnSearch",value:function(t){var e=t.currentTarget,n=t.keyCode;s(i(d.prototype),"onColumnSearch",this).call(this,{currentTarget:e,keyCode:n}),this.renderStickyHeader()}},{key:"resetView",value:function(){for(var t,e=this,r=arguments.length,o=new Array(r),c=0;c<r;c++)o[c]=arguments[c];(t=s(i(d.prototype),"resetView",this)).call.apply(t,[this].concat(o)),n.default(".bootstrap-table.fullscreen").off("scroll").on("scroll",(function(){return e.renderStickyHeader()}))}},{key:"getCaret",value:function(){for(var t,e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];if((t=s(i(d.prototype),"getCaret",this)).call.apply(t,[this].concat(r)),this.$stickyHeader){var c=this.$stickyHeader.find("th");this.$header.find("th").each((function(t,e){c.eq(t).find(".sortable").attr("class",n.default(e).find(".sortable").attr("class"))}))}}},{key:"horizontalScroll",value:function(){var t=this;s(i(d.prototype),"horizontalScroll",this).call(this),this.$tableBody.on("scroll",(function(){return t.matchPositionX()}))}},{key:"renderStickyHeader",value:function(){var t=this,e=this;this.$stickyHeader=this.$header.clone(!0,!0),this.options.filterControl&&n.default(this.$stickyHeader).off("keyup change mouseup").on("keyup change mouse",(function(t){var r=n.default(t.target),o=r.val(),i=r.parents("th").data("field"),c=e.$header.find('th[data-field="'.concat(i,'"]'));if(r.is("input"))c.find("input").val(o);else if(r.is("select")){var a=c.find("select");a.find("option[selected]").removeAttr("selected"),a.find('option[value="'.concat(o,'"]')).attr("selected",!0)}e.triggerSearch()}));var r=n.default(window).scrollTop(),o=this.$stickyBegin.offset().top-this.options.stickyHeaderOffsetY,i=this.$stickyEnd.offset().top-this.options.stickyHeaderOffsetY-this.$header.height();if(r>o&&r<=i){this.$stickyHeader.find("tr").each((function(e,r){n.default(r).find("th").each((function(r,o){n.default(o).css("min-width",t.$header.find("tr:eq(".concat(e,")")).find("th:eq(".concat(r,")")).css("width"))}))})),this.$stickyContainer.show().addClass("fix-sticky fixed-table-container");var c=this.$tableBody[0].getBoundingClientRect(),a="100%",u=this.options.stickyHeaderOffsetLeft,f=this.options.stickyHeaderOffsetRight;u||(u=c.left),f||(a="".concat(c.width,"px")),this.$el.closest(".bootstrap-table").hasClass("fullscreen")&&(u=0,f=0,a="100%"),this.$stickyContainer.css("top","".concat(this.options.stickyHeaderOffsetY,"px")),this.$stickyContainer.css("left","".concat(u,"px")),this.$stickyContainer.css("right","".concat(f,"px")),this.$stickyContainer.css("width","".concat(a)),this.$stickyTable=n.default("<table/>"),this.$stickyTable.addClass(this.options.classes),this.$stickyContainer.html(this.$stickyTable.append(this.$stickyHeader)),this.matchPositionX()}else this.$stickyContainer.removeClass("fix-sticky").hide()}},{key:"matchPositionX",value:function(){this.$stickyContainer.scrollLeft(this.$tableBody.scrollLeft())}}],a&&o(e.prototype,a),f&&o(e,f),Object.defineProperty(e,"prototype",{writable:!1}),d}(n.default.BootstrapTable)}));
