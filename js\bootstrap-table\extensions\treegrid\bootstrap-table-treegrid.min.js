/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.20.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=e(t);function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,e){return u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},u(t,e)}function c(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function a(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=i(t);if(e){var o=i(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return c(this,r)}}function f(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=i(t)););return t}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,r){var n=f(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},l.apply(this,arguments)}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function p(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return s(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,u=!0,c=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return u=t.done,t},e:function(t){c=!0,i=t},f:function(){try{u||null==r.return||r.return()}finally{if(c)throw i}}}}var y="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function d(t,e){return t(e={exports:{}},e.exports),e.exports}var h,v,b=function(t){return t&&t.Math==Math&&t},g=b("object"==typeof globalThis&&globalThis)||b("object"==typeof window&&window)||b("object"==typeof self&&self)||b("object"==typeof y&&y)||function(){return this}()||Function("return this")(),m=function(t){try{return!!t()}catch(t){return!0}},w=!m((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),j=!m((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),O=Function.prototype.call,S=j?O.bind(O):function(){return O.apply(O,arguments)},P={}.propertyIsEnumerable,E=Object.getOwnPropertyDescriptor,T={f:E&&!P.call({1:2},1)?function(t){var e=E(this,t);return!!e&&e.enumerable}:P},A=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},F=Function.prototype,x=F.bind,I=F.call,_=j&&x.bind(I,I),R=j?function(t){return t&&_(t)}:function(t){return t&&function(){return I.apply(t,arguments)}},k=R({}.toString),C=R("".slice),M=function(t){return C(k(t),8,-1)},B=g.Object,z=R("".split),D=m((function(){return!B("z").propertyIsEnumerable(0)}))?function(t){return"String"==M(t)?z(t,""):B(t)}:B,L=g.TypeError,N=function(t){if(null==t)throw L("Can't call method on "+t);return t},G=function(t){return D(N(t))},q=function(t){return"function"==typeof t},U=function(t){return"object"==typeof t?null!==t:q(t)},$=function(t){return q(t)?t:void 0},H=function(t,e){return arguments.length<2?$(g[t]):g[t]&&g[t][e]},W=R({}.isPrototypeOf),K=H("navigator","userAgent")||"",Q=g.process,V=g.Deno,X=Q&&Q.versions||V&&V.version,Y=X&&X.v8;Y&&(v=(h=Y.split("."))[0]>0&&h[0]<4?1:+(h[0]+h[1])),!v&&K&&(!(h=K.match(/Edge\/(\d+)/))||h[1]>=74)&&(h=K.match(/Chrome\/(\d+)/))&&(v=+h[1]);var J=v,Z=!!Object.getOwnPropertySymbols&&!m((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&J&&J<41})),tt=Z&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,et=g.Object,rt=tt?function(t){return"symbol"==typeof t}:function(t){var e=H("Symbol");return q(e)&&W(e.prototype,et(t))},nt=g.String,ot=g.TypeError,it=function(t){if(q(t))return t;throw ot(function(t){try{return nt(t)}catch(t){return"Object"}}(t)+" is not a function")},ut=g.TypeError,ct=Object.defineProperty,at=function(t,e){try{ct(g,t,{value:e,configurable:!0,writable:!0})}catch(r){g[t]=e}return e},ft="__core-js_shared__",lt=g[ft]||at(ft,{}),st=d((function(t){(t.exports=function(t,e){return lt[t]||(lt[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.21.1",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})})),pt=g.Object,yt=function(t){return pt(N(t))},dt=R({}.hasOwnProperty),ht=Object.hasOwn||function(t,e){return dt(yt(t),e)},vt=0,bt=Math.random(),gt=R(1..toString),mt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+gt(++vt+bt,36)},wt=st("wks"),jt=g.Symbol,Ot=jt&&jt.for,St=tt?jt:jt&&jt.withoutSetter||mt,Pt=function(t){if(!ht(wt,t)||!Z&&"string"!=typeof wt[t]){var e="Symbol."+t;Z&&ht(jt,t)?wt[t]=jt[t]:wt[t]=tt&&Ot?Ot(e):St(e)}return wt[t]},Et=g.TypeError,Tt=Pt("toPrimitive"),At=function(t,e){if(!U(t)||rt(t))return t;var r,n,o=null==(r=t[Tt])?void 0:it(r);if(o){if(void 0===e&&(e="default"),n=S(o,t,e),!U(n)||rt(n))return n;throw Et("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var r,n;if("string"===e&&q(r=t.toString)&&!U(n=S(r,t)))return n;if(q(r=t.valueOf)&&!U(n=S(r,t)))return n;if("string"!==e&&q(r=t.toString)&&!U(n=S(r,t)))return n;throw ut("Can't convert object to primitive value")}(t,e)},Ft=function(t){var e=At(t,"string");return rt(e)?e:e+""},xt=g.document,It=U(xt)&&U(xt.createElement),_t=!w&&!m((function(){return 7!=Object.defineProperty((t="div",It?xt.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),Rt=Object.getOwnPropertyDescriptor,kt={f:w?Rt:function(t,e){if(t=G(t),e=Ft(e),_t)try{return Rt(t,e)}catch(t){}if(ht(t,e))return A(!S(T.f,t,e),t[e])}},Ct=w&&m((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Mt=g.String,Bt=g.TypeError,zt=function(t){if(U(t))return t;throw Bt(Mt(t)+" is not an object")},Dt=g.TypeError,Lt=Object.defineProperty,Nt=Object.getOwnPropertyDescriptor,Gt="enumerable",qt="configurable",Ut="writable",$t={f:w?Ct?function(t,e,r){if(zt(t),e=Ft(e),zt(r),"function"==typeof t&&"prototype"===e&&"value"in r&&Ut in r&&!r.writable){var n=Nt(t,e);n&&n.writable&&(t[e]=r.value,r={configurable:qt in r?r.configurable:n.configurable,enumerable:Gt in r?r.enumerable:n.enumerable,writable:!1})}return Lt(t,e,r)}:Lt:function(t,e,r){if(zt(t),e=Ft(e),zt(r),_t)try{return Lt(t,e,r)}catch(t){}if("get"in r||"set"in r)throw Dt("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},Ht=w?function(t,e,r){return $t.f(t,e,A(1,r))}:function(t,e,r){return t[e]=r,t},Wt=R(Function.toString);q(lt.inspectSource)||(lt.inspectSource=function(t){return Wt(t)});var Kt,Qt,Vt,Xt,Yt=lt.inspectSource,Jt=g.WeakMap,Zt=q(Jt)&&/native code/.test(Yt(Jt)),te=st("keys"),ee={},re="Object already initialized",ne=g.TypeError,oe=g.WeakMap;if(Zt||lt.state){var ie=lt.state||(lt.state=new oe),ue=R(ie.get),ce=R(ie.has),ae=R(ie.set);Kt=function(t,e){if(ce(ie,t))throw new ne(re);return e.facade=t,ae(ie,t,e),e},Qt=function(t){return ue(ie,t)||{}},Vt=function(t){return ce(ie,t)}}else{var fe=te[Xt="state"]||(te[Xt]=mt(Xt));ee[fe]=!0,Kt=function(t,e){if(ht(t,fe))throw new ne(re);return e.facade=t,Ht(t,fe,e),e},Qt=function(t){return ht(t,fe)?t[fe]:{}},Vt=function(t){return ht(t,fe)}}var le={set:Kt,get:Qt,has:Vt,enforce:function(t){return Vt(t)?Qt(t):Kt(t,{})},getterFor:function(t){return function(e){var r;if(!U(e)||(r=Qt(e)).type!==t)throw ne("Incompatible receiver, "+t+" required");return r}}},se=Function.prototype,pe=w&&Object.getOwnPropertyDescriptor,ye=ht(se,"name"),de={EXISTS:ye,PROPER:ye&&"something"===function(){}.name,CONFIGURABLE:ye&&(!w||w&&pe(se,"name").configurable)},he=d((function(t){var e=de.CONFIGURABLE,r=le.get,n=le.enforce,o=String(String).split("String");(t.exports=function(t,r,i,u){var c,a=!!u&&!!u.unsafe,f=!!u&&!!u.enumerable,l=!!u&&!!u.noTargetGet,s=u&&void 0!==u.name?u.name:r;q(i)&&("Symbol("===String(s).slice(0,7)&&(s="["+String(s).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!ht(i,"name")||e&&i.name!==s)&&Ht(i,"name",s),(c=n(i)).source||(c.source=o.join("string"==typeof s?s:""))),t!==g?(a?!l&&t[r]&&(f=!0):delete t[r],f?t[r]=i:Ht(t,r,i)):f?t[r]=i:at(r,i)})(Function.prototype,"toString",(function(){return q(this)&&r(this).source||Yt(this)}))})),ve=Math.ceil,be=Math.floor,ge=function(t){var e=+t;return e!=e||0===e?0:(e>0?be:ve)(e)},me=Math.max,we=Math.min,je=Math.min,Oe=function(t){return(e=t.length)>0?je(ge(e),9007199254740991):0;var e},Se=function(t){return function(e,r,n){var o,i=G(e),u=Oe(i),c=function(t,e){var r=ge(t);return r<0?me(r+e,0):we(r,e)}(n,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},Pe={includes:Se(!0),indexOf:Se(!1)}.indexOf,Ee=R([].push),Te=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),Ae=Object.getOwnPropertyNames||function(t){return function(t,e){var r,n=G(t),o=0,i=[];for(r in n)!ht(ee,r)&&ht(n,r)&&Ee(i,r);for(;e.length>o;)ht(n,r=e[o++])&&(~Pe(i,r)||Ee(i,r));return i}(t,Te)},Fe={f:Ae},xe={f:Object.getOwnPropertySymbols},Ie=R([].concat),_e=H("Reflect","ownKeys")||function(t){var e=Fe.f(zt(t)),r=xe.f;return r?Ie(e,r(t)):e},Re=function(t,e,r){for(var n=_e(e),o=$t.f,i=kt.f,u=0;u<n.length;u++){var c=n[u];ht(t,c)||r&&ht(r,c)||o(t,c,i(e,c))}},ke=/#|\.prototype\./,Ce=function(t,e){var r=Be[Me(t)];return r==De||r!=ze&&(q(e)?m(e):!!e)},Me=Ce.normalize=function(t){return String(t).replace(ke,".").toLowerCase()},Be=Ce.data={},ze=Ce.NATIVE="N",De=Ce.POLYFILL="P",Le=Ce,Ne=kt.f,Ge=function(t,e){var r,n,o,i,u,c=t.target,a=t.global,f=t.stat;if(r=a?g:f?g[c]||at(c,{}):(g[c]||{}).prototype)for(n in e){if(i=e[n],o=t.noTargetGet?(u=Ne(r,n))&&u.value:r[n],!Le(a?n:c+(f?".":"#")+n,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Re(i,o)}(t.sham||o&&o.sham)&&Ht(i,"sham",!0),he(r,n,i,t)}},qe=Array.isArray||function(t){return"Array"==M(t)},Ue=function(t,e,r){var n=Ft(e);n in t?$t.f(t,n,A(0,r)):t[n]=r},$e={};$e[Pt("toStringTag")]="z";var He="[object z]"===String($e),We=Pt("toStringTag"),Ke=g.Object,Qe="Arguments"==M(function(){return arguments}()),Ve=He?M:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=Ke(t),We))?r:Qe?M(e):"Object"==(n=M(e))&&q(e.callee)?"Arguments":n},Xe=function(){},Ye=[],Je=H("Reflect","construct"),Ze=/^\s*(?:class|function)\b/,tr=R(Ze.exec),er=!Ze.exec(Xe),rr=function(t){if(!q(t))return!1;try{return Je(Xe,Ye,t),!0}catch(t){return!1}},nr=function(t){if(!q(t))return!1;switch(Ve(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return er||!!tr(Ze,Yt(t))}catch(t){return!0}};nr.sham=!0;var or=!Je||m((function(){var t;return rr(rr.call)||!rr(Object)||!rr((function(){t=!0}))||t}))?nr:rr,ir=Pt("species"),ur=g.Array,cr=function(t,e){return new(function(t){var e;return qe(t)&&(e=t.constructor,(or(e)&&(e===ur||qe(e.prototype))||U(e)&&null===(e=e[ir]))&&(e=void 0)),void 0===e?ur:e}(t))(0===e?0:e)},ar=Pt("species"),fr=function(t){return J>=51||!m((function(){var e=[];return(e.constructor={})[ar]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},lr=Pt("isConcatSpreadable"),sr=9007199254740991,pr="Maximum allowed index exceeded",yr=g.TypeError,dr=J>=51||!m((function(){var t=[];return t[lr]=!1,t.concat()[0]!==t})),hr=fr("concat"),vr=function(t){if(!U(t))return!1;var e=t[lr];return void 0!==e?!!e:qe(t)};Ge({target:"Array",proto:!0,forced:!dr||!hr},{concat:function(t){var e,r,n,o,i,u=yt(this),c=cr(u,0),a=0;for(e=-1,n=arguments.length;e<n;e++)if(vr(i=-1===e?u:arguments[e])){if(a+(o=Oe(i))>sr)throw yr(pr);for(r=0;r<o;r++,a++)r in i&&Ue(c,a,i[r])}else{if(a>=sr)throw yr(pr);Ue(c,a++,i)}return c.length=a,c}});var br=R(R.bind),gr=R([].push),mr=function(t){var e=1==t,r=2==t,n=3==t,o=4==t,i=6==t,u=7==t,c=5==t||i;return function(a,f,l,s){for(var p,y,d=yt(a),h=D(d),v=function(t,e){return it(t),void 0===e?t:j?br(t,e):function(){return t.apply(e,arguments)}}(f,l),b=Oe(h),g=0,m=s||cr,w=e?m(a,b):r||u?m(a,0):void 0;b>g;g++)if((c||g in h)&&(y=v(p=h[g],g,d),t))if(e)w[g]=y;else if(y)switch(t){case 3:return!0;case 5:return p;case 6:return g;case 2:gr(w,p)}else switch(t){case 4:return!1;case 7:gr(w,p)}return i?-1:n||o?o:w}},wr={forEach:mr(0),map:mr(1),filter:mr(2),some:mr(3),every:mr(4),find:mr(5),findIndex:mr(6),filterReject:mr(7)}.filter;Ge({target:"Array",proto:!0,forced:!fr("filter")},{filter:function(t){return wr(this,t,arguments.length>1?arguments[1]:void 0)}});var jr=He?{}.toString:function(){return"[object "+Ve(this)+"]"};He||he(Object.prototype,"toString",jr,{unsafe:!0});var Or,Sr,Pr=R([].join),Er=D!=Object,Tr=(Or=",",!!(Sr=[]["join"])&&m((function(){Sr.call(null,Or||function(){return 1},1)})));Ge({target:"Array",proto:!0,forced:Er||!Tr},{join:function(t){return Pr(G(this),void 0===t?",":t)}});var Ar=g.String,Fr=function(t){if("Symbol"===Ve(t))throw TypeError("Cannot convert a Symbol value to a string");return Ar(t)},xr="\t\n\v\f\r                　\u2028\u2029\ufeff",Ir=R("".replace),_r="["+xr+"]",Rr=RegExp("^"+_r+_r+"*"),kr=RegExp(_r+_r+"*$"),Cr=function(t){return function(e){var r=Fr(N(e));return 1&t&&(r=Ir(r,Rr,"")),2&t&&(r=Ir(r,kr,"")),r}},Mr={start:Cr(1),end:Cr(2),trim:Cr(3)}.trim,Br=g.parseInt,zr=g.Symbol,Dr=zr&&zr.iterator,Lr=/^[+-]?0x/i,Nr=R(Lr.exec),Gr=8!==Br(xr+"08")||22!==Br(xr+"0x16")||Dr&&!m((function(){Br(Object(Dr))}))?function(t,e){var r=Mr(Fr(t));return Br(r,e>>>0||(Nr(Lr,r)?16:10))}:Br;Ge({global:!0,forced:parseInt!=Gr},{parseInt:Gr}),r.default.extend(r.default.fn.bootstrapTable.defaults,{treeEnable:!1,treeShowField:null,idField:"id",parentIdField:"pid",rootParentId:null}),r.default.BootstrapTable=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&u(t,e)}(y,t);var e,c,f,s=a(y);function y(){return n(this,y),s.apply(this,arguments)}return e=y,c=[{key:"init",value:function(){var t;this._rowStyle=this.options.rowStyle;for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];(t=l(i(y.prototype),"init",this)).call.apply(t,[this].concat(r))}},{key:"initHeader",value:function(){for(var t,e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];(t=l(i(y.prototype),"initHeader",this)).call.apply(t,[this].concat(r));var o=this.options.treeShowField;if(o){var u,c=p(this.header.fields);try{for(c.s();!(u=c.n()).done;){var a=u.value;if(o===a){this.treeEnable=!0;break}}}catch(t){c.e(t)}finally{c.f()}}}},{key:"initBody",value:function(){var t;this.treeEnable&&(this.options.virtualScroll=!1);for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];(t=l(i(y.prototype),"initBody",this)).call.apply(t,[this].concat(r))}},{key:"initTr",value:function(t,e,n,o){var u=this,c=n.filter((function(e){return t[u.options.idField]===e[u.options.parentIdField]}));o.append(l(i(y.prototype),"initRow",this).call(this,t,e,n,o));for(var a=c.length-1,f=0;f<=a;f++){var s=c[f],p=r.default.extend(!0,{},t);s._level=p._level+1,s._parent=p,f===a&&(s._last=1),this.options.rowStyle=function(t,e){var r=u._rowStyle(t,e),n=t[u.options.idField]?t[u.options.idField]:0,o=t[u.options.parentIdField]?t[u.options.parentIdField]:0;return r.classes=[r.classes||"","treegrid-".concat(n),"treegrid-parent-".concat(o)].join(" "),r},this.initTr(s,r.default.inArray(s,n),n,o)}}},{key:"initRow",value:function(t,e,r,n){var o=this;if(this.treeEnable){var u=parseInt(t[this.options.parentIdField],10);return!(this.options.rootParentId!==u&&u||(void 0===t._level&&(t._level=0),this.options.rowStyle=function(t,e){var r=o._rowStyle(t,e),n=t[o.options.idField]?t[o.options.idField]:0;return r.classes=[r.classes||"","treegrid-".concat(n)].join(" "),r},this.initTr(t,e,r,n),0))}return l(i(y.prototype),"initRow",this).call(this,t,e,r,n)}},{key:"destroy",value:function(){for(var t,e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];(t=l(i(y.prototype),"destroy",this)).call.apply(t,[this].concat(r)),this.options.rowStyle=this._rowStyle}}],c&&o(e.prototype,c),f&&o(e,f),Object.defineProperty(e,"prototype",{writable:!1}),y}(r.default.BootstrapTable)}));
