/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.20.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,n){return t(n={exports:{}},n.exports),n.exports}var i,u,c=function(t){return t&&t.Math==Math&&t},a=c("object"==typeof globalThis&&globalThis)||c("object"==typeof window&&window)||c("object"==typeof self&&self)||c("object"==typeof e&&e)||function(){return this}()||Function("return this")(),f=function(t){try{return!!t()}catch(t){return!0}},l=!f((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),s=!f((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),p=Function.prototype.call,g=s?p.bind(p):function(){return p.apply(p,arguments)},y={}.propertyIsEnumerable,d=Object.getOwnPropertyDescriptor,h={f:d&&!y.call({1:2},1)?function(t){var n=d(this,t);return!!n&&n.enumerable}:y},m=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},b=Function.prototype,v=b.bind,w=b.call,S=s&&v.bind(w,w),O=s?function(t){return t&&S(t)}:function(t){return t&&function(){return w.apply(t,arguments)}},j=O({}.toString),P=O("".slice),T=function(t){return P(j(t),8,-1)},E=a.Object,x=O("".split),A=f((function(){return!E("z").propertyIsEnumerable(0)}))?function(t){return"String"==T(t)?x(t,""):E(t)}:E,C=a.TypeError,F=function(t){if(null==t)throw C("Can't call method on "+t);return t},R=function(t){return A(F(t))},M=function(t){return"function"==typeof t},I=function(t){return"object"==typeof t?null!==t:M(t)},L=function(t){return M(t)?t:void 0},N=function(t,n){return arguments.length<2?L(a[t]):a[t]&&a[t][n]},k=O({}.isPrototypeOf),z=N("navigator","userAgent")||"",D=a.process,G=a.Deno,H=D&&D.versions||G&&G.version,U=H&&H.v8;U&&(u=(i=U.split("."))[0]>0&&i[0]<4?1:+(i[0]+i[1])),!u&&z&&(!(i=z.match(/Edge\/(\d+)/))||i[1]>=74)&&(i=z.match(/Chrome\/(\d+)/))&&(u=+i[1]);var _=u,q=!!Object.getOwnPropertySymbols&&!f((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&_&&_<41})),B=q&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,W=a.Object,J=B?function(t){return"symbol"==typeof t}:function(t){var n=N("Symbol");return M(n)&&k(n.prototype,W(t))},K=a.String,Q=a.TypeError,V=function(t){if(M(t))return t;throw Q(function(t){try{return K(t)}catch(t){return"Object"}}(t)+" is not a function")},X=a.TypeError,Y=Object.defineProperty,$=function(t,n){try{Y(a,t,{value:n,configurable:!0,writable:!0})}catch(r){a[t]=n}return n},Z="__core-js_shared__",tt=a[Z]||$(Z,{}),nt=o((function(t){(t.exports=function(t,n){return tt[t]||(tt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.21.1",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})})),rt=a.Object,et=function(t){return rt(F(t))},ot=O({}.hasOwnProperty),it=Object.hasOwn||function(t,n){return ot(et(t),n)},ut=0,ct=Math.random(),at=O(1..toString),ft=function(t){return"Symbol("+(void 0===t?"":t)+")_"+at(++ut+ct,36)},lt=nt("wks"),st=a.Symbol,pt=st&&st.for,gt=B?st:st&&st.withoutSetter||ft,yt=function(t){if(!it(lt,t)||!q&&"string"!=typeof lt[t]){var n="Symbol."+t;q&&it(st,t)?lt[t]=st[t]:lt[t]=B&&pt?pt(n):gt(n)}return lt[t]},dt=a.TypeError,ht=yt("toPrimitive"),mt=function(t,n){if(!I(t)||J(t))return t;var r,e,o=null==(r=t[ht])?void 0:V(r);if(o){if(void 0===n&&(n="default"),e=g(o,t,n),!I(e)||J(e))return e;throw dt("Can't convert object to primitive value")}return void 0===n&&(n="number"),function(t,n){var r,e;if("string"===n&&M(r=t.toString)&&!I(e=g(r,t)))return e;if(M(r=t.valueOf)&&!I(e=g(r,t)))return e;if("string"!==n&&M(r=t.toString)&&!I(e=g(r,t)))return e;throw X("Can't convert object to primitive value")}(t,n)},bt=function(t){var n=mt(t,"string");return J(n)?n:n+""},vt=a.document,wt=I(vt)&&I(vt.createElement),St=!l&&!f((function(){return 7!=Object.defineProperty((t="div",wt?vt.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),Ot=Object.getOwnPropertyDescriptor,jt={f:l?Ot:function(t,n){if(t=R(t),n=bt(n),St)try{return Ot(t,n)}catch(t){}if(it(t,n))return m(!g(h.f,t,n),t[n])}},Pt=l&&f((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Tt=a.String,Et=a.TypeError,xt=function(t){if(I(t))return t;throw Et(Tt(t)+" is not an object")},At=a.TypeError,Ct=Object.defineProperty,Ft=Object.getOwnPropertyDescriptor,Rt="enumerable",Mt="configurable",It="writable",Lt={f:l?Pt?function(t,n,r){if(xt(t),n=bt(n),xt(r),"function"==typeof t&&"prototype"===n&&"value"in r&&It in r&&!r.writable){var e=Ft(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Mt in r?r.configurable:e.configurable,enumerable:Rt in r?r.enumerable:e.enumerable,writable:!1})}return Ct(t,n,r)}:Ct:function(t,n,r){if(xt(t),n=bt(n),xt(r),St)try{return Ct(t,n,r)}catch(t){}if("get"in r||"set"in r)throw At("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},Nt=l?function(t,n,r){return Lt.f(t,n,m(1,r))}:function(t,n,r){return t[n]=r,t},kt=O(Function.toString);M(tt.inspectSource)||(tt.inspectSource=function(t){return kt(t)});var zt,Dt,Gt,Ht,Ut=tt.inspectSource,_t=a.WeakMap,qt=M(_t)&&/native code/.test(Ut(_t)),Bt=nt("keys"),Wt={},Jt="Object already initialized",Kt=a.TypeError,Qt=a.WeakMap;if(qt||tt.state){var Vt=tt.state||(tt.state=new Qt),Xt=O(Vt.get),Yt=O(Vt.has),$t=O(Vt.set);zt=function(t,n){if(Yt(Vt,t))throw new Kt(Jt);return n.facade=t,$t(Vt,t,n),n},Dt=function(t){return Xt(Vt,t)||{}},Gt=function(t){return Yt(Vt,t)}}else{var Zt=Bt[Ht="state"]||(Bt[Ht]=ft(Ht));Wt[Zt]=!0,zt=function(t,n){if(it(t,Zt))throw new Kt(Jt);return n.facade=t,Nt(t,Zt,n),n},Dt=function(t){return it(t,Zt)?t[Zt]:{}},Gt=function(t){return it(t,Zt)}}var tn={set:zt,get:Dt,has:Gt,enforce:function(t){return Gt(t)?Dt(t):zt(t,{})},getterFor:function(t){return function(n){var r;if(!I(n)||(r=Dt(n)).type!==t)throw Kt("Incompatible receiver, "+t+" required");return r}}},nn=Function.prototype,rn=l&&Object.getOwnPropertyDescriptor,en=it(nn,"name"),on={EXISTS:en,PROPER:en&&"something"===function(){}.name,CONFIGURABLE:en&&(!l||l&&rn(nn,"name").configurable)},un=o((function(t){var n=on.CONFIGURABLE,r=tn.get,e=tn.enforce,o=String(String).split("String");(t.exports=function(t,r,i,u){var c,f=!!u&&!!u.unsafe,l=!!u&&!!u.enumerable,s=!!u&&!!u.noTargetGet,p=u&&void 0!==u.name?u.name:r;M(i)&&("Symbol("===String(p).slice(0,7)&&(p="["+String(p).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!it(i,"name")||n&&i.name!==p)&&Nt(i,"name",p),(c=e(i)).source||(c.source=o.join("string"==typeof p?p:""))),t!==a?(f?!s&&t[r]&&(l=!0):delete t[r],l?t[r]=i:Nt(t,r,i)):l?t[r]=i:$(r,i)})(Function.prototype,"toString",(function(){return M(this)&&r(this).source||Ut(this)}))})),cn=Math.ceil,an=Math.floor,fn=function(t){var n=+t;return n!=n||0===n?0:(n>0?an:cn)(n)},ln=Math.max,sn=Math.min,pn=Math.min,gn=function(t){return(n=t.length)>0?pn(fn(n),9007199254740991):0;var n},yn=function(t){return function(n,r,e){var o,i=R(n),u=gn(i),c=function(t,n){var r=fn(t);return r<0?ln(r+n,0):sn(r,n)}(e,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},dn={includes:yn(!0),indexOf:yn(!1)}.indexOf,hn=O([].push),mn=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),bn=Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=R(t),o=0,i=[];for(r in e)!it(Wt,r)&&it(e,r)&&hn(i,r);for(;n.length>o;)it(e,r=n[o++])&&(~dn(i,r)||hn(i,r));return i}(t,mn)},vn={f:bn},wn={f:Object.getOwnPropertySymbols},Sn=O([].concat),On=N("Reflect","ownKeys")||function(t){var n=vn.f(xt(t)),r=wn.f;return r?Sn(n,r(t)):n},jn=function(t,n,r){for(var e=On(n),o=Lt.f,i=jt.f,u=0;u<e.length;u++){var c=e[u];it(t,c)||r&&it(r,c)||o(t,c,i(n,c))}},Pn=/#|\.prototype\./,Tn=function(t,n){var r=xn[En(t)];return r==Cn||r!=An&&(M(n)?f(n):!!n)},En=Tn.normalize=function(t){return String(t).replace(Pn,".").toLowerCase()},xn=Tn.data={},An=Tn.NATIVE="N",Cn=Tn.POLYFILL="P",Fn=Tn,Rn=jt.f,Mn=Array.isArray||function(t){return"Array"==T(t)},In=function(t,n,r){var e=bt(n);e in t?Lt.f(t,e,m(0,r)):t[e]=r},Ln={};Ln[yt("toStringTag")]="z";var Nn="[object z]"===String(Ln),kn=yt("toStringTag"),zn=a.Object,Dn="Arguments"==T(function(){return arguments}()),Gn=Nn?T:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=zn(t),kn))?r:Dn?T(n):"Object"==(e=T(n))&&M(n.callee)?"Arguments":e},Hn=function(){},Un=[],_n=N("Reflect","construct"),qn=/^\s*(?:class|function)\b/,Bn=O(qn.exec),Wn=!qn.exec(Hn),Jn=function(t){if(!M(t))return!1;try{return _n(Hn,Un,t),!0}catch(t){return!1}},Kn=function(t){if(!M(t))return!1;switch(Gn(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Wn||!!Bn(qn,Ut(t))}catch(t){return!0}};Kn.sham=!0;var Qn,Vn=!_n||f((function(){var t;return Jn(Jn.call)||!Jn(Object)||!Jn((function(){t=!0}))||t}))?Kn:Jn,Xn=yt("species"),Yn=a.Array,$n=function(t,n){return new(function(t){var n;return Mn(t)&&(n=t.constructor,(Vn(n)&&(n===Yn||Mn(n.prototype))||I(n)&&null===(n=n[Xn]))&&(n=void 0)),void 0===n?Yn:n}(t))(0===n?0:n)},Zn=yt("species"),tr=yt("isConcatSpreadable"),nr=9007199254740991,rr="Maximum allowed index exceeded",er=a.TypeError,or=_>=51||!f((function(){var t=[];return t[tr]=!1,t.concat()[0]!==t})),ir=(Qn="concat",_>=51||!f((function(){var t=[];return(t.constructor={})[Zn]=function(){return{foo:1}},1!==t[Qn](Boolean).foo}))),ur=function(t){if(!I(t))return!1;var n=t[tr];return void 0!==n?!!n:Mn(t)};!function(t,n){var r,e,o,i,u,c=t.target,f=t.global,l=t.stat;if(r=f?a:l?a[c]||$(c,{}):(a[c]||{}).prototype)for(e in n){if(i=n[e],o=t.noTargetGet?(u=Rn(r,e))&&u.value:r[e],!Fn(f?e:c+(l?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;jn(i,o)}(t.sham||o&&o.sham)&&Nt(i,"sham",!0),un(r,e,i,t)}}({target:"Array",proto:!0,forced:!or||!ir},{concat:function(t){var n,r,e,o,i,u=et(this),c=$n(u,0),a=0;for(n=-1,e=arguments.length;n<e;n++)if(ur(i=-1===n?u:arguments[n])){if(a+(o=gn(i))>nr)throw er(rr);for(r=0;r<o;r++,a++)r in i&&In(c,a,i[r])}else{if(a>=nr)throw er(rr);In(c,a++,i)}return c.length=a,c}}),r.default.fn.bootstrapTable.locales["en-US"]=r.default.fn.bootstrapTable.locales.en={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Loading, please wait"},formatRecordsPerPage:function(t){return"".concat(t," rows per page")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Showing ".concat(t," to ").concat(n," of ").concat(r," rows (filtered from ").concat(e," total rows)"):"Showing ".concat(t," to ").concat(n," of ").concat(r," rows")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Search"},formatNoMatches:function(){return"No matching records found"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Refresh"},formatToggle:function(){return"Toggle"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Columns"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["en-US"])}));
