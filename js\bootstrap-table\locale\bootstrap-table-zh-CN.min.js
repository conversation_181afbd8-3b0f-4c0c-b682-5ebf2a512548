/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.20.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,n){return t(n={exports:{}},n.exports),n.exports}var i,u,c=function(t){return t&&t.Math==Math&&t},f=c("object"==typeof globalThis&&globalThis)||c("object"==typeof window&&window)||c("object"==typeof self&&self)||c("object"==typeof e&&e)||function(){return this}()||Function("return this")(),a=function(t){try{return!!t()}catch(t){return!0}},l=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),s=!a((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),p=Function.prototype.call,y=s?p.bind(p):function(){return p.apply(p,arguments)},b={}.propertyIsEnumerable,m=Object.getOwnPropertyDescriptor,g={f:m&&!b.call({1:2},1)?function(t){var n=m(this,t);return!!n&&n.enumerable}:b},h=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},d=Function.prototype,v=d.bind,w=d.call,S=s&&v.bind(w,w),O=s?function(t){return t&&S(t)}:function(t){return t&&function(){return w.apply(t,arguments)}},j=O({}.toString),P=O("".slice),T=function(t){return P(j(t),8,-1)},E=f.Object,x=O("".split),C=a((function(){return!E("z").propertyIsEnumerable(0)}))?function(t){return"String"==T(t)?x(t,""):E(t)}:E,A=f.TypeError,F=function(t){if(null==t)throw A("Can't call method on "+t);return t},R=function(t){return C(F(t))},M=function(t){return"function"==typeof t},z=function(t){return"object"==typeof t?null!==t:M(t)},N=function(t){return M(t)?t:void 0},I=function(t,n){return arguments.length<2?N(f[t]):f[t]&&f[t][n]},L=O({}.isPrototypeOf),k=I("navigator","userAgent")||"",D=f.process,G=f.Deno,_=D&&D.versions||G&&G.version,q=_&&_.v8;q&&(u=(i=q.split("."))[0]>0&&i[0]<4?1:+(i[0]+i[1])),!u&&k&&(!(i=k.match(/Edge\/(\d+)/))||i[1]>=74)&&(i=k.match(/Chrome\/(\d+)/))&&(u=+i[1]);var B=u,U=!!Object.getOwnPropertySymbols&&!a((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&B&&B<41})),W=U&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,H=f.Object,J=W?function(t){return"symbol"==typeof t}:function(t){var n=I("Symbol");return M(n)&&L(n.prototype,H(t))},K=f.String,Q=f.TypeError,V=function(t){if(M(t))return t;throw Q(function(t){try{return K(t)}catch(t){return"Object"}}(t)+" is not a function")},X=f.TypeError,Y=Object.defineProperty,$=function(t,n){try{Y(f,t,{value:n,configurable:!0,writable:!0})}catch(r){f[t]=n}return n},Z="__core-js_shared__",tt=f[Z]||$(Z,{}),nt=o((function(t){(t.exports=function(t,n){return tt[t]||(tt[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.21.1",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})})),rt=f.Object,et=function(t){return rt(F(t))},ot=O({}.hasOwnProperty),it=Object.hasOwn||function(t,n){return ot(et(t),n)},ut=0,ct=Math.random(),ft=O(1..toString),at=function(t){return"Symbol("+(void 0===t?"":t)+")_"+ft(++ut+ct,36)},lt=nt("wks"),st=f.Symbol,pt=st&&st.for,yt=W?st:st&&st.withoutSetter||at,bt=function(t){if(!it(lt,t)||!U&&"string"!=typeof lt[t]){var n="Symbol."+t;U&&it(st,t)?lt[t]=st[t]:lt[t]=W&&pt?pt(n):yt(n)}return lt[t]},mt=f.TypeError,gt=bt("toPrimitive"),ht=function(t,n){if(!z(t)||J(t))return t;var r,e,o=null==(r=t[gt])?void 0:V(r);if(o){if(void 0===n&&(n="default"),e=y(o,t,n),!z(e)||J(e))return e;throw mt("Can't convert object to primitive value")}return void 0===n&&(n="number"),function(t,n){var r,e;if("string"===n&&M(r=t.toString)&&!z(e=y(r,t)))return e;if(M(r=t.valueOf)&&!z(e=y(r,t)))return e;if("string"!==n&&M(r=t.toString)&&!z(e=y(r,t)))return e;throw X("Can't convert object to primitive value")}(t,n)},dt=function(t){var n=ht(t,"string");return J(n)?n:n+""},vt=f.document,wt=z(vt)&&z(vt.createElement),St=!l&&!a((function(){return 7!=Object.defineProperty((t="div",wt?vt.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),Ot=Object.getOwnPropertyDescriptor,jt={f:l?Ot:function(t,n){if(t=R(t),n=dt(n),St)try{return Ot(t,n)}catch(t){}if(it(t,n))return h(!y(g.f,t,n),t[n])}},Pt=l&&a((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Tt=f.String,Et=f.TypeError,xt=function(t){if(z(t))return t;throw Et(Tt(t)+" is not an object")},Ct=f.TypeError,At=Object.defineProperty,Ft=Object.getOwnPropertyDescriptor,Rt="enumerable",Mt="configurable",zt="writable",Nt={f:l?Pt?function(t,n,r){if(xt(t),n=dt(n),xt(r),"function"==typeof t&&"prototype"===n&&"value"in r&&zt in r&&!r.writable){var e=Ft(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Mt in r?r.configurable:e.configurable,enumerable:Rt in r?r.enumerable:e.enumerable,writable:!1})}return At(t,n,r)}:At:function(t,n,r){if(xt(t),n=dt(n),xt(r),St)try{return At(t,n,r)}catch(t){}if("get"in r||"set"in r)throw Ct("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},It=l?function(t,n,r){return Nt.f(t,n,h(1,r))}:function(t,n,r){return t[n]=r,t},Lt=O(Function.toString);M(tt.inspectSource)||(tt.inspectSource=function(t){return Lt(t)});var kt,Dt,Gt,_t,qt=tt.inspectSource,Bt=f.WeakMap,Ut=M(Bt)&&/native code/.test(qt(Bt)),Wt=nt("keys"),Ht={},Jt="Object already initialized",Kt=f.TypeError,Qt=f.WeakMap;if(Ut||tt.state){var Vt=tt.state||(tt.state=new Qt),Xt=O(Vt.get),Yt=O(Vt.has),$t=O(Vt.set);kt=function(t,n){if(Yt(Vt,t))throw new Kt(Jt);return n.facade=t,$t(Vt,t,n),n},Dt=function(t){return Xt(Vt,t)||{}},Gt=function(t){return Yt(Vt,t)}}else{var Zt=Wt[_t="state"]||(Wt[_t]=at(_t));Ht[Zt]=!0,kt=function(t,n){if(it(t,Zt))throw new Kt(Jt);return n.facade=t,It(t,Zt,n),n},Dt=function(t){return it(t,Zt)?t[Zt]:{}},Gt=function(t){return it(t,Zt)}}var tn={set:kt,get:Dt,has:Gt,enforce:function(t){return Gt(t)?Dt(t):kt(t,{})},getterFor:function(t){return function(n){var r;if(!z(n)||(r=Dt(n)).type!==t)throw Kt("Incompatible receiver, "+t+" required");return r}}},nn=Function.prototype,rn=l&&Object.getOwnPropertyDescriptor,en=it(nn,"name"),on={EXISTS:en,PROPER:en&&"something"===function(){}.name,CONFIGURABLE:en&&(!l||l&&rn(nn,"name").configurable)},un=o((function(t){var n=on.CONFIGURABLE,r=tn.get,e=tn.enforce,o=String(String).split("String");(t.exports=function(t,r,i,u){var c,a=!!u&&!!u.unsafe,l=!!u&&!!u.enumerable,s=!!u&&!!u.noTargetGet,p=u&&void 0!==u.name?u.name:r;M(i)&&("Symbol("===String(p).slice(0,7)&&(p="["+String(p).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!it(i,"name")||n&&i.name!==p)&&It(i,"name",p),(c=e(i)).source||(c.source=o.join("string"==typeof p?p:""))),t!==f?(a?!s&&t[r]&&(l=!0):delete t[r],l?t[r]=i:It(t,r,i)):l?t[r]=i:$(r,i)})(Function.prototype,"toString",(function(){return M(this)&&r(this).source||qt(this)}))})),cn=Math.ceil,fn=Math.floor,an=function(t){var n=+t;return n!=n||0===n?0:(n>0?fn:cn)(n)},ln=Math.max,sn=Math.min,pn=Math.min,yn=function(t){return(n=t.length)>0?pn(an(n),9007199254740991):0;var n},bn=function(t){return function(n,r,e){var o,i=R(n),u=yn(i),c=function(t,n){var r=an(t);return r<0?ln(r+n,0):sn(r,n)}(e,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},mn={includes:bn(!0),indexOf:bn(!1)}.indexOf,gn=O([].push),hn=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),dn=Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=R(t),o=0,i=[];for(r in e)!it(Ht,r)&&it(e,r)&&gn(i,r);for(;n.length>o;)it(e,r=n[o++])&&(~mn(i,r)||gn(i,r));return i}(t,hn)},vn={f:dn},wn={f:Object.getOwnPropertySymbols},Sn=O([].concat),On=I("Reflect","ownKeys")||function(t){var n=vn.f(xt(t)),r=wn.f;return r?Sn(n,r(t)):n},jn=function(t,n,r){for(var e=On(n),o=Nt.f,i=jt.f,u=0;u<e.length;u++){var c=e[u];it(t,c)||r&&it(r,c)||o(t,c,i(n,c))}},Pn=/#|\.prototype\./,Tn=function(t,n){var r=xn[En(t)];return r==An||r!=Cn&&(M(n)?a(n):!!n)},En=Tn.normalize=function(t){return String(t).replace(Pn,".").toLowerCase()},xn=Tn.data={},Cn=Tn.NATIVE="N",An=Tn.POLYFILL="P",Fn=Tn,Rn=jt.f,Mn=Array.isArray||function(t){return"Array"==T(t)},zn=function(t,n,r){var e=dt(n);e in t?Nt.f(t,e,h(0,r)):t[e]=r},Nn={};Nn[bt("toStringTag")]="z";var In="[object z]"===String(Nn),Ln=bt("toStringTag"),kn=f.Object,Dn="Arguments"==T(function(){return arguments}()),Gn=In?T:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=kn(t),Ln))?r:Dn?T(n):"Object"==(e=T(n))&&M(n.callee)?"Arguments":e},_n=function(){},qn=[],Bn=I("Reflect","construct"),Un=/^\s*(?:class|function)\b/,Wn=O(Un.exec),Hn=!Un.exec(_n),Jn=function(t){if(!M(t))return!1;try{return Bn(_n,qn,t),!0}catch(t){return!1}},Kn=function(t){if(!M(t))return!1;switch(Gn(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Hn||!!Wn(Un,qt(t))}catch(t){return!0}};Kn.sham=!0;var Qn,Vn=!Bn||a((function(){var t;return Jn(Jn.call)||!Jn(Object)||!Jn((function(){t=!0}))||t}))?Kn:Jn,Xn=bt("species"),Yn=f.Array,$n=function(t,n){return new(function(t){var n;return Mn(t)&&(n=t.constructor,(Vn(n)&&(n===Yn||Mn(n.prototype))||z(n)&&null===(n=n[Xn]))&&(n=void 0)),void 0===n?Yn:n}(t))(0===n?0:n)},Zn=bt("species"),tr=bt("isConcatSpreadable"),nr=9007199254740991,rr="Maximum allowed index exceeded",er=f.TypeError,or=B>=51||!a((function(){var t=[];return t[tr]=!1,t.concat()[0]!==t})),ir=(Qn="concat",B>=51||!a((function(){var t=[];return(t.constructor={})[Zn]=function(){return{foo:1}},1!==t[Qn](Boolean).foo}))),ur=function(t){if(!z(t))return!1;var n=t[tr];return void 0!==n?!!n:Mn(t)};!function(t,n){var r,e,o,i,u,c=t.target,a=t.global,l=t.stat;if(r=a?f:l?f[c]||$(c,{}):(f[c]||{}).prototype)for(e in n){if(i=n[e],o=t.noTargetGet?(u=Rn(r,e))&&u.value:r[e],!Fn(a?e:c+(l?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;jn(i,o)}(t.sham||o&&o.sham)&&It(i,"sham",!0),un(r,e,i,t)}}({target:"Array",proto:!0,forced:!or||!ir},{concat:function(t){var n,r,e,o,i,u=et(this),c=$n(u,0),f=0;for(n=-1,e=arguments.length;n<e;n++)if(ur(i=-1===n?u:arguments[n])){if(f+(o=yn(i))>nr)throw er(rr);for(r=0;r<o;r++,f++)r in i&&zn(c,f,i[r])}else{if(f>=nr)throw er(rr);zn(c,f++,i)}return c.length=f,c}}),r.default.fn.bootstrapTable.locales["zh-CN"]=r.default.fn.bootstrapTable.locales.zh={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"正在努力地加载数据中，请稍候"},formatRecordsPerPage:function(t){return"每页显示 ".concat(t," 条记录")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"显示第 ".concat(t," 到第 ").concat(n," 条记录，总共 ").concat(r," 条记录（从 ").concat(e," 总记录中过滤）"):"显示第 ".concat(t," 到第 ").concat(n," 条记录，总共 ").concat(r," 条记录")},formatSRPaginationPreText:function(){return"上一页"},formatSRPaginationPageText:function(t){return"第".concat(t,"页")},formatSRPaginationNextText:function(){return"下一页"},formatDetailPagination:function(t){return"总共 ".concat(t," 条记录")},formatClearSearch:function(){return"清空过滤"},formatSearch:function(){return"搜索"},formatNoMatches:function(){return"没有找到匹配的记录"},formatPaginationSwitch:function(){return"隐藏/显示分页"},formatPaginationSwitchDown:function(){return"显示分页"},formatPaginationSwitchUp:function(){return"隐藏分页"},formatRefresh:function(){return"刷新"},formatToggle:function(){return"切换"},formatToggleOn:function(){return"显示卡片视图"},formatToggleOff:function(){return"隐藏卡片视图"},formatColumns:function(){return"列"},formatColumnsToggleAll:function(){return"切换所有"},formatFullscreen:function(){return"全屏"},formatAllRows:function(){return"所有"},formatAutoRefresh:function(){return"自动刷新"},formatExport:function(){return"导出数据"},formatJumpTo:function(){return"跳转"},formatAdvancedSearch:function(){return"高级搜索"},formatAdvancedCloseButton:function(){return"关闭"},formatFilterControlSwitch:function(){return"隐藏/显示过滤控制"},formatFilterControlSwitchHide:function(){return"隐藏过滤控制"},formatFilterControlSwitchShow:function(){return"显示过滤控制"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["zh-CN"])}));
