/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.20.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var e=n(t);function r(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}function o(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function i(t){return i=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},i(t)}function u(t,n){return u=Object.setPrototypeOf||function(t,n){return t.__proto__=n,t},u(t,n)}function c(t,n){if(n&&("object"==typeof n||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function a(t){var n=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var e,r=i(t);if(n){var o=i(this).constructor;e=Reflect.construct(r,arguments,o)}else e=r.apply(this,arguments);return c(this,e)}}function f(t,n){for(;!Object.prototype.hasOwnProperty.call(t,n)&&null!==(t=i(t)););return t}function s(){return s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,n,e){var r=f(t,n);if(r){var o=Object.getOwnPropertyDescriptor(r,n);return o.get?o.get.call(arguments.length<3?t:e):o.value}},s.apply(this,arguments)}var l="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function p(t,n){return t(n={exports:{}},n.exports),n.exports}var d,h,y=function(t){return t&&t.Math==Math&&t},b=y("object"==typeof globalThis&&globalThis)||y("object"==typeof window&&window)||y("object"==typeof self&&self)||y("object"==typeof l&&l)||function(){return this}()||Function("return this")(),g=function(t){try{return!!t()}catch(t){return!0}},v=!g((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),m=!g((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),w=Function.prototype.call,O=m?w.bind(w):function(){return w.apply(w,arguments)},j={}.propertyIsEnumerable,S=Object.getOwnPropertyDescriptor,P={f:S&&!j.call({1:2},1)?function(t){var n=S(this,t);return!!n&&n.enumerable}:j},T=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},E=Function.prototype,x=E.bind,A=E.call,R=m&&x.bind(A,A),_=m?function(t){return t&&R(t)}:function(t){return t&&function(){return A.apply(t,arguments)}},k=_({}.toString),C=_("".slice),D=function(t){return C(k(t),8,-1)},F=b.Object,I=_("".split),z=g((function(){return!F("z").propertyIsEnumerable(0)}))?function(t){return"String"==D(t)?I(t,""):F(t)}:F,M=b.TypeError,G=function(t){if(null==t)throw M("Can't call method on "+t);return t},L=function(t){return z(G(t))},N=function(t){return"function"==typeof t},B=function(t){return"object"==typeof t?null!==t:N(t)},W=function(t){return N(t)?t:void 0},$=function(t,n){return arguments.length<2?W(b[t]):b[t]&&b[t][n]},q=_({}.isPrototypeOf),U=$("navigator","userAgent")||"",X=b.process,K=b.Deno,Q=X&&X.versions||K&&K.version,V=Q&&Q.v8;V&&(h=(d=V.split("."))[0]>0&&d[0]<4?1:+(d[0]+d[1])),!h&&U&&(!(d=U.match(/Edge\/(\d+)/))||d[1]>=74)&&(d=U.match(/Chrome\/(\d+)/))&&(h=+d[1]);var Y=h,H=!!Object.getOwnPropertySymbols&&!g((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&Y&&Y<41})),J=H&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Z=b.Object,tt=J?function(t){return"symbol"==typeof t}:function(t){var n=$("Symbol");return N(n)&&q(n.prototype,Z(t))},nt=b.String,et=b.TypeError,rt=function(t){if(N(t))return t;throw et(function(t){try{return nt(t)}catch(t){return"Object"}}(t)+" is not a function")},ot=b.TypeError,it=Object.defineProperty,ut=function(t,n){try{it(b,t,{value:n,configurable:!0,writable:!0})}catch(e){b[t]=n}return n},ct="__core-js_shared__",at=b[ct]||ut(ct,{}),ft=p((function(t){(t.exports=function(t,n){return at[t]||(at[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.21.1",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})})),st=b.Object,lt=function(t){return st(G(t))},pt=_({}.hasOwnProperty),dt=Object.hasOwn||function(t,n){return pt(lt(t),n)},ht=0,yt=Math.random(),bt=_(1..toString),gt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+bt(++ht+yt,36)},vt=ft("wks"),mt=b.Symbol,wt=mt&&mt.for,Ot=J?mt:mt&&mt.withoutSetter||gt,jt=function(t){if(!dt(vt,t)||!H&&"string"!=typeof vt[t]){var n="Symbol."+t;H&&dt(mt,t)?vt[t]=mt[t]:vt[t]=J&&wt?wt(n):Ot(n)}return vt[t]},St=b.TypeError,Pt=jt("toPrimitive"),Tt=function(t,n){if(!B(t)||tt(t))return t;var e,r,o=null==(e=t[Pt])?void 0:rt(e);if(o){if(void 0===n&&(n="default"),r=O(o,t,n),!B(r)||tt(r))return r;throw St("Can't convert object to primitive value")}return void 0===n&&(n="number"),function(t,n){var e,r;if("string"===n&&N(e=t.toString)&&!B(r=O(e,t)))return r;if(N(e=t.valueOf)&&!B(r=O(e,t)))return r;if("string"!==n&&N(e=t.toString)&&!B(r=O(e,t)))return r;throw ot("Can't convert object to primitive value")}(t,n)},Et=function(t){var n=Tt(t,"string");return tt(n)?n:n+""},xt=b.document,At=B(xt)&&B(xt.createElement),Rt=function(t){return At?xt.createElement(t):{}},_t=!v&&!g((function(){return 7!=Object.defineProperty(Rt("div"),"a",{get:function(){return 7}}).a})),kt=Object.getOwnPropertyDescriptor,Ct={f:v?kt:function(t,n){if(t=L(t),n=Et(n),_t)try{return kt(t,n)}catch(t){}if(dt(t,n))return T(!O(P.f,t,n),t[n])}},Dt=v&&g((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Ft=b.String,It=b.TypeError,zt=function(t){if(B(t))return t;throw It(Ft(t)+" is not an object")},Mt=b.TypeError,Gt=Object.defineProperty,Lt=Object.getOwnPropertyDescriptor,Nt="enumerable",Bt="configurable",Wt="writable",$t={f:v?Dt?function(t,n,e){if(zt(t),n=Et(n),zt(e),"function"==typeof t&&"prototype"===n&&"value"in e&&Wt in e&&!e.writable){var r=Lt(t,n);r&&r.writable&&(t[n]=e.value,e={configurable:Bt in e?e.configurable:r.configurable,enumerable:Nt in e?e.enumerable:r.enumerable,writable:!1})}return Gt(t,n,e)}:Gt:function(t,n,e){if(zt(t),n=Et(n),zt(e),_t)try{return Gt(t,n,e)}catch(t){}if("get"in e||"set"in e)throw Mt("Accessors not supported");return"value"in e&&(t[n]=e.value),t}},qt=v?function(t,n,e){return $t.f(t,n,T(1,e))}:function(t,n,e){return t[n]=e,t},Ut=_(Function.toString);N(at.inspectSource)||(at.inspectSource=function(t){return Ut(t)});var Xt,Kt,Qt,Vt=at.inspectSource,Yt=b.WeakMap,Ht=N(Yt)&&/native code/.test(Vt(Yt)),Jt=ft("keys"),Zt=function(t){return Jt[t]||(Jt[t]=gt(t))},tn={},nn="Object already initialized",en=b.TypeError,rn=b.WeakMap;if(Ht||at.state){var on=at.state||(at.state=new rn),un=_(on.get),cn=_(on.has),an=_(on.set);Xt=function(t,n){if(cn(on,t))throw new en(nn);return n.facade=t,an(on,t,n),n},Kt=function(t){return un(on,t)||{}},Qt=function(t){return cn(on,t)}}else{var fn=Zt("state");tn[fn]=!0,Xt=function(t,n){if(dt(t,fn))throw new en(nn);return n.facade=t,qt(t,fn,n),n},Kt=function(t){return dt(t,fn)?t[fn]:{}},Qt=function(t){return dt(t,fn)}}var sn={set:Xt,get:Kt,has:Qt,enforce:function(t){return Qt(t)?Kt(t):Xt(t,{})},getterFor:function(t){return function(n){var e;if(!B(n)||(e=Kt(n)).type!==t)throw en("Incompatible receiver, "+t+" required");return e}}},ln=Function.prototype,pn=v&&Object.getOwnPropertyDescriptor,dn=dt(ln,"name"),hn={EXISTS:dn,PROPER:dn&&"something"===function(){}.name,CONFIGURABLE:dn&&(!v||v&&pn(ln,"name").configurable)},yn=p((function(t){var n=hn.CONFIGURABLE,e=sn.get,r=sn.enforce,o=String(String).split("String");(t.exports=function(t,e,i,u){var c,a=!!u&&!!u.unsafe,f=!!u&&!!u.enumerable,s=!!u&&!!u.noTargetGet,l=u&&void 0!==u.name?u.name:e;N(i)&&("Symbol("===String(l).slice(0,7)&&(l="["+String(l).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!dt(i,"name")||n&&i.name!==l)&&qt(i,"name",l),(c=r(i)).source||(c.source=o.join("string"==typeof l?l:""))),t!==b?(a?!s&&t[e]&&(f=!0):delete t[e],f?t[e]=i:qt(t,e,i)):f?t[e]=i:ut(e,i)})(Function.prototype,"toString",(function(){return N(this)&&e(this).source||Vt(this)}))})),bn=Math.ceil,gn=Math.floor,vn=function(t){var n=+t;return n!=n||0===n?0:(n>0?gn:bn)(n)},mn=Math.max,wn=Math.min,On=Math.min,jn=function(t){return(n=t.length)>0?On(vn(n),9007199254740991):0;var n},Sn=function(t){return function(n,e,r){var o,i=L(n),u=jn(i),c=function(t,n){var e=vn(t);return e<0?mn(e+n,0):wn(e,n)}(r,u);if(t&&e!=e){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===e)return t||c||0;return!t&&-1}},Pn={includes:Sn(!0),indexOf:Sn(!1)},Tn=Pn.indexOf,En=_([].push),xn=function(t,n){var e,r=L(t),o=0,i=[];for(e in r)!dt(tn,e)&&dt(r,e)&&En(i,e);for(;n.length>o;)dt(r,e=n[o++])&&(~Tn(i,e)||En(i,e));return i},An=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Rn=An.concat("length","prototype"),_n={f:Object.getOwnPropertyNames||function(t){return xn(t,Rn)}},kn={f:Object.getOwnPropertySymbols},Cn=_([].concat),Dn=$("Reflect","ownKeys")||function(t){var n=_n.f(zt(t)),e=kn.f;return e?Cn(n,e(t)):n},Fn=function(t,n,e){for(var r=Dn(n),o=$t.f,i=Ct.f,u=0;u<r.length;u++){var c=r[u];dt(t,c)||e&&dt(e,c)||o(t,c,i(n,c))}},In=/#|\.prototype\./,zn=function(t,n){var e=Gn[Mn(t)];return e==Nn||e!=Ln&&(N(n)?g(n):!!n)},Mn=zn.normalize=function(t){return String(t).replace(In,".").toLowerCase()},Gn=zn.data={},Ln=zn.NATIVE="N",Nn=zn.POLYFILL="P",Bn=zn,Wn=Ct.f,$n=function(t,n){var e,r,o,i,u,c=t.target,a=t.global,f=t.stat;if(e=a?b:f?b[c]||ut(c,{}):(b[c]||{}).prototype)for(r in n){if(i=n[r],o=t.noTargetGet?(u=Wn(e,r))&&u.value:e[r],!Bn(a?r:c+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Fn(i,o)}(t.sham||o&&o.sham)&&qt(i,"sham",!0),yn(e,r,i,t)}},qn=_(_.bind),Un=Array.isArray||function(t){return"Array"==D(t)},Xn={};Xn[jt("toStringTag")]="z";var Kn="[object z]"===String(Xn),Qn=jt("toStringTag"),Vn=b.Object,Yn="Arguments"==D(function(){return arguments}()),Hn=Kn?D:function(t){var n,e,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(e=function(t,n){try{return t[n]}catch(t){}}(n=Vn(t),Qn))?e:Yn?D(n):"Object"==(r=D(n))&&N(n.callee)?"Arguments":r},Jn=function(){},Zn=[],te=$("Reflect","construct"),ne=/^\s*(?:class|function)\b/,ee=_(ne.exec),re=!ne.exec(Jn),oe=function(t){if(!N(t))return!1;try{return te(Jn,Zn,t),!0}catch(t){return!1}},ie=function(t){if(!N(t))return!1;switch(Hn(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return re||!!ee(ne,Vt(t))}catch(t){return!0}};ie.sham=!0;var ue,ce=!te||g((function(){var t;return oe(oe.call)||!oe(Object)||!oe((function(){t=!0}))||t}))?ie:oe,ae=jt("species"),fe=b.Array,se=function(t,n){return new(function(t){var n;return Un(t)&&(n=t.constructor,(ce(n)&&(n===fe||Un(n.prototype))||B(n)&&null===(n=n[ae]))&&(n=void 0)),void 0===n?fe:n}(t))(0===n?0:n)},le=_([].push),pe=function(t){var n=1==t,e=2==t,r=3==t,o=4==t,i=6==t,u=7==t,c=5==t||i;return function(a,f,s,l){for(var p,d,h=lt(a),y=z(h),b=function(t,n){return rt(t),void 0===n?t:m?qn(t,n):function(){return t.apply(n,arguments)}}(f,s),g=jn(y),v=0,w=l||se,O=n?w(a,g):e||u?w(a,0):void 0;g>v;v++)if((c||v in y)&&(d=b(p=y[v],v,h),t))if(n)O[v]=d;else if(d)switch(t){case 3:return!0;case 5:return p;case 6:return v;case 2:le(O,p)}else switch(t){case 4:return!1;case 7:le(O,p)}return i?-1:r||o?o:O}},de={forEach:pe(0),map:pe(1),filter:pe(2),some:pe(3),every:pe(4),find:pe(5),findIndex:pe(6),filterReject:pe(7)},he=Object.keys||function(t){return xn(t,An)},ye=v&&!Dt?Object.defineProperties:function(t,n){zt(t);for(var e,r=L(n),o=he(n),i=o.length,u=0;i>u;)$t.f(t,e=o[u++],r[e]);return t},be={f:ye},ge=$("document","documentElement"),ve=Zt("IE_PROTO"),me=function(){},we=function(t){return"<script>"+t+"</"+"script>"},Oe=function(t){t.write(we("")),t.close();var n=t.parentWindow.Object;return t=null,n},je=function(){try{ue=new ActiveXObject("htmlfile")}catch(t){}var t,n;je="undefined"!=typeof document?document.domain&&ue?Oe(ue):((n=Rt("iframe")).style.display="none",ge.appendChild(n),n.src=String("javascript:"),(t=n.contentWindow.document).open(),t.write(we("document.F=Object")),t.close(),t.F):Oe(ue);for(var e=An.length;e--;)delete je.prototype[An[e]];return je()};tn[ve]=!0;var Se=Object.create||function(t,n){var e;return null!==t?(me.prototype=zt(t),e=new me,me.prototype=null,e[ve]=t):e=je(),void 0===n?e:be.f(e,n)},Pe=jt("unscopables"),Te=Array.prototype;null==Te[Pe]&&$t.f(Te,Pe,{configurable:!0,value:Se(null)});var Ee=function(t){Te[Pe][t]=!0},xe=de.find,Ae="find",Re=!0;Ae in[]&&Array(1).find((function(){Re=!1})),$n({target:"Array",proto:!0,forced:Re},{find:function(t){return xe(this,t,arguments.length>1?arguments[1]:void 0)}}),Ee(Ae);var _e=Kn?{}.toString:function(){return"[object "+Hn(this)+"]"};Kn||yn(Object.prototype,"toString",_e,{unsafe:!0});var ke=Pn.includes;$n({target:"Array",proto:!0},{includes:function(t){return ke(this,t,arguments.length>1?arguments[1]:void 0)}}),Ee("includes");var Ce=jt("match"),De=b.TypeError,Fe=function(t){if(function(t){var n;return B(t)&&(void 0!==(n=t[Ce])?!!n:"RegExp"==D(t))}(t))throw De("The method doesn't accept regular expressions");return t},Ie=b.String,ze=function(t){if("Symbol"===Hn(t))throw TypeError("Cannot convert a Symbol value to a string");return Ie(t)},Me=jt("match"),Ge=_("".indexOf);$n({target:"String",proto:!0,forced:!function(t){var n=/./;try{"/./"[t](n)}catch(e){try{return n[Me]=!1,"/./"[t](n)}catch(t){}}return!1}("includes")},{includes:function(t){return!!~Ge(ze(G(this)),ze(Fe(t)),arguments.length>1?arguments[1]:void 0)}}),e.default.extend(e.default.fn.bootstrapTable.defaults,{classes:"table highlight",buttonsPrefix:"",buttonsClass:"waves-effect waves-light btn"}),e.default.fn.bootstrapTable.theme="materialize",e.default.BootstrapTable=function(t){!function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),n&&u(t,n)}(p,t);var n,c,f,l=a(p);function p(){return r(this,p),l.apply(this,arguments)}return n=p,c=[{key:"initConstants",value:function(){s(i(p.prototype),"initConstants",this).call(this),this.constants.classes.buttonsGroup="button-group",this.constants.classes.buttonsDropdown="",this.constants.classes.input="input-field",this.constants.classes.input="",this.constants.classes.paginationDropdown="",this.constants.classes.buttonActive="green",this.constants.html.toolbarDropdown=['<ul class="dropdown-content">',"</ul>"],this.constants.html.toolbarDropdownItem='<li class="dropdown-item-marker"><label>%s</label></li>',this.constants.html.toolbarDropdownSeparator='<li class="divider" tabindex="-1"></li>',this.constants.html.pageDropdown=['<ul id="pagination-list-id" class="dropdown-content">',"</ul>"],this.constants.html.pageDropdownItem='<li><a class="%s" href="#">%s</a></li>',this.constants.html.dropdownCaret='<i class="material-icons">arrow_drop_down</i>',this.constants.html.pagination=['<ul class="pagination%s">',"</ul>"],this.constants.html.paginationItem='<li class="waves-effect page-item%s" aria-label="%s"><a href="#">%s</a></li>',this.constants.html.icon='<i class="%s">%s</i>',this.constants.html.inputGroup="%s%s"}},{key:"initToolbar",value:function(){s(i(p.prototype),"initToolbar",this).call(this),this.handleToolbar()}},{key:"handleToolbar",value:function(){this.$toolbar.find(".dropdown-toggle").length&&this.$toolbar.find(".dropdown-toggle").each((function(t,n){if(e.default(n).next().length){var r="toolbar-columns-id".concat(t);e.default(n).next().attr("id",r),e.default(n).attr("data-target",r).dropdown({alignment:"right",constrainWidth:!1,closeOnClick:!1})}}))}},{key:"initPagination",value:function(){s(i(p.prototype),"initPagination",this).call(this),this.options.pagination&&this.paginationParts.includes("pageSize")&&this.$pagination.find(".dropdown-toggle").attr("data-target",this.$pagination.find(".dropdown-content").attr("id")).dropdown()}}],c&&o(n.prototype,c),f&&o(n,f),Object.defineProperty(n,"prototype",{writable:!1}),p}(e.default.BootstrapTable)}));
